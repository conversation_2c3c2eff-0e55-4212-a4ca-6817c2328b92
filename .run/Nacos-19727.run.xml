<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Nacos-19727" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ENABLE_JMX_AGENT" value="false" />
    <module name="sptalent<PERSON><PERSON>" />
    <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.yxt.talent.rv.Application" />
    <option name="VM_PARAMETERS" value="-Dnacos.url=10.130.4.77:8848 -Dnacos.config.url=10.130.4.77:8848 -Dspring.cloud.nacos.discovery.enabled=false -Dspring.cloud.nacos.config.enabled=true -Dnacos.rw.username=readwrite -Dnacos.rw.password=readwrite -Dnacos.ro.username=read -Dnacos.ro.password=read -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=25000 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -Xmx1000m -Xms500m -Xss512k -XX:MetaspaceSize=500m -XX:MaxMetaspaceSize=500m -XX:ReservedCodeCacheSize=500m -Dfile.encoding=UTF-8 --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/jdk.internal.access=ALL-UNNAMED --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>