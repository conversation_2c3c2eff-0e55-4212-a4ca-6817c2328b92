{
  "version": "0.2.0",
  "configurations": [
      {
          "type": "java",
          "name": "sptalentrvapi-19727",
          "request": "launch",
          "mainClass": "com.yxt.talent.rv.Application",
          "projectName": "sptalentrvapi",
          "args": "",
          "env": {
              "SPRING_PROFILES_ACTIVE": "native",
              "WAF_LOG_BASE_DIR": "${workspaceFolder}/data/logback",
          },
          "vmArgs": [
              "-Dspring.profiles.active=native",
              "-Dspring.jmx.enabled=false",
              "-Dcom.sun.management.jmxremote",
              "-Dcom.sun.management.jmxremote.port=49010",
              "-Dcom.sun.management.jmxremote.rmi.port=49010",
              "-Dcom.sun.management.jmxremote.local.only=false",
              "-Dcom.sun.management.jmxremote.authenticate=false",
              "-Dcom.sun.management.jmxremote.ssl=false",
              "-Dnacos.url=10.130.4.77:8848",
              "-Dnacos.config.url=10.130.4.77:8848",
              "-Dspring.cloud.nacos.discovery.enabled=false",
              "-Dspring.cloud.nacos.config.enabled=false",
              "-Dnacos.rw.username=readwrite",
              "-Dnacos.rw.password=readwrite",
              "-Dnacos.ro.username=read",
              "-Dnacos.ro.password=read",
              "-Xmx1000m",
              "-Xms500m",
              "-Xss512k",
              "-XX:MetaspaceSize=500m",
              "-XX:MaxMetaspaceSize=500m",
              "-XX:ReservedCodeCacheSize=500m",
              "-Dfile.encoding=UTF-8",
              "--add-opens=java.base/java.lang=ALL-UNNAMED",
              "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
              "--add-opens=java.base/java.io=ALL-UNNAMED",
              "--add-opens=java.base/java.math=ALL-UNNAMED",
              "--add-opens=java.base/java.net=ALL-UNNAMED",
              "--add-opens=java.base/java.nio=ALL-UNNAMED",
              "--add-opens=java.base/java.security=ALL-UNNAMED",
              "--add-opens=java.base/java.text=ALL-UNNAMED",
              "--add-opens=java.base/java.time=ALL-UNNAMED",
              "--add-opens=java.base/java.util=ALL-UNNAMED",
              "--add-opens=java.base/jdk.internal.access=ALL-UNNAMED",
              "--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED",
              "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
              "--add-opens=java.base/sun.reflect.annotation=ALL-UNNAMED"
          ],
          "console": "internalConsole",
          "stopOnEntry": false,
          "cwd": "${workspaceFolder}"
      }
  ]
}