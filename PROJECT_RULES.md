# AOM项目开发规范与架构指南

## 项目概述

### 基本信息
- **项目名称**: sptalentrva<PERSON> (奇点-人才盘点项目)
- **项目版本**: 6.1.2-jdk17
- **组织**: com.yxt
- **描述**: 基于AOM(Activity Object Management)架构的人才盘点与评估系统

### 核心业务领域
- **人才盘点(XPD)**: 员工能力评估、发展规划
- **AOM活动管理**: 项目/活动的全生命周期管理
- **用户与部门管理**: 基于UDP的用户中心集成

## 技术架构

### 技术栈
- **Java版本**: JDK 17
- **框架**: Spring Boot 3.x (基于usdk-bom-waf 1.2.7-jdk17)
- **数据库**: MySQL 8.0.33 + MyBatis-Plus + Druid连接池
- **缓存**: Redis (支持UCache统一缓存框架)
- **消息队列**: RocketMQ (usdk-components-rocketmq)
- **微服务**: Spring Cloud Alibaba + Nacos (服务发现与配置中心)
- **搜索引擎**: Elasticsearch 6.x
- **任务调度**: XXL-Job
- **对象映射**: MapStruct 1.5.5.Final
- **文档**: Knife4j (已排除，使用自定义文档方案)

### 架构模式
- **MVC分层架构**: 采用MVC分层架构
  - `controller`: 控制层，处理HTTP请求
  - `application`: 应用服务层，业务流程编排
  - `infrastructure`: 基础设施层，数据持久化与外部服务

### 数据库设计原则
- **分库分表**: 支持多数据源配置(AomDbConfig)
- **主键策略**: 统一使用UUID(部分表使用的雪花算法,具体问题具体分析)
- **多租户**: 所有表必须包含org_id字段进行数据隔离

## AOM核心概念与数据模型

### 核心实体关系
```
rv_activity (AOM项目表)
    ↓
rv_activity_arrange_item (编排活动表)
    ↓  
rv_base_activity_result (员工活动结果表)
    ↓
rv_activity_objective_result (员工指标结果表)
```

### 关键实体说明
- **Activity**: 活动/项目主表，区分活动(actv_type=1)和项目(actv_type=2)
- **ActivityArrange**: 活动编排表，管理活动的生命周期状态
- **ActivityArrangeItem**: 活动编排项，定义具体的任务节点
- **User**: 用户领域对象，基于UDP用户中心
- **Dept**: 部门领域对象，支持层级结构

### 状态管理
- **活动状态**: 0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回
- **编排状态**: 0-未发布(草稿), 1-已发布, 2-撤回, 3-结束, 4-归档, 5-删除

## 编码规范

### 代码结构规范
1. **类大小限制**: 每个类不超过300行代码
2. **方法大小限制**: 每个方法不超过30行代码
3. **包命名**: 统一使用com.yxt.talent.rv作为根包

### MyBatis-Plus使用规范
- **严格限制**: 只允许使用BaseMapper提供的基础CRUD方法
- **禁止使用**: QueryWrapper、LambdaQueryWrapper等动态查询构建器
- **复杂查询**: 必须使用传统MyBatis XML方式实现

### 数据库操作规范
- **查询限制**: 单次查询结果不超过1000条，超过必须分页
- **必带条件**: 所有查询必须包含org_id条件
- **事务控制**: 单个事务影响行数不超过5000行
- **索引要求**: WHERE条件必须命中索引
- **排序限制**: 排序字段不超过3个，且不能跨表

### 缓存使用规范
- **强制过期时间**: 所有缓存Key必须设置过期时间，最长30天
- **大Key限制**: 禁止存储超过10K的string类型数据
- **高危指令**: 禁止使用keys、scan、flushall等高危命令
- **推荐工具**: 优先使用UCache替代RedisTemplate

### 安全与性能规范
- **参数校验**: 所有用户输入必须进行有效性验证
- **权限控制**: 用户相关功能必须进行权限校验
- **防重放**: 关键操作必须实现防重放机制
- **异步处理**: 耗时操作优先考虑异步处理
- **熔断降级**: 外部调用必须实现熔断和降级机制

## 国际化支持

### 配置
- **消息资源**: spring.messages.basename=sptalentrv-messages,talent-messages,aom-messages
- **回退机制**: ikit.i18n.bundle.fallback-basename=message,i18n
- **支持语言**: 中文(zh_CN)、英文(en)、繁体中文(zh_TW)

## 部署与运维

### 应用配置
- **服务名**: sptalentrvapi
- **端口**: 19727
- **配置中心**: Nacos
- **服务发现**: Nacos Discovery

### 监控与日志
- **健康检查**: Spring Boot Actuator
- **日志配置**: logback-native.xml
- **链路追踪**: waf-trace 2.4.2-jdk17

### 数据库迁移
- **工具**: Liquibase 4.26.0
- **变更日志**: src/main/resources/db/changelog/db.changelog-master.yaml

## 开发工具配置

### 代码格式化
- **缩进**: 4个空格
- **行长度**: 120字符
- **编码**: UTF-8
- **换行符**: LF

### 注解处理器
- Lombok 1.18.24
- MapStruct 1.5.5.Final
- Spring Boot Configuration Processor

## 依赖管理

### 核心依赖
- **AOM模块**: aom-project, aom-activity, aom-migr
- **人才模块**: sptalentsdapi-facade, sptalentrv-facade
- **基础框架**: sp-sdk, ddd-spring-boot-starter
- **工具库**: ubiz-export, ubiz-tree, ikit-i18n

### 版本管理
- 使用Maven versions插件进行版本管理
- 统一版本号定义在properties中
- 排除冲突依赖，避免版本冲突

## 注意事项

### 硬性规定
1. 始终使用中文进行交流与文档编写
2. 方法返回null值时必须使用@jakarta.annotation.Nullable注解标识
3. 严格遵循MyBatis-Plus使用限制
4. 所有数据库操作必须包含org_id条件
5. 代码行数限制严格执行

### 最佳实践
1. 优先使用缓存提升性能
2. 合理使用异步处理提升响应速度
3. 实现完善的异常处理和日志记录
4. 定期进行代码重构，保持代码质量