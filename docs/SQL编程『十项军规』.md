# SQL编程『十项军规』

1.  禁止使用『select *』查询所有字段，必须根据业务需要只查询所需字段；
    
2.  关联查询联表数量不能超过三张【报表除外】，且关联条件要充分，数据类型必须一致，关联字段必须命中索引；
    
3.  单表查询条件必须带org_id字段，多表关联查询条件主表必须带org_id字段，子查询条件必须带org_id字段；
    
4.  单次查询返回结果不得超过1000条，如果数据总量超过1000条，必须分页返回；
    
5.  禁止无条件的查询、修改、删除，where条件（包括子查询where条件）必须充分且命中索引，like查询必须左匹配，否则使用ES查询；
    
6.  禁止大事务操作（包括批量UPDATE/DELETE操作），单个事务影响行数禁止超过5000行；如果有超过5000条数据的删改操作，则分批顺序处理，禁止并发执行；如果有大事务需求，需改成异步消息队列进行保障；
    
7.  非必要不排序，排序字段不能超过3个，且不能跨表；排序字段建议命中索引；
    
8.  非必须强制要求数据一致性的查询场景，必须查询从库（待前端改造完确认框后统一执行）；禁止滥用FORCE MASTER，禁止在mapper代码中直接增加FORCE MASTER；每条查询主库的SELECT语句需要提交到后端技术委员会（后端FT）审核；
    
9.  SQL语法必须兼容主流数据库，禁止使用自定义的函数、过程及变量（遵循SQL99标准）；
    
10.  主键类型必须雪花ID（ bigint(20) unsigned 类型），不允许使用UUID和自增ID；varchar类型长度禁止超过10k；主表中不可创建text、longtext、blob、json等长文本类型字段，如果有需要，单独建表以保持长文本信息
    
11.  保证数据库和缓存数据一致性：
    
    1.  强一致性的数据，更新数据时先更新/删除缓存，后更新数据库；
        
    2.  高并发读的场景，更新数据时先更新缓存，后准实时更新数据库；（高并发绝对避免流量打到数据库）
        
    3.  弱一致性、最终一致性数据，可以先更新数据库，后异步更新缓存；