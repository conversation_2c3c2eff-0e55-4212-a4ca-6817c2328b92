# 认证项目证书到期提醒功能实现文档

## 功能概述

本功能实现了认证项目中证书到期的分角色提醒机制，支持员工和管理者两种不同的消息模板，能够根据配置的提醒时间和角色自动发送到期提醒消息。

## 核心功能

### 1. 分角色消息模板

#### 员工消息模板
- **模板代码**: `certificate_expire_employee`
- **推送对象**: 证书持有员工
- **消息标题**: 您获得的认证证书即将到期
- **消息内容**: 您获得的认证证书将于{{days}}天后到期，请关注\n项目名称：{{projectName}}\n证书名称：{{certificateName}}\n到期时间：{{expireTime}}\n点击查看详情，非常感谢您的支持！

#### 管理者消息模板
- **模板代码**: `certificate_expire_manager`
- **推送对象**: 负责人、部门经理、直属经理
- **消息标题**: 您管辖员工的认证证书即将到期
- **消息内容**: 您管辖员工的认证证书将于{{days}}天后到期，请关注\n涉及员工人数：{{userCnt}}\n证书名称：{{certificateName}}\n到期时间：{{expireTime}}\n点击查看详情，非常感谢您的支持！

### 2. 支持的提醒角色

- **0**: 员工 - 证书持有者本人
- **1**: 部门经理 - 员工所在部门的经理
- **2**: 直属经理 - 员工的直接上级
- **3**: 项目负责人 - 认证项目的创建者

### 3. 消息发送逻辑

#### 员工消息
- 每个员工单独发送一条消息
- 包含个人证书的详细信息
- 使用员工专用消息模板

#### 管理者消息
- 按管理者分组发送汇总消息
- 统计该管理者管辖的即将到期证书员工数量
- 使用管理者专用消息模板

## 技术实现

### 1. 核心类和方法

#### AuthprjCertAppService
- `processCertRemindTask()`: 主要的任务处理方法
- `sendRemindMessages()`: 分角色发送提醒消息
- `sendEmployeeRemindMessages()`: 发送员工提醒消息
- `sendManagerRemindMessages()`: 发送管理者提醒消息
- `getCertificateName()`: 获取证书名称
- `getManagerUserMap()`: 获取管理者与用户的映射关系

#### AuthPrjCertRemindJob
- 定时任务入口，每天上午8点执行
- 使用事件驱动架构处理任务

#### AuthprjEventListener
- 处理证书提醒任务事件
- 调用业务逻辑进行实际处理

### 2. 数据库查询

#### AuthprjCertRemindMapper
- `selectAllActiveRemindConfigs()`: 查询所有活跃的证书提醒配置
- 支持跨表查询，确保只处理有效的认证项目和证书

### 3. 消息参数映射

#### 员工消息参数
- `projectName`: 认证项目名称
- `certificateName`: 证书名称
- `expireTime`: 到期时间 (yyyy-MM-dd格式)
- `days`: 提醒天数

#### 管理者消息参数
- `certificateName`: 证书名称
- `userCnt`: 涉及员工人数
- `expireTime`: 到期时间 (yyyy-MM-dd格式)
- `days`: 提醒天数

## 配置说明

### 1. 提醒配置
在认证项目证书管理界面可以配置：
- **到期前天数**: 如30天、7天、1天
- **提醒角色**: 可选择多个角色，用分号分隔

### 2. 消息模板配置
需要在消息中心配置对应的消息模板：
- `certificate_expire_employee`
- `certificate_expire_manager`

### 3. 定时任务配置
- 任务名称: `authPrjCertRemindJobHandler`
- 执行频率: 每天上午8点
- 支持分片处理

## 扩展性设计

### 1. 新角色支持
- 在 `getManagersForUser()` 方法中添加新的角色处理逻辑
- 更新角色类型常量定义

### 2. 新消息模板
- 在 `sendRemindMessages()` 方法中添加新的模板分支
- 配置对应的消息模板和参数映射

### 3. 自定义提醒逻辑
- 可以在 `filterCertsByRemindTime()` 方法中自定义过滤逻辑
- 支持更复杂的提醒时间计算

## 错误处理

### 1. 异常处理
- 单个证书处理失败不影响其他证书
- 详细的错误日志记录
- 优雅降级处理

### 2. 数据验证
- 证书有效性检查
- 认证项目状态验证
- 提醒配置合法性校验

### 3. 性能优化
- 分页处理大量证书数据
- 批量查询减少数据库访问
- 合理的缓存策略

## 测试覆盖

### 1. 单元测试
- `testProcessCertRemindTask()`: 测试主要任务处理流程
- `testCertRemindWithMultipleRoles()`: 测试多角色提醒功能

### 2. 集成测试
- 证书服务集成测试
- 消息服务集成测试
- 用户服务集成测试

## 部署说明

### 1. 数据库更新
- 确保相关表结构已创建
- 验证查询SQL的正确性

### 2. 消息模板配置
- 在消息中心配置对应的消息模板
- 测试模板参数替换功能

### 3. 定时任务配置
- 在XXL-JOB中配置定时任务
- 设置合适的执行时间和分片策略
