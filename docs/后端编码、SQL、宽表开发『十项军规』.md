# 后端编码、SQL、宽表开发『十项军规』

:::
  **军规执行整体原则：**

军规从迭代4.1开始生效执行。

衔接过程中，历史代码老办法；新增代码新办法，按军规执行。

实际执行过程中，有特殊场景，上报FT，至技术委员会讨论分析。
:::

:::
### 如何做好程序设计与保护？

**用户请求传入的任何参数必须做有效性验证（大小、范围、空指针、枚举等维度）。**

说明：忽略参数校验可能导致：page size 过大导致内存溢出; 恶意 order by 导致数据库慢查询; 缓存击穿; SSRF; 任意重定向; SQL 注入，Shell 注入，反序列化注入; 正则输入源串拒绝服务 ReDoS 

扩展：Java 代码用正则来验证客户端的输入，有些正则写法验证普通用户输入没有问题，但是如果攻击人员使用的是特殊构造的字符串来验证，有可能导致死循环的结果。

**存储方案和底层数据结构的设计获得评审一致通过，并沉淀成为文档。**

说明：有缺陷的底层数据结构容易导致系统风险上升，可扩展性下降，重构成本也会因历史数据迁移和系统平滑过渡而陡然增加，所以，存储方案和数据结构需要认真地进行设计和评审，生产环境提交执行后， 需要进行 double check。

**外部正在调用的接口或者二方库依赖的接口，不允许修改方法。避免对接口调用方产生影响。**

说明：接口过时必须加@Deprecated 注解，并清晰地说明采用的新接口或者新服务是什么。

**调用方与被调用方，要充分考虑上下游调用频率、次数、并发等因素，做好自我保护，使用故障重试、幂等保障、熔断限流等机制。**

说明：后期迭代形成公共组件，实现全局控制，减少业务方实现难度。

**核心功能模块，需要打印较完整的日志，做好业务监控与统计，方便定位异常业务调用及错误详情。**

说明：选择恰当的日志级别，避免重复打印日志，业务监控数据统计，优化服务调用治理。
:::

:::
### 如何避免OOM？

**对于循环逻辑，循环写入/查询需要替换为单次批量写入/查询。**

**优雅的处理大事务问题，将select方法放在事务外，禁止事务中执行远程调用，避免一次事务中处理大量数据。**

说明：注意Spring注解@Transactional 声明式事务失效的问题。总量超过5000条数据增删改场景，属于大事务。

**对于导出功能，禁止全部查询到内存后再写入Excel，必须要批量查询批量写入。**

说明：已实现批量导出组件，后期提供。
:::

:::
### 如何避免安全问题？

**隶属于用户个人的页面或者功能，必须进行权限控制校验。**

说明：防止没有做水平权限校验就可随意访问、修改、删除别人的数据，比如查看他人的私信内容。 

**在使用平台资源，譬如短信、邮件、电话、下单、支付，必须实现正确的防重放的机制，如数量限制、疲劳度控制、验证码校验，避免被滥刷而导致资损。** 

说明：如注册时发送验证码到手机，如果没有限制次数和频率，那么可以利用此功能骚扰到其它用户，并造成短信平台资源浪费。 

 **对于文件上传功能，需要对于文件大小、类型进行严格检查和控制。**

说明：攻击者可以利用上传漏洞，上传恶意文件到服务器，并且远程执行，达到控制网站服务器的目的。 
:::

:::
### 如何优雅使用中间件？

**锁**

**对多个资源、数据库表、对象同时加锁时，需要保持一致的加锁顺序，否则可能会造成死锁。**

 说明：线程一需要对表 A、B、C 依次全部加锁后才可以进行更新操作，那么线程二的加锁顺序也必须是 A、B、C，否则可能出现死锁。

**并发修改同一记录时，避免更新丢失，要么在应用层加锁，要么在缓存加锁，要么在数据库层使用乐观锁，使用 version 作为更新依据。**

**Redis**

**编码时根据场景，优先考虑缓存逻辑，能用尽用。增删改动作后，需要及时刷新缓存。**

**所有Key必须有过期时间，最长为30天。**

说明：无法使用淘汰策略优化容量使用、易引发程序逻辑的一致性问题，建议使用UCache代替RedisTemplate和其它Redis客户端，具有强制expireTime的能力。

**禁止存放超过10K的string类型大Key、****集合类型field个数不可超过10000。**

 说明：大Key会引发慢查询，严重拖慢Redis查询速度，引发API慢响应。建议使用UCache代替RedisTemplate和其它Redis客户端，具有大Key监控功能和拒绝策略。

**禁止高危指令，例如keys、 scan、config、monitor、 flushdb、flushall、hgetall 等。**

说明：以上指令时间复杂度O（N），会阻塞Redis服务端命令通道，造成其它连接操作等待，引发慢查询。建议使用UCache代替RedisTemplate和其它Redis客户端，具有高危指令防护能力。

**Elasticsearch**

**Elaticsearch禁止使用Wildcard匹配查询。**

 说明：\*会引发慢查询，会对ES集群造成负载压力，拖慢整体ES查询速度。建议使用ngram + matchPhraseQuery替代wildcard。

**Elasticsearch不要返回太大的结果集，不要未分页拉取全机构数据，单页数量不要超过1000。**

说明：ES作为搜索引擎擅长返回一定条件下的TopN文档，否则会影响查询性能。如果返回大量数据，一定要采取分页。

**ES大数据量插入时，使用批量导入，单次导入限制数量1000条或大小10M，需合理评估。**

说明：高频写入会引发索引重建，会引发ES集群负载压力变大，影响查询性能。能判断出数据大小时参照容量限制，不能判断时参照条数限制。

**RocketMQ**

**业务消费逻辑一定要保障幂等性。**

**对db有压力的queue任务，扩容pod时，根据压测评估结果，合理增加消费者。**

**单条消息大小需要控制，大小限制为4M。**

说明：一方面防止消息体过大，另一方面防止消费方出现大事务

**业务消费线程数量可配置，不要写死。**

说明：当业务体量变化时，无需改动代码，可动态调整消费速率，参考文档 。[《waf-mq版本管理》](https://alidocs.dingtalk.com/i/nodes/KOEmgBoGwD78vl0deD63VndLerP9b30a)
:::

:::
### 如何写好SQL语句？（SQL『十项军规』）

**用户输入的SQL参数严格使用参数绑定或者METADATA字段值限定，防止SQL注入，禁止字符串拼接SQL访问数据库。**

**禁止使用『select \*』查询所有字段，必须根据业务需要只查询所需字段。**

**关联查询联表数量不能超过三张，且关联条件要充分，数据类型必须一致，关联字段必须命中索引。**

说明：报表及信息系统除外。

**单表查询条件必须带org\_id字段，多表关联查询条件主表必须带org\_id字段，子查询条件必须带org\_id字段。**

说明：涉及三方生态微服务需有类似orgid字段，如corpid。数据清洗需要遵循此规定。平台或公共数据是独立表，可没有orgid。

**单次查询返回结果不得超过1000条，如果数据总量超过1000条，必须分页返回。**

**禁止无条件的查询、修改、删除，where条件（包括子查询where条件）必须充分且命中索引，like查询必须左匹配，否则使用ES查询。**

说明：联合产品，开展搜索框需求改造，新代码遵循规定，老代码逐步改造。

**禁止大事务操作（包括批量增删改操作），单个事务影响行数禁止超过5000行；如果有超过5000条数据的增删改操作，则分批顺序处理，禁止并发执行；如果有大事务需求，需改成异步消息队列进行保障。**

**非必要不排序，排序字段不能超过3个，且不能跨表；排序字段建议命中索引。**

说明：报表及信息系统除外。

**非必须强制要求数据一致性的查询场景，必须查询从库（待前端改造完确认框后统一执行）；禁止滥用FORCE MASTER，禁止在mapper代码中直接增加FORCE MASTER；每条查询主库的SELECT语句需要提交到后端技术委员会（后端FT）审核；**

**SQL语法必须兼容主流数据库，禁止使用自定义的函数、过程及变量（遵循SQL99标准）。**

**主键类型必须雪花ID（ bigint(20) unsigned 类型），不允许使用UUID和自增ID；varchar类型长度禁止超过10k；主表中不可创建text、longtext、blob、json等长文本类型字段，如果有需要，单独建表以保持长文本信息。**

说明：老数据UUID不处理，新表使用雪花ID。

**保证数据库和缓存数据一致性：**

**强一致性的数据，更新数据时先更新/删除缓存，后更新数据库；**

**高并发读的场景，更新数据时先更新缓存，后准实时更新数据库；（高并发绝对避免流量打到数据库）**

**弱一致性、最终一致性数据，可以先更新数据库，后异步更新缓存；**
:::

:::
### 如何进行宽表开发？

**业务宽表开发，需要降低大量binlog产生的场景（迁移&DML），避免造成宽表延迟。**

说明：涉及迁移和 DML 清洗数据场景，需要最大程度降低影响，并提前报备。 

**业务高频应用场景改动，需告知各相关方，达成统一意见后，才可实施。**

说明：业务高频应用场景，技术方案设计，要降低对宽表的影响。

**涉及宽表的业务表，不可删除、重建、修改关联数据，如增减字段，需告知各相关方，不可随意操作。**

说明：业务增减字段按流程报备及重洗宽表数据，避免宽表乱序。

**涉及宽表的业务迭代，必须维护宽表数据字典。**

说明：业务字段变更、字段逻辑含义变更，需实时维护并通知相关方。[请至钉钉文档查看附件《宽表数据字典维护》](https://alidocs.dingtalk.com/i/nodes/EGd6jK4Nvk3JlZLPydMOWZOP0LawMmQq?iframeQuery=anchorId%3DX02ljidki86wwwfw15x25r&utm_medium=dingdoc_sheet_plugin_url&utm_source=dingdoc_sheet)

**宽表开发不可随意重跑产线 job ，必须通知 PM 及各相关方。**

说明：如发现宽表数据错误后，需要重跑的场景，告知后方可实施。
:::

**【反馈渠道】**

**军规执行有难度或无法覆盖的场景，请大家在此页面登记。**[《后端军规-无法适配场景问题FAQ》](https://alidocs.dingtalk.com/i/nodes/bpOeKqnEPADJAxnGnLOXVm3zdorXxy0Q)