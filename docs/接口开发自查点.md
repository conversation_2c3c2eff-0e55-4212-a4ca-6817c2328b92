# 项目自查checklist

## 请求（http、job）
1. 请求,http传参列表大小是否有限制（自我保护措施）
2. 请求,接口是否有防重复提交
3. 请求,接口是否支持降级?如何快速降级?指导文档在哪里?
4. 请求,定时任务job传参是否有限制

## 响应
1. 响应,响应列表大小是否有限制,是否有分页
2. 响应,查询列表是否支持瀑布式查询
3. 响应,facade调用结果是否可以缓存,减少重复调用
4. 异常,是否有catch后不打印日志、或catch范围过小的问题

## 代码逻辑检查项
1. 缓存,读接口是否有使用缓存
2. 调用,接口或逻辑是否可以改成异步
3. 调用,是否有同步调用链路过深,比如 A->B->C->D->E->F,和前端一起优化
4. 调用,是否有单链路调用接口过多,比如接口A依赖多个服务的多个接口,和前端一起优化
5. 调用,是否有易出现超时接口,有没有做熔断
6. 调用,是否有并发接口调用,线程池配置是否合适
7. 调用,是否可对依赖方降级,是否可对自己降级（内部异常后,捕获并返回降级值）
8. 调用,是否有故障重试,重试次数是否合理,是否会引发重试风暴
9. 调用,逻辑中调用外部接口发生异常,自己方法能否正常运行
10. 函数,是否有存在循环查询数据库
11. 函数,是否有查询大数据量到内存计算,导致内存占用过大甚至崩溃
12. 函数,是否有数组越界,空指针
13. 函数,查询条件是否可以缩小范围

## SQL检查点
1. SQL是否加了索引
2. SQL是否有获取过多数据
3. SQL是否过度使用force master / force index
4. SQL是否限制了租户
5. SQL是否有删除字段,条件遗漏
6. SQL是否仍有自增id和uuid
7. SQL是否有超过3张表关联,注意视图内部的关联
8. SQL是否有非标语法,要弱化SQL逻辑
9. SQL是否过度使用视图,注意关联放大
10. SQL数据更新时是否范围过大,只更新变化的字段
11. SQLCOUNT语句是否不够精简,是否用了mybatis-plus
12. SQL是否有大表关联查询?
13. SQL学员端是否有大字段返回
14. SQL模糊查询是否可以用ES替换

## 数据库配置
1. 数据库,连接池大小配置是否合理
2. 数据库,高频数据库是否独立
3. 数据库,是否存在2000w+大表,是否引入了分库分表、分布式数据库,是否可做冷数据归档

## mq检查项
1. mq,是否有高峰值的消费并且会向下游产生压力,有没有做流速控制
2. mq,是否有做幂等处理
3. mq,是否和api主服务做了拆分,项目拆分：X-api / X-job / X-mqc
4. mq,是否对单条消息大小进行限制,大消息需要切割
5. job,是否有多租户的数据处理,是否危险
6. job,是否有单批次大量数据处理,是否会对数据库等下游产生压力
7. job,是否和api主服务做了拆分

## Redis
1. redis,是否有大key
2. redis,hmget等命令不要有过多传参
3. redis,是否有用阻塞性强的指令
4. redis,连接池配置是否合适

## xxljob
1. job,是否存在全机构执行,能否改成单机构
2. job,统计类的是否可以降低评率

## 重点业务检查
1. 群发信息（短信、邮件、钉钉.....）逻辑是否有问题
2. 不允许使用管理员账号测试