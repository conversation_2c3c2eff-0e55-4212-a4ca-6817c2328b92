# 认证项目证书管理功能实现说明

## 功能概述

本功能实现了认证项目的证书管理，包括证书配置、颁发、吊销等完整流程。

## 主要功能模块

### 1. 证书列表管理
- **证书列表查询**：展示认证项目下的所有证书配置
- **添加证书**：为认证项目添加新的证书配置
- **编辑证书**：修改证书的获取条件和提醒配置
- **删除证书**：移除证书配置（不影响已颁发的证书）

### 2. 证书获取条件配置
支持以下几种获取方式：
- **认证结果**：基于分层规则结果
- **总得分大于**：基于总分数阈值
- **通过率大于**：基于通过率阈值
- **通过特定认证任务**：完成指定的考试/鉴定活动
- **通过所有认证任务**：完成所有认证活动

### 3. 到期提醒配置
- 支持多个提醒时间点配置
- 支持多种提醒角色：员工、部门经理、直属经理、项目负责人
- 仅对有有效期的证书生效

### 4. 证书颁发管理
- **证书颁发记录列表**：查看已颁发的证书记录
- **手动颁发证书**：为指定用户手动颁发证书
- **批量吊销证书**：批量吊销已颁发的证书
- **状态筛选**：支持按证书状态筛选（生效中、已吊销、已过期）

## 技术实现

### 1. 数据库设计

#### 主要数据表
- `rv_authprj_cert`：项目证书配置表
- `rv_authprj_cert_remind`：证书到期提醒配置表
- `rv_authprj_cert_obtain_item`：证书获取条件明细表
- `rv_cert_log`：证书颁发记录表

### 2. 核心类结构

#### Controller层
- `AuthPrjCertManageController`：证书管理接口控制器

#### Service层
- `AuthprjCertAppService`：证书管理核心业务服务

#### 数据访问层
- `AuthprjCertMapper`：证书配置数据访问
- `AuthprjCertRemindMapper`：提醒配置数据访问
- `AuthprjCertObtainItemMapper`：获取条件数据访问
- `CertLogMapper`：证书记录数据访问

### 3. 外部服务集成

#### 证书服务集成
- `CertificateAclService`：证书服务接口封装
- 支持证书列表查询、颁发记录查询、证书吊销等操作

#### MQ消息处理
- **证书颁发**：通过MQ异步发送证书颁发请求
- **状态回调**：接收证书服务的状态变更通知

### 4. API接口

#### 证书配置接口
```
GET /mgr/authprj/{authprjId}/cert/list - 证书列表
POST /mgr/authprj/{authprjId}/cert - 添加证书
PUT /mgr/authprj/{authprjId}/cert/{certId} - 编辑证书
DELETE /mgr/authprj/{authprjId}/cert/{certId} - 删除证书
```

#### 证书颁发接口
```
POST /mgr/authprj/{authprjId}/cert/{certId}/issue/list - 证书颁发记录列表
POST /mgr/authprj/{authprjId}/cert/{certId}/issue - 颁发证书
POST /mgr/authprj/{authprjId}/cert/{certId}/revoke - 吊销证书
```

## 配置说明

### 1. 证书获取条件配置

#### 认证结果方式
```json
{
  "obtainType": 0,
  "obtainValue": "优秀,良好"
}
```

#### 总得分方式
```json
{
  "obtainType": 1,
  "obtainValue": "85.5"
}
```

#### 通过特定任务方式
```json
{
  "obtainType": 3,
  "obtainValue": "activity-id-1,activity-id-2"
}
```

### 2. 到期提醒配置
```json
{
  "reminds": [
    {
      "remindTime": 30,
      "remindType": "0;3"
    },
    {
      "remindTime": 7,
      "remindType": "0;1;2;3"
    }
  ]
}
```

## 使用说明

### 1. 添加证书
1. 选择证书模板
2. 配置获取条件
3. 设置到期提醒（可选）
4. 保存配置

### 2. 证书颁发
1. 进入证书管理页面
2. 点击"颁发证书"按钮
3. 选择目标用户
4. 确认颁发

### 3. 证书吊销
1. 在证书颁发记录中选择要吊销的证书
2. 点击"吊销"按钮
3. 确认吊销操作

## 注意事项

1. **权限控制**：所有接口都需要相应的权限验证
2. **数据一致性**：证书配置的修改不会影响已颁发的证书
3. **异步处理**：证书颁发通过MQ异步处理，提高系统性能
4. **错误处理**：完善的异常处理和日志记录
5. **事务管理**：关键操作使用事务保证数据一致性

## 扩展功能

1. **自动颁发**：基于员工认证结果变化自动触发证书颁发
2. **证书模板管理**：集成证书模板的选择和预览
3. **批量操作**：支持批量颁发和吊销操作
4. **统计报表**：证书颁发统计和分析报表
