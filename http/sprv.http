### 用户扩展信息同步
POST https://{{host}}/open/user/sync/ext
Accept: application/json
Content-Type: application/json
X-Request-Authorization: {{custom_token}}
Authorization: Bearer {{token}}
Source: {{source}}

{
  "tranId": "test_tran_001",
  "datas": [
    {
      "thirdUserId": "841600d3-bec2-4074-9e32-ae5577ff3f84",
      "manager": 1,
      "keyPosition": 1,
      "gradeLevel": "P6",
      "residenceAddress": "上海市",
      "profCerts": "PMP;CISSP"
    },
    {
      "thirdUserId": "81ebcde9-796d-4dcb-8b31-572ee715d720",
      "manager": 1,
      "keyPosition": 1,
      "gradeLevel": "P5",
      "residenceAddress": "北京市",
      "profCerts": "AWS;CCNA"
    },
    {
      "thirdUserId": "1bfcc685-de79-414b-a7d6-0c433fd6eb03",
      "manager": 1,
      "keyPosition": 0,
      "gradeLevel": "P4",
      "residenceAddress": "上海市",
      "profCerts": "AWS;CCNA"
    },
    {
      "thirdUserId": "65a1ea44-9398-426d-8647-c5777a115b98",
      "manager": 1,
      "keyPosition": 0,
      "gradeLevel": "P2",
      "residenceAddress": "上海市",
      "profCerts": "AWS;CCNA"
    },
    {
      "thirdUserId": "45c38a57-e5be-470b-bb8b-9e35bdc4dd34",
      "manager": 1,
      "keyPosition": 0,
      "gradeLevel": "P4",
      "residenceAddress": "上海市",
      "profCerts": "AWS;CCNA"
    }
  ]
}

### 开放平台接口-获取accessToken
POST https://{{host}}/token?appId={{appId}}&appSecret={{appSecret}}