@echo off
setlocal

set LB_CMD=mvn org.liquibase:liquibase-maven-plugin:

:: 获取当前日期和时间
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set year=%datetime:~0,4%
set month=%datetime:~4,2%
set day=%datetime:~6,2%
:: 组合日期和时间字符串
set date=%year%%month%%day%

:menu
cls
echo Liquibase Maven Plugin Commands
echo -------------------------------
echo 1. Update (Apply changes from the changelog to the database)
echo 2. Rollback (Roll back the database to a previous state)
echo 3. Generate ChangeLog (Generate a changelog file based on the current database schema)
echo 4. Status (Show the number of pending changesets)
echo 5. Diff (Compare two databases and generate a changelog)
echo 6. Tag (Tag the database)
echo 7. ChangelogSync (Sync the database tag to the changelog)
echo 8. Help (Display all commands)
echo 9. Exit (Exit the script)
echo -------------------------------
echo Please enter your choice:
set /p choice=

if "%choice%"=="1" (
    %LB_CMD%update
) else if "%choice%"=="2" (
    %LB_CMD%rollback -Dliquibase.rollbackCount=1
) else if "%choice%"=="3" (
    %LB_CMD%generateChangeLog -Dliquibase.outputChangeLogFile=src/main/resources/db/changelog/changes/%date%_changesets.mysql.sql
) else if "%choice%"=="4" (
    %LB_CMD%status
) else if "%choice%"=="5" (
    %LB_CMD%diff
) else if "%choice%"=="6" (
    %LB_CMD%tag -Dliquibase.tag=v1
) else if "%choice%"=="7" (
    %LB_CMD%changelogSync
) else if "%choice%"=="8" (
    %LB_CMD%help
) else if "%choice%"=="9" (
    echo Exiting the script...
    exit /b
) else (
    echo Invalid choice.
)

echo.
echo Press any key to return to the main menu...
pause > nul
goto menu

endlocal
