#!/bin/bash

LB_CMD="mvn org.liquibase:liquibase-maven-plugin:"

# 获取当前日期和时间
date=$(date "+%Y%m%d")

while true; do
    clear
    echo "Liquibase Maven Plugin Commands"
    echo "-------------------------------"
    echo "1. Update (Apply changes from the changelog to the database)"
    echo "2. Rollback (Roll back the database to a previous state)"
    echo "3. Generate ChangeLog (Generate a changelog file based on the current database schema)"
    echo "4. Status (Show the number of pending changesets)"
    echo "5. Diff (Compare two databases and generate a changelog)"
    echo "6. Tag (Tag the database)"
    echo "7. ChangelogSync (Sync the database tag to the changelog)"
    echo "8. Help (Display all commands)"
    echo "9. Exit (Exit the script)"
    echo "-------------------------------"
    read -p "Please enter your choice: " choice

    case "$choice" in
        1) $LB_CMD"update" ;;
        2) $LB_CMD"rollback -Dliquibase.rollbackCount=1" ;;
        3) $LB_CMD"generateChangeLog -Dliquibase.outputChangeLogFile=src/main/resources/db/changelog/changes/${date}_changesets.yaml" ;;
        4) $LB_CMD"status" ;;
        5) $LB_CMD"diff" ;;
        6) $LB_CMD"tag -Dliquibase.tag=v1" ;;
        7) $LB_CMD"changelogSync" ;;
        8) $LB_CMD"help" ;;
        9) echo "Exiting the script..."; exit 0 ;;
        *) echo "Invalid choice."
    esac
done
