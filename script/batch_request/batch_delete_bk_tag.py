import requests
import time
import traceback


"""
脚本用于批量清理老人发人才标签初始化时出现重复tagKey的数据
"""

# 定义一个函数来处理响应体
def handle_response(response):
    if response.status_code in [200, 204]:
        print("Success:", response.url)
    else:
        print("Failed:", response.status_code, response.text)


# 发送请求并处理响应的函数
def send_request(method, url, headers, data=None):
    try:
        if method == 'PUT':
            response = requests.put(url, headers=headers, json=data)
        elif method == 'DELETE':
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError("Unsupported HTTP method")
        handle_response(response)
    except requests.exceptions.RequestException as e:
        print("An error occurred:", e)
        traceback.print_exc()


# 主函数
def main():
    # 请求头
    headers = {
        "X-Request-Authorization": "b75a2a8b-4e10-41e7-a4e8-b23052a355b4",
        "Content-Type": "application/json",
        "Source": "501",
        "yxt-orgdomain": "api6-ag-pro0510-mr01.yunxuetang.cn",
        "Token": "eyJhbGciOiJIUzUxMiJ9.eyJvcmdJZCI6ImRlNTlmZGZlLTZiNGEtNDhhMS1hMTkzLTE5MmE3ZTBmYTNkNSIsInVzZXJJZCI6ImI0MzYyMjMyLTI4MjYtNDNlMy1iMDdkLTg3ZWI4NmUyNTM0ZCIsImNsdXN0ZXJJZCI6ImFsaXByb2QiLCJleHAiOjE3MjY3MTA3MjV9.m_pw0-3K2umkuSy_T_GLKtI5aS9NfrQEohWLEOcTGXe6olyBPwrx9Zc4g8ipIE4yxRO40wUF2ZN7El2hzGfkYA"
    }

    # 标签ID列表
    tag_ids = [
        "0a09c572-b9b2-442c-9b6a-abd3768b304e"
    ]

    # 基础URL
    base_url = "https://api-phx-ali.yunxuetang.cn/talentrv"

    # 禁用标签
    for tag_id in tag_ids:
        url = f"{base_url}/mgr/tag/{tag_id}/0"
        send_request('PUT', url, headers)
        # 每500毫秒请求一次
        # time.sleep(0.1)

    # 删除标签
    for tag_id in tag_ids:
        url = f"{base_url}/mgr/tag/{tag_id}"
        send_request('DELETE', url, headers)
        # 每500毫秒请求一次
        # time.sleep(0.1)


if __name__ == "__main__":
    main()
