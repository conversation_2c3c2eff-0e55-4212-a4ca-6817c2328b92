import requests
import time
import traceback

"""
脚本用于批量请求 DMP的重新计算接口，
当然也可以改吧改吧用于其他批量调用的场景
"""


# 定义一个函数来处理响应体
def handle_response(response):
    if response.status_code == 200:
        print("Success:", response.url)
    else:
        print("Failed:", response.status_code, response.text)


# 发送请求并处理响应的函数
def do_get(url, headers, params):
    try:
        response = requests.get(url, headers=headers, params=params)
        handle_response(response)
    except requests.exceptions.RequestException as e:
        print("An error occurred:", e)
        traceback.print_exc()


# 主函数
def main():
    # 请求头
    headers = {
        "X-Request-Authorization": "b75a2a8b-4e10-41e7-a4e8-b23052a355b4",
        "Content-Type": "application/json",
        "Source": "501",
        "Token": ""
    }

    # 部分入参数组
    dmp_ids = [
        "f6f2972d-a35f-43d9-892e-3423c9340779"
    ]

    params = {"isForce": "true"}
    org_id = "8eee1504-1ded-4b63-a89e-ba9031135015"

    base_url = "https://api-phx-tf-ali.yunxuetang.com.cn/sptalentrv"

    # 遍历参数数组并调用接口
    for dmp_id in dmp_ids:
        url = f"{base_url}/root/{org_id}/dmp/{dmp_id}/manual"
        do_get(url, headers, params)
        # 每500毫秒请求一次
        time.sleep(0.5)


if __name__ == "__main__":
    main()
