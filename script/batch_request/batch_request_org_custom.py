import json
import requests
import time
import traceback

"""
脚本用于批量请求组织自定义配置接口，
可以根据需要修改参数进行批量调用
"""


# 定义一个函数来处理响应体
def handle_response(response):
    if response.status_code == 200:
        print("请求成功:", response.url)
        print("响应内容:", response.text)
    else:
        print("请求失败:", response.status_code, response.text)


# 发送请求并处理响应的函数
def do_get(url, headers, data):
    try:
        response = requests.get(url, headers=headers, data=data)
        handle_response(response)
    except requests.exceptions.RequestException as e:
        print("发生错误:", e)
        traceback.print_exc()


# 主函数
def main():
    # 请求头
    headers = {
        "source": "501",
        "X-Yxt-Product": "sp",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "X-Request-Authorization": "b75a2a8b-4e10-41e7-a4e8-b23052a355b4",
        "Content-Type": "application/json"
    }

    # 组织ID列表，可以根据需要添加多个组织ID
    org_ids = [
        "00efe884-3c4a-4828-b9a8-0b5e3d720898"
        # 可以在此添加更多组织ID
    ]

    # 请求体数据
    data = json.dumps({})

    base_url = "https://api-phx-tc.yunxuetang.cn/sptalentrv"

    # 遍历组织ID并调用接口
    for org_id in org_ids:
        url = f"{base_url}/root/demo/orgcustom?orgId={org_id}"
        print(f"正在请求组织ID: {org_id}")
        do_get(url, headers, data)
        # 每500毫秒请求一次
        time.sleep(0.5)


if __name__ == "__main__":
    main()
