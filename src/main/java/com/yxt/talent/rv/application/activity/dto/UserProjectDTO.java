package com.yxt.talent.rv.application.activity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class UserProjectDTO {
    @Schema(
        description = "项目ID"
    )
    private String projectId;
    @Schema(
        description = "项目名称"
    )
    private String projectName;
    @Schema(
        description = "状态(2-进行中, 3-已结束)",
        example = "1"
    )
    private Integer status;

    @Schema(
        description = "开始时间"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    @Schema(
        description = "结束时间"
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    @Schema(
        description = "项目类型"
    )
    private Integer timeModel;

}
