package com.yxt.talent.rv.application.calimeet;

import com.yxt.event.EventListener;
import com.yxt.talent.rv.domain.user.event.UserTransferResourceMessageEvent;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CaliMeetEventListener implements EventListener {

    private final CaliMeetAppService caliMeetAppService;

    /**
     * 处理用户资源迁移事件
     * @param calimeetTransferEvent
     */
    @org.springframework.context.event.EventListener
    public void handleUserTransferResourceMessageEvent(
            UserTransferResourceMessageEvent calimeetTransferEvent) {
        String resourceCode = calimeetTransferEvent.getResourceCode();
        if (!Objects.equals(resourceCode, AppConstants.TRANSFERABLE_RESOURCES_CODE_CALI_MEET)) {
            return;
        }

        log.debug("LOG67540:");
        String orgId = calimeetTransferEvent.getOrgId();
        String fromUserId = calimeetTransferEvent.getFrom();
        String toUserId = calimeetTransferEvent.getTo();

        caliMeetAppService.transferResource(orgId, fromUserId, toUserId);
    }

}
