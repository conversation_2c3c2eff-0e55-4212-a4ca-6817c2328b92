package com.yxt.talent.rv.application.calimeet;

import com.yxt.common.annotation.DbHintMaster;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.NewMeetAddLogDTO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CaliMeetLogCommonService {
    private final CalimeetMapper calimeetMapper;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;

    public CalimeetPO getCliMeetPO(String caliMeetId, String orgId) {
        if (StringUtils.isBlank(caliMeetId)) {
            return null;
        }
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId,orgId);
        if (Objects.isNull(calimeetPO)) {
            return null;
        }
        return calimeetPO;
    }

    @DbHintMaster
    public NewMeetAddLogDTO getDetail(String caliMeetId, String orgId) {
        if (StringUtils.isBlank(caliMeetId)) {
            return null;
        }
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (Objects.isNull(calimeetPO)) {
            return null;
        }
        NewMeetAddLogDTO res = new NewMeetAddLogDTO();
        res.setXpdId(calimeetPO.getXpdId());
        res.setMeetName(calimeetPO.getCalimeetName());
        res.setCaliTypeStr(getTypeStr(calimeetPO.getCalimeetType()));
        res.setMeetStTime(getTimeStr(calimeetPO.getStartTime()));
        res.setMeetEdTime(getTimeStr(calimeetPO.getEndTime()));
        res.setOrgTypeStr(Integer.valueOf(0).equals(calimeetPO.getCalimeetMode()) ? "在线校准" : "线下校准");
        res.setShowRateStr(Integer.valueOf(0).equals(calimeetPO.getShowRatio()) ? "未开启" : "开启");
        List<CalimeetParticipantsPO> participantsPOList = calimeetParticipantsMapper.selectByCaliMeetId(orgId,
                caliMeetId);
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            List<String> userIds = participantsPOList.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                    .collect(Collectors.toList());
            Map<String, String> idNameMap = udpLiteUserMapper.selectByUserIds(orgId, userIds).stream()
                    .collect(Collectors.toMap(UdpLiteUserPO::getId, UdpLiteUserPO::getFullname, (e1, e2) -> e1));
            List<CalimeetParticipantsPO> orgUsers = participantsPOList.stream().filter(el -> el.getUserType() == 1)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orgUsers)) {
                res.setOrganizerList(getUserNameStr(orgUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()), idNameMap));
            }
            List<CalimeetParticipantsPO> caliUsers = participantsPOList.stream().filter(el -> el.getUserType() == 2)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caliUsers)) {
                res.setCaliUserList(getUserNameStr(caliUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()), idNameMap));
            }
        }
        return res;
    }

    private String getUserNameStr(List<String> userIds, Map<String, String> idNameMap) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(userIds)) {
            return "";
        }
        for (String uId : userIds) {
            sb.append(idNameMap.getOrDefault(uId, "-"));
            sb.append(";");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    private String getTimeStr(LocalDateTime startTime) {
        if (Objects.isNull(startTime)) {
            return null;
        }
        return startTime.format(DateTimeUtil.YYYY_MM_DD_HH_MM);
    }

    private String getTypeStr(Integer calimeetType) {
        String res = "";
        //0-维度分层结果，1-维度结果，2-指标结果
        if (Integer.valueOf(0).equals(calimeetType)) {
            res = "维度分层结果";
        } else if (Integer.valueOf(1).equals(calimeetType)) {
            res = "维度结果";
        } else if (Integer.valueOf(2).equals(calimeetType)) {
            res = "指标结果";
        }
        return res;
    }
}
