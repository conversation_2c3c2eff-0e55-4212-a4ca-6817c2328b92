package com.yxt.talent.rv.application.calimeet;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.result.calculator.UserDimCombResultCalculator;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CaliMeetResultSyncService {

    /**
     * 校准会数据同步上下文
     */
    @Data
    public static class CaliMeetSyncContext {
        // 基础信息
        private String orgId;
        private String xpdId;
        private String calimeetId;
        private String operatorId;
        private int calimeetType;

        // 批量查询的数据缓存
        private List<String> allUserIds;
        private Map<String, XpdUserExtPO> existingUserExtMap;
        private Map<String, XpdResultUserPO> existingUserResultMap;
        private Map<String, List<XpdResultUserDimPO>> existingUserDimResultsMap;
        private Map<String, List<XpdResultUserDimcombPO>> existingUserDimCombResultsMap;
        private Map<String, List<XpdResultUserIndicatorPO>> existingUserIndicatorResultsMap;
        private List<XpdLevelPO> xpdLevels;
        private List<XpdDimCombPO> dimCombs;
        private Map<String, Integer> levelCompetentMap;

        public CaliMeetSyncContext(String orgId, String xpdId, String calimeetId, String operatorId, int calimeetType) {
            this.orgId = orgId;
            this.xpdId = xpdId;
            this.calimeetId = calimeetId;
            this.operatorId = operatorId;
            this.calimeetType = calimeetType;
            this.allUserIds = new ArrayList<>();
            this.existingUserExtMap = new HashMap<>();
            this.existingUserResultMap = new HashMap<>();
            this.existingUserDimResultsMap = new HashMap<>();
            this.existingUserDimCombResultsMap = new HashMap<>();
            this.existingUserIndicatorResultsMap = new HashMap<>();
            this.levelCompetentMap = new HashMap<>();
        }
    }

    private final CalimeetMapper calimeetMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final UserDimCombResultCalculator userDimCombResultCalculator;

    /**
     * 将校准结果同步到盘点项目中的员工结果
     *
     * @param calimeet   CalimeetPO
     * @param operatorId 操作人ID
     * @param orgId      组织ID
     */
    @Async
    public void syncCalimeetData(CalimeetPO calimeet, String operatorId, String orgId) {
        String xpdId = calimeet.getXpdId();
        String calimeetId = calimeet.getId();
        log.info(
            "LOG21493:开始同步校准会数据到盘点项目，calimeetId={}, xpdId={}, orgId={}", calimeet.getId(), xpdId, orgId);

        // 查询校准会的所有被校准人的结果
        List<CalimeetDimResultDto> calimeetRecords = calimeetRecordMapper.getAllRecordsByCalimeetId(orgId, calimeetId);
        if (CollectionUtils.isEmpty(calimeetRecords)) {
            log.info("校准会没有校准记录，跳过同步，calimeetId={}", calimeetId);
            return;
        }

        log.info("LOG21503:找到{}条校准记录需要同步", calimeetRecords.size());

        // 获取校准会信息以确定校准类型
        CalimeetPO calimeetInfo = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        if (calimeetInfo == null) {
            log.warn("无法获取校准会信息，跳过同步");
            return;
        }

        // 创建同步上下文，统一收口传参
        CaliMeetSyncContext context = new CaliMeetSyncContext(orgId, xpdId, calimeetId, operatorId, calimeetInfo.getCalimeetType());

        // 批量预加载数据，避免循环查库
        preloadBatchData(context, calimeetRecords);

        // 同步suggestion到XpdUserExtPO
        syncSuggestionToXpdUserExt(context, calimeetRecords);

        // 同步到xpd结果表：XpdResultUserPO, XpdResultUserDimPO, XpdResultUserDimcombPO, XpdResultUserIndicatorPO
        syncCalimeetResultsToXpd(context, calimeetRecords);

        log.info("LOG21513:校准会数据同步完成，calimeetId={}", calimeetId);
    }

    /**
     * 批量预加载数据，避免循环查库
     */
    private void preloadBatchData(CaliMeetSyncContext context, List<CalimeetDimResultDto> calimeetRecords) {
        // 提取所有用户ID
        context.setAllUserIds(calimeetRecords.stream()
            .map(CalimeetDimResultDto::getUserId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(context.getAllUserIds())) {
            log.info("没有有效的用户ID，跳过数据预加载");
            return;
        }

        log.info("开始预加载{}个用户的相关数据", context.getAllUserIds().size());

        // 批量查询XpdUserExt
        List<XpdUserExtPO> existingUserExts = xpdUserExtMapper.selectByXpdIdAndUserIds(
            context.getOrgId(), context.getXpdId(), context.getAllUserIds());
        context.setExistingUserExtMap(existingUserExts.stream()
            .collect(Collectors.toMap(XpdUserExtPO::getUserId, Function.identity(), (existing, replacement) -> existing)));

        // 批量查询XpdResultUser
        List<XpdResultUserPO> existingUserResults = xpdResultUserMapper.findByXpdIdAndUserIds(
            context.getOrgId(), context.getXpdId(), context.getAllUserIds());
        context.setExistingUserResultMap(existingUserResults.stream()
            .collect(Collectors.toMap(XpdResultUserPO::getUserId, Function.identity(), (existing, replacement) -> existing)));

        // 批量查询XpdResultUserDim
        List<XpdResultUserDimPO> existingUserDimResults = xpdResultUserDimMapper.findByXpdIdAndUserIds(
            context.getOrgId(), context.getXpdId(), context.getAllUserIds());
        context.setExistingUserDimResultsMap(existingUserDimResults.stream()
            .collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId)));

        // 批量查询XpdResultUserDimcomb
        List<XpdResultUserDimcombPO> existingUserDimCombResults = xpdResultUserDimcombMapper.findByXpdIdAndUserIds(
            context.getOrgId(), context.getXpdId(), context.getAllUserIds());
        context.setExistingUserDimCombResultsMap(existingUserDimCombResults.stream()
            .collect(Collectors.groupingBy(XpdResultUserDimcombPO::getUserId)));

        // 批量查询XpdResultUserIndicator
        List<XpdResultUserIndicatorPO> existingUserIndicatorResults = xpdResultUserIndicatorMapper.findByXpdIdAndUserIds(
            context.getOrgId(), context.getXpdId(), context.getAllUserIds());
        context.setExistingUserIndicatorResultsMap(existingUserIndicatorResults.stream()
            .collect(Collectors.groupingBy(XpdResultUserIndicatorPO::getUserId)));

        // 查询XpdLevel数据
        context.setXpdLevels(xpdLevelMapper.selectByXpdId(context.getOrgId(), context.getXpdId()));
        context.setLevelCompetentMap(context.getXpdLevels().stream()
            .filter(Objects::nonNull) // 过滤掉 null 元素
            .filter(e -> e.getCompetent() != null)
            .collect(Collectors.toMap(
                XpdLevelPO::getId,
                XpdLevelPO::getCompetent,
                (existing, replacement) -> existing // 如果键冲突，保留现有的值
            )));

        // 查询XpdDimComb数据
        context.setDimCombs(xpdDimCombMapper.listByXpdId(context.getOrgId(), context.getXpdId()));

        log.info(
            "LOG21673:数据预加载完成，用户扩展信息:{}条，用户结果:{}条，用户维度结果:{}条，用户维度组结果:{}条，用户指标结果:{}条",
            context.getExistingUserExtMap().size(),
            context.getExistingUserResultMap().size(),
            context.getExistingUserDimResultsMap().size(),
            context.getExistingUserDimCombResultsMap().size(),
            context.getExistingUserIndicatorResultsMap().size());
    }

    /**
     * 同步发展建议到XpdUserExtPO
     */
    private void syncSuggestionToXpdUserExt(CaliMeetSyncContext context, List<CalimeetDimResultDto> calimeetRecords) {
        log.info("LOG21483:开始同步发展建议到XpdUserExt表，记录数量={}", calimeetRecords.size());

        if (CollectionUtils.isEmpty(context.getAllUserIds())) {
            log.info("没有有效的用户ID，跳过同步发展建议");
            return;
        }

        // 构建校准记录映射
        Map<String, String> suggestionMap = calimeetRecords.stream()
            .filter(record -> StringUtils.isNotBlank(record.getUserId()))
            .collect(Collectors.toMap(
                CalimeetDimResultDto::getUserId,
                record -> Objects.toString(record.getSuggestion(), ""),
                (existing, replacement) -> replacement
            ));

        List<XpdUserExtPO> toUpdate = new ArrayList<>();
        List<XpdUserExtPO> toInsert = new ArrayList<>();

        for (String userId : context.getAllUserIds()) {
            String suggestion = suggestionMap.get(userId);
            XpdUserExtPO existing = context.getExistingUserExtMap().get(userId);

            if (existing != null) {
                // 更新现有记录
                existing.setSuggestion(suggestion);
                existing.setUpdateUserId(context.getOperatorId());
                existing.setUpdateTime(new Date());
                toUpdate.add(existing);
            } else {
                // 创建新记录
                XpdUserExtPO newRecord = new XpdUserExtPO();
                newRecord.setId(ApiUtil.getUuid());
                newRecord.setOrgId(context.getOrgId());
                newRecord.setXpdId(context.getXpdId());
                newRecord.setUserId(userId);
                newRecord.setSuggestion(suggestion);
                newRecord.setDeleted(0);
                EntityUtil.setAuditFields(newRecord, context.getOperatorId());
                toInsert.add(newRecord);
            }
        }

        // 批量更新
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            xpdUserExtMapper.batchUpdateRvXpdUserExt(toUpdate);
            log.info("LOG21523:更新了{}条XpdUserExt记录的发展建议", toUpdate.size());
        }

        // 批量插入
        if (CollectionUtils.isNotEmpty(toInsert)) {
            xpdUserExtMapper.insertBatch(toInsert);
            log.info("LOG21533:插入了{}条新的XpdUserExt记录", toInsert.size());
        }
    }

    /**
     * 同步校准结果到XPD结果表
     */
    private void syncCalimeetResultsToXpd(CaliMeetSyncContext context, List<CalimeetDimResultDto> calimeetRecords) {
        log.info("开始同步校准结果到XPD结果表，记录数量={}", calimeetRecords.size());

        if (CollectionUtils.isEmpty(calimeetRecords)) {
            return;
        }

        for (CalimeetDimResultDto record : calimeetRecords) {
            if (StringUtils.isBlank(record.getUserId())) {
                continue;
            }

            try {
                // 解析校准详情
                CaliUpdateUserResultWrapDto caliDetails = parseCalimeetDetails(record.getCaliDetails());
                if (caliDetails == null || CollectionUtils.isEmpty(caliDetails.getUserResults())) {
                    log.debug("LOG21553:用户{}没有校准详情数据，跳过", record.getUserId());
                    continue;
                }

                // 管理员实际调整的维度分层/维度得分/指标得分/指标达标率等数据，没有调整的数据不会记录在此
                List<CaliUpdateUserResultDto> userResults = caliDetails.getUserResults();
                // 用户校准之后，重新计算的盘点项目结果， 维度结果，指标结果等数据，
                // 如果某个维度结果经过校准之后没有计算出来任务结果，这里不会存储，复制的时候就需要清理掉原有的盘点项目结果
                CaliDimResultWrapDto resultDetails = parseCalimeetResults(record.getResultDetails());

                // 根据校准会类型同步不同的结果表 (CaliMeetTypeEnum)
                switch (context.getCalimeetType()) {
                    case 0: // CaliMeetTypeEnum.LEVEL - 维度分层结果
                        syncUserDimLevelResults(context, record.getUserId(), resultDetails);
                        break;
                    case 1: // CaliMeetTypeEnum.DIM - 维度结果
                        syncUserDimResults(context, record.getUserId(), resultDetails);
                        break;
                    case 2: // CaliMeetTypeEnum.INDICATOR - 指标结果
                        syncUserIndicatorResults(context, record.getUserId(), userResults, resultDetails);
                        break;
                    default:
                        log.warn("未知的校准会类型：{}", context.getCalimeetType());
                }

            } catch (Exception e) {
                log.error("同步用户{}的校准结果时发生异常", record.getUserId(), e);
            }
        }

        log.info("校准结果同步到XPD结果表完成");
    }

    private CaliDimResultWrapDto parseCalimeetResults(String resultDetails) {
        if (StringUtils.isBlank(resultDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(resultDetails, CaliDimResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21563:解析校准结果JSON失败：{}", resultDetails, e);
            return null;
        }
    }


    /**
     * 解析校准详情JSON
     */
    @Nullable
    private CaliUpdateUserResultWrapDto parseCalimeetDetails(String caliDetails) {
        if (StringUtils.isBlank(caliDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(caliDetails, CaliUpdateUserResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21543:解析校准详情JSON失败：{}", caliDetails, e);
            return null;
        }
    }

    /**
     * 同步用户维度分层结果
     */
    private void syncUserDimLevelResults(CaliMeetSyncContext context, String userId, CaliDimResultWrapDto resultDetails) {
        log.debug("LOG21573:同步用户{}的维度分层结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(context, userId, resultDetails);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(context, userId, resultDetails);

        // 3. 同步维度组结果
        syncUserDimCombResults(context, userId, resultDetails);
    }

    /**
     * 同步用户维度结果
     */
    private void syncUserDimResults(CaliMeetSyncContext context, String userId, CaliDimResultWrapDto resultDetails) {
        log.debug("同步用户{}的维度结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(context, userId, resultDetails);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(context, userId, resultDetails);

        // 3. 同步维度组结果
        syncUserDimCombResults(context, userId, resultDetails);
    }

    /**
     * 同步用户指标结果
     */
    private void syncUserIndicatorResults(CaliMeetSyncContext context, String userId,
        List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails) {
        log.debug("LOG21683:同步用户{}的指标结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(context, userId, resultDetails);

        // 2. 以resultDetails为准，完整同步指标结果（先清理后同步）
        syncCompleteUserIndicatorResults(context, userId, userResults);

        // 3. 同步维度组结果
        syncUserDimCombResults(context, userId, resultDetails);
    }

    /**
     * 同步用户项目级别结果
     */
    private void syncUserProjectResult(CaliMeetSyncContext context, String userId, CaliDimResultWrapDto resultDetails) {
        if (resultDetails == null || StringUtils.isBlank(resultDetails.getXpdLevelId())) {
            log.debug("用户{}没有项目级别结果数据，跳过同步", userId);
            return;
        }

        // 从上下文中获取现有的用户项目结果记录
        XpdResultUserPO existingResult = context.getExistingUserResultMap().get(userId);

        if (existingResult != null) {
            // 更新现有记录
            existingResult.setXpdLevelId(resultDetails.getXpdLevelId());
            existingResult.setCompetent(context.getLevelCompetentMap().getOrDefault(resultDetails.getXpdLevelId(), 0));
            if (resultDetails.getScoreValue() != null) {
                existingResult.setScoreValue(resultDetails.getScoreValue());
            }
            if (resultDetails.getQualifiedPtg() != null) {
                existingResult.setQualifiedPtg(resultDetails.getQualifiedPtg());
            }
            existingResult.setUpdateUserId(context.getOperatorId());
            existingResult.setUpdateTime(LocalDateTime.now());
            existingResult.setCaliFlag(1); // 标记为已校准
            existingResult.buildSnapshot();
            xpdResultUserMapper.updateByPrimaryKey(existingResult);
            log.debug("LOG21613:更新了用户{}的项目级别结果，等级ID={}", userId, resultDetails.getXpdLevelId());
        } else {
            log.debug("LOG21603:未找到用户{}的项目级别结果记录，生成一个新的", userId);
            XpdResultUserPO po = new XpdResultUserPO();
            po.setOrgId(context.getOrgId());
            po.setXpdId(context.getXpdId());
            po.setUserId(userId);
            po.setXpdLevelId(resultDetails.getXpdLevelId());
            if (resultDetails.getScoreValue() != null) {
                po.setScoreValue(resultDetails.getScoreValue());
            }
            if (resultDetails.getQualifiedPtg() != null) {
                po.setQualifiedPtg(resultDetails.getQualifiedPtg());
            }
            po.setCompetent(context.getLevelCompetentMap().getOrDefault(resultDetails.getXpdLevelId(), 0));
            po.setUpdateUserId(context.getOperatorId());
            po.setUpdateTime(LocalDateTime.now());
            po.setCaliFlag(1); // 标记为已校准
            po.setOriginalSnap(null); // 新增的记录之前没有盘点结果，无法记录快照
            po.setDeleted(0);
            po.setCreateUserId(context.getOperatorId());
            po.setCreateTime(LocalDateTime.now());
            xpdResultUserMapper.insert(po);
        }
    }

    /**
     * 完整同步用户维度结果（先清理后同步）
     */
    private void syncCompleteUserDimResults(CaliMeetSyncContext context, String userId, CaliDimResultWrapDto resultDetails) {
        if (resultDetails == null) {
            log.debug("用户{}没有结果详情数据，跳过维度结果同步", userId);
            return;
        }

        // 1. 从上下文中获取用户现有的所有维度结果
        List<XpdResultUserDimPO> existingRecords = context.getExistingUserDimResultsMap().getOrDefault(userId, new ArrayList<>());
        Map<String, XpdResultUserDimPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdResultUserDimPO::getSdDimId, Function.identity(), (existing, replacement) -> existing));

        // 2. 从resultDetails中获取应该保留的维度ID
        Set<String> targetDimIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            targetDimIds = resultDetails.getDimResults().stream()
                .map(CaliDimResultDto::getSdDimId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        }
        final Set<String> effectivelyFinalTargetDimIds = targetDimIds;

        // 3. 清理不在resultDetails中的旧维度结果
        List<String> toDeleteIds = existingRecords.stream()
            .filter(record -> !effectivelyFinalTargetDimIds.contains(record.getSdDimId()))
            .map(XpdResultUserDimPO::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toDeleteIds)) {
            // 逻辑删除不在resultDetails中的维度结果
            List<XpdResultUserDimPO> toDelete = toDeleteIds.stream()
                .map(id -> {
                    XpdResultUserDimPO po = new XpdResultUserDimPO();
                    po.setId(id);
                    po.setDeleted(1);
                    po.setUpdateUserId(context.getOperatorId());
                    po.setUpdateTime(LocalDateTime.now());
                    return po;
                })
                .collect(Collectors.toList());
            xpdResultUserDimMapper.batchUpdateResult(toDelete);
            log.debug("LOG21623:清理了用户{}的旧维度:{}", userId, toDeleteIds);
        }

        // 4. 同步resultDetails中的维度结果
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            List<XpdResultUserDimPO> toUpdate = new ArrayList<>();

            for (CaliDimResultDto dimResult : resultDetails.getDimResults()) {
                if (StringUtils.isBlank(dimResult.getSdDimId())) {
                    log.warn("LOG21763:{}的维度ID为空", BeanHelper.bean2Json(dimResult, JsonInclude.Include.ALWAYS));
                    continue;
                }

                XpdResultUserDimPO existing = existingMap.get(dimResult.getSdDimId());
                if (existing != null && existing.getDeleted() == 0) {
                    // 更新现有记录
                    existing.buildSnapshot(); // 保存原始快照
                    if (StringUtils.isNotBlank(dimResult.getGridLevelId())) {
                        existing.setGridLevelId(dimResult.getGridLevelId());
                    }
                    if (dimResult.getScoreValue() != null) {
                        existing.setScoreValue(dimResult.getScoreValue());
                    }
                    if (dimResult.getQualifiedPtg() != null) {
                        existing.setQualifiedPtg(dimResult.getQualifiedPtg());
                    }
                    existing.setUpdateUserId(context.getOperatorId());
                    existing.setUpdateTime(LocalDateTime.now());
                    existing.setCaliFlag(1); // 标记为已校准
                    toUpdate.add(existing);
                }
            }

            if (CollectionUtils.isNotEmpty(toUpdate)) {
                xpdResultUserDimMapper.batchUpdateResult(toUpdate);
                log.debug("LOG21633:更新了用户{}的{}个维度结果", userId, toUpdate.size());
            }
        }
    }

    /**
     * 完整同步用户指标结果（先清理后同步）
     */
    private void syncCompleteUserIndicatorResults(CaliMeetSyncContext context, String userId,
        List<CaliUpdateUserResultDto> userResults) {
        log.debug("LOG21773:开始同步用户{}的指标结果", userId);

        // 1. 从上下文中获取用户现有的所有指标结果
        List<XpdResultUserIndicatorPO> existingRecords = context.getExistingUserIndicatorResultsMap().getOrDefault(userId, new ArrayList<>());

        Map<String, XpdResultUserIndicatorPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdResultUserIndicatorPO::getSdIndicatorId, Function.identity(), (existing, replacement) -> existing));

        // 4. 同步校准结果中的指标结果
        if (CollectionUtils.isNotEmpty(userResults)) {
            List<XpdResultUserIndicatorPO> toUpdate = new ArrayList<>();

            for (CaliUpdateUserResultDto indicatorResult : userResults) {
                if (StringUtils.isBlank(indicatorResult.getSdIndicatorId())) {
                    continue;
                }

                XpdResultUserIndicatorPO existing = existingMap.get(indicatorResult.getSdIndicatorId());
                if (existing != null && existing.getDeleted() == 0) {
                    // 更新现有记录
                    existing.buildSnapshot(); // 保存原始快照
                    if (indicatorResult.getScoreValue() != null) {
                        existing.setScoreValue(indicatorResult.getScoreValue());
                    }
                    if (indicatorResult.getQualified() != null) {
                        existing.setQualified(indicatorResult.getQualified());
                    }
                    existing.setUpdateUserId(context.getOperatorId());
                    existing.setUpdateTime(LocalDateTime.now());
                    existing.setCaliFlag(1); // 标记为已校准

                    // 按照绩效等级进行计算的维度 在校准会中不能进行调整，所以这里不考虑这种情况
//                    existing.setPerfResultId();

                    toUpdate.add(existing);
                }
            }

            if (CollectionUtils.isNotEmpty(toUpdate)) {
                xpdResultUserIndicatorMapper.batchUpdateResult(toUpdate);
                log.debug("LOG21783:更新了用户{}的{}个指标结果", userId, toUpdate.size());
            }
        }

        log.debug("LOG21793:用户{}的指标结果同步完成", userId);
    }

    /**
     * 同步用户维度组结果
     */
    private void syncUserDimCombResults(CaliMeetSyncContext context, String userId, CaliDimResultWrapDto resultDetails) {
        String orgId = context.getOrgId();
        String xpdId = context.getXpdId();
        if (CollectionUtils.isEmpty(context.getDimCombs())) {
            log.info("LOG21673:项目{}没有配置维度组，无需同步维度组结果", xpdId);
            return;
        }

        // 由于resultDetails存储于了用户所有的有效维度的结果（不论是否受到了校准），所以可以直接以此作为完整的维度结果来计算维度组结果
        Map<String, CaliDimResultDto> dimResultMap = resultDetails.getDimResults().stream()
            .collect(Collectors.toMap(CaliDimResultDto::getSdDimId, Function.identity(), (existing, replacement) -> existing));

        // 调用计算器计算结果
        List<XpdResultUserDimcombPO> newResults = userDimCombResultCalculator.calculateUserDimCombResults(
            orgId, xpdId, userId, context.getDimCombs(), dimResultMap, context.getOperatorId());

        if (CollectionUtils.isEmpty(newResults)) {
            log.info("LOG21678:用户{}在项目{}中没有计算出任何维度组结果, 清理用户现存维度组结果", userId, xpdId);
            // 如果没有新结果，可能需要清理旧结果，或者根据业务逻辑决定是否清理
            xpdResultUserDimcombMapper.deleteByUserIdAndXpdId(orgId, xpdId, userId);
            return;
        }

        // 从上下文中获取用户已有的维度组结果
        List<XpdResultUserDimcombPO> existingResults = context.getExistingUserDimCombResultsMap().getOrDefault(userId, new ArrayList<>());

        syncDimCombResultsIncremental(existingResults, newResults, userId, context.getOperatorId());
    }

    /**
     * 增量同步维度组结果
     */
    private void syncDimCombResultsIncremental(
        List<XpdResultUserDimcombPO> existingResults,
        List<XpdResultUserDimcombPO> newResults, String userId, String operatorId) {

        // 构建现有结果的映射表，key为维度组ID
        Map<String, XpdResultUserDimcombPO> existingMap = existingResults.stream()
            .collect(Collectors.toMap(XpdResultUserDimcombPO::getDimCombId, Function.identity(), (existing, replacement) -> existing));

        // 构建新结果的映射表，key为维度组ID
        Map<String, XpdResultUserDimcombPO> newMap = newResults.stream()
            .collect(Collectors.toMap(XpdResultUserDimcombPO::getDimCombId, Function.identity(), (existing, replacement) -> existing));

        List<XpdResultUserDimcombPO> toInsert = new ArrayList<>();
        List<XpdResultUserDimcombPO> toUpdate = new ArrayList<>();
        List<String> toDeleteIds = new ArrayList<>();

        // 处理新结果：新增或更新
        for (XpdResultUserDimcombPO newResult : newResults) {
            String dimCombId = newResult.getDimCombId();
            XpdResultUserDimcombPO existing = existingMap.get(dimCombId);

            if (existing == null) {
                // 新增 - newResult
                toInsert.add(newResult);
            } else {
                // 更新：保留原有的ID和创建信息，更新其他字段
                existing.buildSnapshot();
                existing.setCellId(newResult.getCellId());
                existing.setCellIndex(newResult.getCellIndex());
                existing.setUpdateUserId(operatorId);
                existing.setUpdateTime(LocalDateTime.now());
                existing.setCaliFlag(1);
                toUpdate.add(existing);
            }
        }

        // 处理需要删除的结果：存在于现有结果但不在新结果中
        for (XpdResultUserDimcombPO existingResult : existingResults) {
            if (!newMap.containsKey(existingResult.getDimCombId())) {
                toDeleteIds.add(existingResult.getId());
            }
        }

        // 执行数据库操作
        if (!toInsert.isEmpty()) {
            xpdResultUserDimcombMapper.batchInsert(toInsert);
            log.debug("LOG21723:用户{}新增{}个维度组结果", userId, toInsert.size());
        }

        if (!toUpdate.isEmpty()) {
            xpdResultUserDimcombMapper.updateBatch(toUpdate);
            log.debug("LOG21733:用户{}更新{}个维度组结果", userId, toUpdate.size());
        }

        if (!toDeleteIds.isEmpty()) {
            xpdResultUserDimcombMapper.deleteBatch(toDeleteIds, operatorId);
            log.debug("LOG21743:用户{}删除{}个维度组结果", userId, toDeleteIds.size());
        }
    }

}
