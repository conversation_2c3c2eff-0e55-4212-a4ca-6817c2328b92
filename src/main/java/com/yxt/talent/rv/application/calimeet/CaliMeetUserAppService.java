package com.yxt.talent.rv.application.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.ApplicationCommandService;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserAddCmd;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUser4Add;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserDimcombMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimcombPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 校准人员应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ApplicationCommandService
public class CaliMeetUserAppService {
    public static final String DATA_PERM_RESULT = "sp_talentrv_calibrationmemberlist_dep_extent";
    private final CalimeetUserMapper calimeetUserMapper;
    private final CalimeetMapper calimeetMapper;
    private final I18nTranslator i18nTranslator;
    private final AuthService authService;

    // 增加盘点结果相关的Mapper
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;

    // 增加校准会结果相关的Mapper
    private final CalimeetResultUserMapper calimeetResultUserMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetResultUserDimcombMapper calimeetResultUserDimcombMapper;
    private final CalimeetResultUserIndicatorMapper calimeetResultUserIndicatorMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final CalimeetRecordItemMapper calimeetRecordItemMapper;

    /**
     * 查询校准人员列表（分页）
     *
     * @param caliMeetId 校准会ID
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PagingList<CaliMeetUserVO> pageCaliMeetUsers(String caliMeetId, CaliMeetUserQuery queryParam) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        String orgId = userAuth.getOrgId();
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        IPage<CaliMeetUserVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        // 调用Mapper查询校准人员列表
        IPage<CaliMeetUserVO> result = calimeetUserMapper.selectCaliMeetUsers(page, orgId, caliMeetId, queryParam);
        // 国际化翻译
        i18nTranslator.translate(orgId, userAuth.getLocale(), result.getRecords());
        return BeanCopierUtil.toPagingList(result);
    }

    /**
     * 查询校准人员列表
     *
     * @param caliMeetId 校准会ID
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public List<CaliMeetUserVO> listCaliMeetUsers(String caliMeetId, CaliMeetUserQuery queryParam) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        String orgId = userAuth.getOrgId();

        // 调用Mapper查询校准人员列表
        List<CaliMeetUserVO> result = calimeetUserMapper.selectCaliMeetUsers(orgId, caliMeetId, queryParam);
        // 国际化翻译
        i18nTranslator.translate(orgId, userAuth.getLocale(), result);
        return result;
    }

    /**
     * 查询未添加的校准人员列表（来自关联的盘点项目）
     *
     * @param caliMeetId 校准会ID
     * @param query      查询参数
     * @return 分页结果
     */
    public PagingList<CaliMeetUser4Add> listNotAddedUsers(
        String caliMeetId, SearchUdpScopeAuthQuery query) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        String orgId = userAuth.getOrgId();
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        IPage<CaliMeetUser4Add> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        // 调用Mapper查询未添加的人员列表
        IPage<CaliMeetUser4Add> result = calimeetUserMapper.listNotAddedUsers(page, orgId, caliMeetId, query);
        // 国际化翻译
        i18nTranslator.translate(orgId, userAuth.getLocale(), result.getRecords());
        return BeanCopierUtil.toPagingList(result);
    }

    /**
     * 添加校准人员
     *
     * @param orgId 组织ID
     * @param caliMeetId 校准会ID
     * @param addCmd 添加校准人员DTO
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int addCaliMeetUsers(String orgId, String caliMeetId, CaliMeetUserAddCmd addCmd) {
        if (CollectionUtils.isEmpty(addCmd.getUserIds())) {
            return 0;
        }

        // 1. 获取校准会信息，需要获取对应的盘点项目ID
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (calimeet == null) {
            log.error("校准会不存在，orgId={}, caliMeetId={}", orgId, caliMeetId);
            return 0;
        }

        String xpdId = calimeet.getXpdId();

        // 2. 过滤掉已存在的用户
        List<String> userIdsToAdd = addCmd.getUserIds();
        List<String> existentUserIds = calimeetUserMapper.getExistentUserIds(orgId, caliMeetId, userIdsToAdd);

        List<String> nonExistentUserIds = userIdsToAdd.stream()
                .filter(userId -> !existentUserIds.contains(userId))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonExistentUserIds)) {
            log.info("所有用户已存在于校准会中，无需添加，orgId={}, caliMeetId={}", orgId, caliMeetId);
            return 0;
        }

        // 3. 构建校准会人员对象列表并插入
        List<CalimeetUserPO> userPOList = new ArrayList<>();
        for (String userId : nonExistentUserIds) {
            CalimeetUserPO po = new CalimeetUserPO();
            po.setOrgId(orgId);
            po.setCalimeetId(caliMeetId);
            po.setUserId(userId);
            po.setCaliStatus(0); // 未校准状态
            EntityUtil.setAuditFields(po);
            userPOList.add(po);
        }

        int addedCount = 0;
        if (!userPOList.isEmpty()) {
            addedCount = calimeetUserMapper.batchInsert(userPOList);
            log.info("成功添加{}个用户到校准会中，orgId={}, caliMeetId={}", addedCount, orgId, caliMeetId);
            // 4. 同步盘点结果到校准会
            syncXpdResults(orgId, xpdId, caliMeetId, nonExistentUserIds);
        }

        return addedCount;
    }

    /**
     * 同步盘点结果到校准会
     *
     * @param orgId        组织ID
     * @param xpdId        盘点项目ID
     * @param caliMeetId   校准会ID
     * @param userIds      用户ID列表
     */
    private void syncXpdResults(String orgId, String xpdId, String caliMeetId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        log.info(
            "开始同步盘点结果到校准会，orgId={}, xpdId={}, caliMeetId={}, userCount={}",
            orgId, xpdId, caliMeetId, userIds.size());

        // 0. 先清空校准人员的盘点结果快照
        calimeetResultUserMapper.deleteUserResults(orgId, caliMeetId, userIds);
        calimeetResultUserDimMapper.deleteUserDimResults(orgId, caliMeetId, userIds);
        calimeetResultUserDimcombMapper.deleteUserDimcombResults(orgId, caliMeetId, userIds);
        calimeetResultUserIndicatorMapper.deleteUserIndicatorResults(orgId, caliMeetId, userIds);

        // 1. 同步用户整体结果数据
        syncUserResults(orgId, xpdId, caliMeetId, userIds);

        // 2. 同步用户维度结果数据
        syncUserDimResults(orgId, xpdId, caliMeetId, userIds);

        // 3. 同步用户维度组合结果数据
        syncUserDimcombResults(orgId, xpdId, caliMeetId, userIds);

        // 4. 同步用户指标结果数据
        syncUserIndicatorResults(orgId, xpdId, caliMeetId, userIds);

        log.info(
            "同步盘点结果到校准会完成，orgId={}, xpdId={}, caliMeetId={}, userCount={}",
            orgId, xpdId, caliMeetId, userIds.size());
    }

    /**
     * 同步用户整体结果
     */
    private void syncUserResults(String orgId, String xpdId, String caliMeetId, List<String> userIds) {
        // 1. 查询用户的盘点结果
        List<XpdResultUserPO> xpdResults = xpdResultUserMapper.findByXpdIdAndUserIds(orgId, xpdId, userIds);

        if (CollectionUtils.isEmpty(xpdResults)) {
            log.info("未找到用户的盘点结果，跳过同步用户整体结果，orgId={}, xpdId={}", orgId, xpdId);
            return;
        }

        // 2. 将盘点结果转换为校准会结果
        List<CalimeetResultUserPO> datas = BeanCopierUtil.convertList(
            xpdResults, source -> {
                CalimeetResultUserPO target = new CalimeetResultUserPO();
                BeanCopierUtil.copy(source, target, false);
                target.setId(ApiUtil.getUuid());
                target.setCalimeetId(caliMeetId);
                return target;
            });

        // 3. 批量插入校准会结果
        if (CollectionUtils.isNotEmpty(datas)) {
            calimeetResultUserMapper.batchInsert(datas);
        }
    }

    /**
     * 同步用户维度结果
     */
    private void syncUserDimResults(String orgId, String xpdId, String caliMeetId, List<String> userIds) {
        // 1. 查询用户的盘点维度结果 (一次性查询所有用户)
        List<XpdResultUserDimPO> xpdDimResults = xpdResultUserDimMapper.findByXpdIdAndUserIds(orgId, xpdId, userIds);

        if (CollectionUtils.isEmpty(xpdDimResults)) {
            log.info("未找到用户的盘点维度结果，跳过同步用户维度结果，orgId={}, xpdId={}", orgId, xpdId);
            return;
        }

        // 2. 将盘点维度结果转换为校准会维度结果
        List<CalimeetResultUserDimPO> datas =
            BeanCopierUtil.convertList(
                xpdDimResults, source -> {
                    CalimeetResultUserDimPO target = new CalimeetResultUserDimPO();
                    BeanCopierUtil.copy(source, target, false);
                    target.setId(ApiUtil.getUuid());
                    target.setCalimeetId(caliMeetId);
                    return target;
                });

        // 3. 批量插入校准会维度结果
        if (CollectionUtils.isNotEmpty(datas)) {
            calimeetResultUserDimMapper.batchInsert(datas);
        }
    }

    /**
     * 同步用户维度组合结果
     */
    private void syncUserDimcombResults(String orgId, String xpdId, String caliMeetId, List<String> userIds) {
        // 1. 查询所有相关用户的盘点维度组合结果 (一次性查询)
        List<XpdResultUserDimcombPO> xpdDimcombResults =
            xpdResultUserDimcombMapper.findByXpdIdAndUserIds(orgId, xpdId, userIds);

        if (CollectionUtils.isEmpty(xpdDimcombResults)) {
            log.info("LOG21063:未找到用户的盘点维度组合结果，跳过同步，orgId={}, xpdId={}", orgId, xpdId);
            return;
        }

        // 2. 将盘点维度组合结果转换为校准会维度组合结果
        List<CalimeetResultUserDimcombPO> datas =
            BeanCopierUtil.convertList(
                xpdDimcombResults, source -> {
                    CalimeetResultUserDimcombPO target = new CalimeetResultUserDimcombPO();
                    BeanCopierUtil.copy(source, target, false);
                    target.setId(ApiUtil.getUuid());
                    target.setCalimeetId(caliMeetId);
                    return target;
                });

        // 3. 批量插入校准会维度组合结果
        if (CollectionUtils.isNotEmpty(datas)) {
            calimeetResultUserDimcombMapper.batchInsert(datas);
        }
    }

    /**
     * 同步用户指标结果
     */
    private void syncUserIndicatorResults(String orgId, String xpdId, String caliMeetId, List<String> userIds) {
        // 1. 收集所有用户的盘点指标结果
        List<XpdResultUserIndicatorPO> allXpdIndicatorResults =
            xpdResultUserIndicatorMapper.findByXpdIdAndUserIds(orgId, xpdId, userIds);

        if (CollectionUtils.isEmpty(allXpdIndicatorResults)) {
            log.info("未找到用户的盘点指标结果，跳过同步，orgId={}, xpdId={}", orgId, xpdId);
            return;
        }

        // 2. 将盘点指标结果转换为校准会指标结果
        List<CalimeetResultUserIndicatorPO> datas =
            BeanCopierUtil.convertList(
                allXpdIndicatorResults, source -> {
                    CalimeetResultUserIndicatorPO target = new CalimeetResultUserIndicatorPO();
                    BeanCopierUtil.copy(source, target, false);
                    target.setId(ApiUtil.getUuid());
                    target.setCalimeetId(caliMeetId);
                    return target;
                });

        // 3. 批量插入校准会指标结果
        if (CollectionUtils.isNotEmpty(datas)) {
            calimeetResultUserIndicatorMapper.batchInsert(datas);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCalimeetUser(String id, String orgId, String operatorId) {
        this.deleteCaliMeetUsers(Collections.singletonList(id), orgId, operatorId);
    }

    /**
     * 批量删除校准人员
     *
     * @param ids        校准人员ID列表
     * @param orgId      组织ID
     * @param operatorId 操作人ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCaliMeetUsers(List<String> ids, String orgId, String operatorId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 查询校准人员信息
        List<CalimeetUserPO> calimeetUsers = calimeetUserMapper.selectByIds(orgId, ids);
        if (CollectionUtils.isEmpty(calimeetUsers)) {
            return;
        }

        String caliMeetId = calimeetUsers.get(0).getCalimeetId();
        List<String> userIds = calimeetUsers.stream().map(CalimeetUserPO::getUserId).distinct().collect(Collectors.toList());

        // 批量逻辑删除校准人员
        calimeetUserMapper.batchLogicDeleteCaliMeetUsers(orgId, caliMeetId, userIds, operatorId);

        // 删除校准人员的校准结果
        calimeetRecordMapper.deleteByUserIds(orgId, caliMeetId, userIds, operatorId);
        calimeetRecordItemMapper.deleteByUserIds(orgId, caliMeetId, userIds, operatorId);

        // 删除校准人员的盘点结果快照
        calimeetResultUserMapper.deleteUserResults(orgId, caliMeetId, userIds);
        calimeetResultUserDimMapper.deleteUserDimResults(orgId, caliMeetId, userIds);
        calimeetResultUserDimcombMapper.deleteUserDimcombResults(orgId, caliMeetId, userIds);
        calimeetResultUserIndicatorMapper.deleteUserIndicatorResults(orgId, caliMeetId, userIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void refreshCaliMeetUserResult(String caliMeetId) {
        CalimeetPO calimeet = calimeetMapper.selectById(caliMeetId);
        if (calimeet == null) {
            return;
        }
        List<String> userIds = calimeetUserMapper.selectUserIdsByCaliMeetId(calimeet.getOrgId(), caliMeetId);
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        syncXpdResults(calimeet.getOrgId(), calimeet.getXpdId(), caliMeetId, userIds);
    }
}
