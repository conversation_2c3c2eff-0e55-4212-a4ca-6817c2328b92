package com.yxt.talent.rv.application.calimeet.expt;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultDto;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultWrapDto;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExportSupport;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExportSupportWithWaf;
import com.yxt.talent.rv.infrastructure.service.file.expt.FileExporter;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yxt.common.Constants.SDF_YEAR2SECOND;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil.dateToString;
import static com.yxt.talent.rv.infrastructure.service.file.FileConstants.FILE_SUFFIX_XLSX;
import static com.yxt.talent.rv.infrastructure.service.file.FileConstants.SHEET_1;
import static java.lang.String.format;
import static java.util.Arrays.stream;

/**
 * 校准人员数据导出器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaliMeetUserDataExporter extends FileExporter {
    public static final String LK_CALIMEET_USER_DATA_EXPT = "sprv:lk:calimeet:user:data:expt:%s:%s";
    private static final String TASK_NAME = "apis.sptalentrv.calimeet.user_data.export.file.name";
    private static final String HEADER_PREFIX = "apis.sptalentrv.calimeet.user_data.export.sheet1.";
    // 固定表头
    private static final String[] FIXED_HEADER_KEYS = {
        "fullname", "username", "status", "dept", "pos", "grade", "cali_status", "cali_time", "cali_user"};

    private final CalimeetMapper calimeetMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final I18nComponent i18nComponent;
    private final AuthService authService;
    private final MessageSourceService messageSourceService;
    private final SpsdAclServiceImpl spsdAclService;
    private final XpdMapper xpdMapper;
    private final XpdDimMapper xpdDimMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;

    /**
     * 导出校准人员数据
     *
     * @param caliMeetId 校准会ID
     * @param orgId      组织ID
     * @param operator   操作人
     * @param queryParam
     * @return 导出结果
     */
    public GenericFileExportVO export(String caliMeetId, String orgId, String operator, CaliMeetUserQuery queryParam) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (calimeet == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXIST);
        }

        DynamicExcelExportContent exportContent = buildExportContent(calimeet, queryParam);

        FileExportSupport fileExportSupport = FileExportSupportWithWaf.builder()
            .orgId(orgId)
            .operator(operator)
            .fileName(getFileName())
            .tranId(format(LK_CALIMEET_USER_DATA_EXPT, caliMeetId, operator))
            .outputStrategy(generateOutputStrategy())
            .exportContent(exportContent)
            .build();

        return toExport(fileExportSupport);
    }

    /**
     * 生成输出策略
     *
     * @return 输出策略
     */
    private OutputStrategy generateOutputStrategy() {
        return new AbstractExportStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                DynamicExcelExportContent results = (DynamicExcelExportContent) data;
                ExcelUtils.exportWithDynamicHeader(
                    results.getHeaders(), results.getSheets(), results.getData(), filePath);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                String taskName = CaliMeetUserDataExporter.this.i18nComponent.getI18nValue(TASK_NAME);
                return buildDownInfo(userCache, fileName, taskName);
            }
        };
    }

    /**
     * 获取文件名
     *
     * @return 文件名
     */
    private String getFileName() {
        return format(
            "校准人员数据导出_%s%s", dateToString(LocalDateTime.now(), FORMATTER_YYYY_MM_DD_HH_MM_SS),
            FILE_SUFFIX_XLSX);
    }

    /**
     * 构建导出内容
     *
     * @param calimeet   校准会
     * @param queryParam
     * @return 导出内容
     */
    private DynamicExcelExportContent buildExportContent(CalimeetPO calimeet, CaliMeetUserQuery queryParam) {
        DynamicExcelExportContent content = new DynamicExcelExportContent();

        // 获取xpd所有维度
        List<String> sdDimIds = getSdDimIds(calimeet);

        // 初始化表头
        content.setHeaders(initHeaders(calimeet, sdDimIds));
        // 初始化数据
        Map<String, List<Object>> dataMap = new HashMap<>();
        List<Object> dataList = new ArrayList<>(initDatas(calimeet, queryParam, sdDimIds));
        dataMap.put(SHEET_1, dataList);
        content.setData(dataMap);
        // 设置sheet
        content.setSheets(buildSingleSheets(SHEET_1, "校准人员数据"));
        return content;
    }

    private List<String> getSdDimIds(CalimeetPO calimeet) {
        List<XpdDimPO> xpdDims = xpdDimMapper.selectByXpdId(calimeet.getOrgId(), calimeet.getXpdId(), 0);
        // 获取所有维度ID
        Set<String> allDimIds = StreamUtil.map2set(xpdDims, XpdDimPO::getSdDimId);
        // 按照id排序
        return allDimIds.stream().filter(Objects::nonNull).distinct().sorted().toList();
    }

    /**
     * 初始化数据
     *
     * @param calimeet   校准会
     * @param queryParam
     * @param sdDimIds
     * @return 数据列表
     */
    private List<List<String>> initDatas(CalimeetPO calimeet, CaliMeetUserQuery queryParam, List<String> sdDimIds) {
        // 获取校准会下的所有用户
        String orgId = calimeet.getOrgId();
        List<CaliMeetUserVO> users = calimeetUserMapper.selectCaliMeetUsers(orgId, calimeet.getId(), queryParam);
        if (CollectionUtils.isEmpty(users)) {
            log.warn("LOG21173:{}", calimeet.getId());
            return Collections.emptyList();
        }

        Map<String, CaliMeetUserVO> caliInfoMap =
            users.stream().collect(Collectors.toMap(CaliMeetUserVO::getUserId, Function.identity(), (a, b) -> a));

        List<String> userIds = StreamUtil.mapList(users, CaliMeetUserVO::getUserId);
        // 获取校准前数据
        List<CalimeetResultUserDimPO> beforeCaliDatas =
            calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(orgId, calimeet.getId(), userIds);

        // 按用户ID和维度ID分组
        Map<String, Map<String, CalimeetResultUserDimPO>> beforeCaliDataMap = beforeCaliDatas.stream()
            .collect(Collectors.groupingBy(
                CalimeetResultUserDimPO::getUserId,
                Collectors.toMap(CalimeetResultUserDimPO::getSdDimId, po -> po, (a, b) -> a)));

        // 获取校准后数据
        Map<String, CaliUpdateUserResultWrapDto> afterCaliDataMap = new HashMap<>();
        for (CaliMeetUserVO record : users) {
            if (record != null && StringUtils.isNotBlank(record.getResultDetails())) {
                CaliUpdateUserResultWrapDto resultWrapDto =
                    BeanHelper.json2Bean(record.getCaliDetails(), CaliUpdateUserResultWrapDto.class);
                afterCaliDataMap.put(record.getUserId(), resultWrapDto);
            }
        }

        // 填充导出数据
        return fillExportDatas(userIds, sdDimIds, caliInfoMap, beforeCaliDataMap, afterCaliDataMap, calimeet);
    }

    /**
     * 填充导出数据
     *
     * @param userIds           用户ID列表
     * @param sdDimIds
     * @param caliInfoMap       用户基本信息Map
     * @param beforeCaliDataMap 校准前数据Map
     * @param afterCaliDataMap  校准后数据Map
     * @param calimeet          校准会
     * @return 导出数据列表
     */
    private List<List<String>> fillExportDatas(
        List<String> userIds, List<String> sdDimIds, Map<String, CaliMeetUserVO> caliInfoMap,
        Map<String, Map<String, CalimeetResultUserDimPO>> beforeCaliDataMap,
        Map<String, CaliUpdateUserResultWrapDto> afterCaliDataMap, CalimeetPO calimeet) {

        List<List<String>> datas = new ArrayList<>();

        // 获取所有可能用到的gridLevelId，用于批量查询
        Set<String> allGridLevelIds = new HashSet<>();
        if (calimeet.getCalimeetType() == 0) {
            // 收集校准前数据中的gridLevelId
            beforeCaliDataMap.values().forEach(dimMap -> 
                dimMap.values().stream()
                    .filter(dim -> StringUtils.isNotBlank(dim.getGridLevelId()))
                    .forEach(dim -> allGridLevelIds.add(dim.getGridLevelId()))
            );
            
            // 收集校准后数据中的gridLevelId
            afterCaliDataMap.values().stream()
                .filter(wrap -> wrap != null && CollectionUtils.isNotEmpty(wrap.getUserResults()))
                .flatMap(wrap -> wrap.getUserResults().stream())
                .filter(dim -> StringUtils.isNotBlank(dim.getGridLevelId()))
                .forEach(dim -> allGridLevelIds.add(dim.getGridLevelId()));
        }
        
        // 批量查询gridLevelId对应的名称
        Map<String, String> gridLevelNameMap = new HashMap<>();
        if (!allGridLevelIds.isEmpty()) {
            List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.selectByIds(new ArrayList<>(allGridLevelIds));
            if (CollectionUtils.isNotEmpty(gridLevels)) {
                gridLevels.forEach(level -> gridLevelNameMap.put(level.getId(), level.getLevelName()));
            }
        }

        for (String userId : userIds) {
            CaliMeetUserVO record = caliInfoMap.get(userId);
            if (record == null) {
                continue;
            }

            List<String> rowData = new ArrayList<>();
            // 添加固定列数据
            rowData.add(record.getFullname()); // 姓名
            rowData.add(record.getUsername()); // 账号
            rowData.add(record.getStatusDesc()); // 账号状态
            rowData.add(record.getDeptName()); // 部门
            rowData.add(record.getPositionName()); // 岗位
            rowData.add(record.getGradeName()); // 职级
            rowData.add(record.getCaliStatusStr()); // 校准状态
            rowData.add(LocalDateTimeUtil.format(record.getCaliTime(), SDF_YEAR2SECOND)); // 校准时间
            rowData.add(record.getCaliUserFullName()); // 校准人

            // 添加维度数据
            Map<String, CalimeetResultUserDimPO> beforeDimDataMap =
                beforeCaliDataMap.getOrDefault(userId, Collections.emptyMap());
            CaliUpdateUserResultWrapDto afterDimData = afterCaliDataMap.get(userId);

            for (String dimId : sdDimIds) {
                // 校准前数据
                CalimeetResultUserDimPO beforeDim = beforeDimDataMap.get(dimId);
                String beforeValue = "";
                if (beforeDim != null) {
                    // 根据校准类型获取不同的数据
                    if (calimeet.getCalimeetType() == 0) {
                        // 维度分层结果 - 通过gridLevelId查询对应的名称
                        String gridLevelId = beforeDim.getGridLevelId();
                        beforeValue = StringUtils.isNotBlank(gridLevelId) ? 
                            gridLevelNameMap.getOrDefault(gridLevelId, gridLevelId) : "";
                    } else if (calimeet.getCalimeetType() == 1) {
                        // 维度结果
                        beforeValue = formatDimValue(beforeDim.getScoreValue());
                    } else if (calimeet.getCalimeetType() == 2) {
                        // 指标结果
                        beforeValue = formatDimValue(beforeDim.getQualifiedPtg());
                    }
                }
                rowData.add(StringUtils.isBlank(beforeValue) ? "-" : beforeValue);

                // 校准后数据
                String afterValue = "-";
                if (afterDimData != null && afterDimData.getUserResults() != null) {
                    Optional<CaliUpdateUserResultDto> afterDim =
                        afterDimData.getUserResults().stream().filter(dim -> dimId.equals(dim.getSdDimId())).findFirst();
                    if (afterDim.isPresent()) {
                        // 根据校准类型获取不同的数据
                        if (calimeet.getCalimeetType() == 0) {
                            // 维度分层结果 - 通过gridLevelId查询对应的名称
                            String gridLevelId = afterDim.get().getGridLevelId();
                            afterValue = StringUtils.isNotBlank(gridLevelId) ? 
                                gridLevelNameMap.getOrDefault(gridLevelId, gridLevelId) : "";
                        } else if (calimeet.getCalimeetType() == 1) {
                            // 维度结果
                            afterValue = formatDimValue(afterDim.get().getScoreValue());
                        } else if (calimeet.getCalimeetType() == 2) {
                            // 指标结果
                            afterValue = formatDimValue(afterDim.get().getQualifiedPtg());
                        }
                    }
                }
                rowData.add(StringUtils.isBlank(afterValue) ? "-" : afterValue);
            }
            datas.add(rowData);
        }
        return datas;
    }

    /**
     * 格式化维度值
     *
     * @param value 维度值
     * @return 格式化后的值
     */
    private String formatDimValue(BigDecimal value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 初始化表头
     *
     * @param calimeet   校准会
     * @param sdDimIds
     * @return 表头
     */
    private Map<String, List<List<String>>> initHeaders(CalimeetPO calimeet, List<String> sdDimIds) {
        List<List<String>> headers = new ArrayList<>();
        Locale locale = authService.getLocale();

        // 添加固定表头
        stream(FIXED_HEADER_KEYS).forEach(key -> {
            String header = ApiUtil.getL10nString(messageSourceService, HEADER_PREFIX + key, locale);
            List<String> headerList = new ArrayList<>();
            headerList.add(header);
            headers.add(headerList);
        });

        Map<String, String> dimNameMap = getDimNameMap(calimeet.getOrgId(), calimeet.getXpdId(), sdDimIds);

        // 添加动态表头
        for (String dimId : sdDimIds) {
            String dimName = dimNameMap.getOrDefault(dimId, "维度" + dimId);

            // 校准前表头
            List<String> beforeHeader = new ArrayList<>();
            beforeHeader.add("校准前" + dimName);
            headers.add(beforeHeader);

            // 校准后表头
            List<String> afterHeader = new ArrayList<>();
            afterHeader.add("校准后" + dimName);
            headers.add(afterHeader);
        }

        return Map.of(SHEET_1, headers);
    }

    /**
     * 获取维度名称Map
     *
     * @param orgId  组织ID
     * @param xpdId
     * @param dimIds 维度ID集合
     * @return 维度名称Map
     */
    @Nonnull
    private Map<String, String> getDimNameMap(String orgId, String xpdId, List<String> dimIds) {
        // 获取xpd
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        if (xpd == null) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(dimIds)) {
            return Collections.emptyMap();
        }

        // 调用SPSD服务获取维度信息
        List<ModelBase4Facade> dimInfoList =
            spsdAclService.getDimInfoList(orgId, xpd.getModelId(), new ArrayList<>(dimIds));

        if (CollectionUtils.isEmpty(dimInfoList)) {
            return Collections.emptyMap();
        }

        // 转换为Map<维度ID, 维度名称>
        Map<String, String> result = new HashMap<>();
        for (ModelBase4Facade dimInfo : dimInfoList) {
            result.put(dimInfo.getDmId(), dimInfo.getDmName());
        }

        return result;
    }

}
