package com.yxt.talent.rv.application.calimeet.impt;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Lists;
import com.microsoft.schemas.vml.STTrueFalse;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.dto.PeriodExportResultDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliTempExportBean;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 动态表头抽象类
 */
@Component
@RequiredArgsConstructor
public abstract class AbstractExport extends AbstractExportStrategy {

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        exportWithDynamicHeader(data,  filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = getTaskName();
        return buildDownInfo(userCache, fileName, taskName);
    }

    public void exportWithDynamicHeader(Object dataBean, String filePath) throws
        IOException {
        File file = new File(filePath);
        boolean fileExist = file.exists();
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }

            fileExist = file.createNewFile();
        }
        if (!fileExist) {
            throw new FileNotFoundException("file not exist!");
        } else {
            ExcelWriterBuilder writerBuilder = EasyExcel.write(new FileOutputStream(file));
            ExcelWriter excelWriter = writerBuilder.build();

            WriteSheet sheet = EasyExcel.writerSheet("Sheet1").build();

            //1、设置备注信息
            /**
             * 1.1、 合并单元格 【四个参数】
             * 参数1：合并开始的第一行     【0：表示第一行】
             * 参数2：和平结束的最后一行   【0：表示第一行，0-0=0，表示没有合并行】
             * 参数3：合并开始的第一列     【0：表示第一列】
             * 参数4：合并开始的最后一列   【size-1：表示合并的列数与数据表格的列数一致】
             */
            OnceAbsoluteMergeStrategy remarkMergeStrategy = new OnceAbsoluteMergeStrategy(0, 0, 0, getRemardLastColumn(dataBean));
            //1.2、设置内容居中
            WriteCellStyle contentStyle = new WriteCellStyle();
            // 设置自动换行
            contentStyle.setWrapped(true);
            //垂直居中
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            WriteFont writeFont = new WriteFont();
            //字体大小为16
            writeFont.setFontHeightInPoints((short) 10);
            writeFont.setColor(IndexedColors.RED.index);
            contentStyle.setWriteFont(writeFont);
            // 单元格策略 参数1为头样式【不需要头部，设置为null】，参数2位表格内容样式
            HorizontalCellStyleStrategy remarkHorizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentStyle);

            //3、设置数据表格的样式
            //  ---------- 头部样式 ----------
            WriteCellStyle headStyle = new WriteCellStyle();
            // 字体样式
            WriteFont headFont = new WriteFont();
            headFont.setFontHeightInPoints((short) 9);
            headFont.setBold(true);
            headStyle.setWriteFont(headFont);
            // 背景颜色
            headStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            headStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);

            //  ---------- 内容样式 ----------
            WriteCellStyle bodyStyle = new WriteCellStyle();
            // 字体样式
            WriteFont bodyFont = new WriteFont();
            bodyFont.setFontHeightInPoints((short) 10);
            bodyStyle.setWriteFont(bodyFont);
            // 设置边框
            // bodyStyle.setBorderTop(BorderStyle.DOUBLE);
            bodyStyle.setBorderLeft(BorderStyle.THIN);
            bodyStyle.setBorderRight(BorderStyle.THIN);
            bodyStyle.setBorderBottom(BorderStyle.THIN);
            // 创建策略
            HorizontalCellStyleStrategy dataTableStrategy = new HorizontalCellStyleStrategy(headStyle, bodyStyle);

            /**
             * (4)、统一设置行高
             */
            // 设置表头行高【最上面的标题】  参数1：表头行高为0【不需要表头】    参数2：内容行高为28
            SimpleRowHeightStyleStrategy rowHeightStrategy1 = new SimpleRowHeightStyleStrategy((short) 0, (short) 28);
            // 设置数据表格的行高   null表示使用原来的行高
            SimpleRowHeightStyleStrategy rowHeightStrategy3 = new SimpleRowHeightStyleStrategy( null, (short) 18);


            // 生成表格1 ----页面中最上方的大标题
            WriteTable topicTable = EasyExcel.writerTable(0).registerWriteHandler(rowHeightStrategy1).registerWriteHandler(remarkMergeStrategy).registerWriteHandler(remarkHorizontalCellStyleStrategy).needHead(false).build();

            /**填写备注信息 */
            excelWriter.write(getRemarkDatas(), sheet, topicTable);

            /**表头信息 */
            WriteTable dataTable =
                EasyExcel.writerTable(1).registerWriteHandler(rowHeightStrategy3).registerWriteHandler(dataTableStrategy).head(getHeadDatas(dataBean)).needHead(Boolean.TRUE).build();
            /** 填写表头 */
            excelWriter.write(getDatas(dataBean), sheet, dataTable);
            excelWriter.finish();
        }
    }


    /**
     * 组装动态表头
     *
     * @param titleName 标题List
     * @param secondaryHeadTitleList 次标题List
     * @return java.util.List<java.lang.String>
     */
    public List<List<String>> concatDynamicHead(
        List<String> titleName, List<String> secondaryHeadTitleList) {
        List<List<String>> headTtileList = new ArrayList<>();
        if (CollectionUtils.isEmpty(titleName)) {
            return headTtileList;
        }
        titleName.forEach(
            t -> {
                if (CollectionUtils.isEmpty(secondaryHeadTitleList)) {
                    List<String> headTitle = new ArrayList<>();
                    headTitle.add(t);
                    headTtileList.add(headTitle);
                    return;
                }
                secondaryHeadTitleList.forEach(
                    x -> {
                        List<String> headTitle = new ArrayList<>();
                        headTitle.add(t);
                        headTitle.add(x);
                        headTtileList.add(headTitle);
                    });
            });
        return headTtileList;
    }

    public List<List<String>> concatDynamicHead(
        List<String> titleNames, List<String> secondaryHeadTitleList, List<String> thirdHeadTitleList) {
        List<List<String>> headTtileList = new ArrayList<>();
        if (CollectionUtils.isEmpty(titleNames)) {
            return headTtileList;
        }
        titleNames.forEach(
            t1 -> {
                if (CollectionUtils.isEmpty(secondaryHeadTitleList)) {
                    List<String> headTitle = new ArrayList<>();
                    headTitle.add(t1);
                    headTtileList.add(headTitle);
                    return;
                }
                secondaryHeadTitleList.forEach(
                    t2 -> {
                        if (CollectionUtils.isEmpty(thirdHeadTitleList)) {
                            List<String> headTitle = new ArrayList<>();
                            headTitle.add(t1);
                            headTitle.add(t2);
                            headTtileList.add(headTitle);
                            return;
                        }
                        thirdHeadTitleList.forEach(
                            x -> {
                                List<String> headTitle = new ArrayList<>();
                                headTitle.add(t1);
                                headTitle.add(t2);
                                headTitle.add(x);
                                headTtileList.add(headTitle);
                            });
                    });
            });
        return headTtileList;
    }

    abstract String getTaskName();
    abstract List<List<Object>> getRemarkDatas();
    abstract List<List<String>> getHeadDatas(Object dataBean);
    abstract List<List<Object>> getDatas(Object dataBean);
    abstract int getRemardLastColumn(Object dataBean);

}
