package com.yxt.talent.rv.application.calimeet.impt;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Lists;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.dto.PeriodExportResultDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliDmDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliTempExportBean;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserImportDTO;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维度分层结果导入模板
 */
@Component
@RequiredArgsConstructor
public class CaliDimLevelTemExportStrategy extends AbstractExport {
    public static final String TASK_NAME = "apis.sptalentrv.cali.dimlevel.template.export.file.name";

    private static final String REMARK = "apis.sptalentrv.cali.dimlevel.template.export.file.remark";
    private static final String USER_NAME = "apis.sptalentrv.cali.dimlevel.template.export.file.header.username";
    private static final String FULL_NAME = "apis.sptalentrv.cali.dimlevel.template.export.file.header.fullname";
    private static final String REASON = "apis.sptalentrv.cali.dimlevel.template.export.file.header.reason";
    private static final String RECOMMEND = "apis.sptalentrv.cali.dimlevel.template.export.file.header.recommend";
    private static final String ERROR = "apis.sptalentrv.cali.dim.template.export.file.header.error";

    private final I18nComponent i18nComponent;

    @Override
    String getTaskName() {
        return i18nComponent.getI18nValue(TASK_NAME);
    }

    @Override
    List<List<Object>> getRemarkDatas() {
        List<List<Object>> remarkData = new ArrayList<>();
        List<Object> remarkList = new ArrayList<>();
        remarkList.add(i18nComponent.getI18nValue(REMARK));
        remarkData.add(remarkList);
        return remarkData;
    }

    @Override
    List<List<String>> getHeadDatas(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;
            List<String> heads = new ArrayList<>();
            heads.add(i18nComponent.getI18nValue(FULL_NAME));
            heads.add(i18nComponent.getI18nValue(USER_NAME));
            Map<String, String> dmMap = errorDatas.get(0).getOriginDmResults();
            for (String dm : dmMap.keySet()){
                heads.add(dm);
            }
            heads.add(i18nComponent.getI18nValue(REASON));
            heads.add(i18nComponent.getI18nValue(RECOMMEND));
            heads.add(i18nComponent.getI18nValue(ERROR));
            return concatDynamicHead(heads, null);
        }


        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
        List<CaliDmDTO> dms = caliTempExportBean.getDms();
        List<String> heads = new ArrayList<>();
        heads.add(i18nComponent.getI18nValue(FULL_NAME));
        heads.add(i18nComponent.getI18nValue(USER_NAME));
        dms.forEach(dm -> {
            heads.add(dm.getDmName());
            heads.add("校准后"+dm.getDmName());
        });
        heads.add(i18nComponent.getI18nValue(REASON));
        heads.add(i18nComponent.getI18nValue(RECOMMEND));

        return concatDynamicHead(heads, null);
    }

    @Override
    List<List<Object>> getDatas(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;

            List<List<Object>> datas = new ArrayList<>();
            for (CaliUserImportDTO info : errorDatas) {
                List<Object> userData = new ArrayList<>();
                userData.add(info.getFullName());
                userData.add(info.getUserName());
                for (Map.Entry<String,String> entry : info.getOriginDmResults().entrySet()){
                    userData.add(Optional.ofNullable(entry.getValue()).orElse(""));
                }
                userData.add(info.getReason());
                userData.add(info.getRecommend());
                userData.add(info.getErrorMsg());
                datas.add(userData);
            }
            return datas;
        }

        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
//        List<String> dms = caliTempExportBean.getDms().stream().map(CaliDmDTO::getDmName).collect(Collectors.toList());
        List<CaliUserImportDTO> userDatas = caliTempExportBean.getUserDatas();
        List<List<Object>> datas = new ArrayList<>();
        for (CaliUserImportDTO info : userDatas) {
            List<Object> userData = new ArrayList<>();
            userData.add(info.getFullName());
            userData.add(info.getUserName());
            for (CaliDmDTO s : caliTempExportBean.getDms()){
                if (info.getDmResults() != null){
                    Map<String, String> dmResults = info.getDmResults();
                    userData.add(Optional.ofNullable(dmResults.get(s.getDmName())).orElse(""));
                }else{
                    userData.add("");
                }
                if (info.getDmCaliResults() != null){
                    Map<String, String> dmCaliResults = info.getDmCaliResults();
                    userData.add(Optional.ofNullable(dmCaliResults.get(s.getDmId())).orElse(""));
                }else{
                    userData.add("");
                }
            }
            userData.add(StringUtils.isBlank(info.getReason()) ? "" : info.getReason());
            userData.add(StringUtils.isBlank(info.getRecommend()) ? "" : info.getRecommend());
            datas.add(userData);
        }
        return datas;
    }

    @Override
    int getRemardLastColumn(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;
            Map<String, String> dmMap = errorDatas.get(0).getOriginDmResults();
            return dmMap.entrySet().size() + 4;
        }
        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
        List<CaliDmDTO> dms = caliTempExportBean.getDms();
        return dms.size() *  2 + 3;
    }

}
