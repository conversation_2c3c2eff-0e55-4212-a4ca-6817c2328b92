package com.yxt.talent.rv.application.calimeet.impt;

import com.google.common.collect.Lists;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.calimeet.dto.CaliIndicatorDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliTempExportBean;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserImportDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 维度指标结果导入模板
 */
@Component
@RequiredArgsConstructor
public class CaliIndicatorTemExportStrategy extends AbstractExport {
    public static final String TASK_NAME = "apis.sptalentrv.cali.indicator.template.export.file.name";

    private static final String REMARK = "apis.sptalentrv.cali.indicator.template.export.file.remark";
    private static final String USER_NAME = "apis.sptalentrv.cali.indicator.template.export.file.header.username";
    private static final String FULL_NAME = "apis.sptalentrv.cali.indicator.template.export.file.header.fullname";
    private static final String REASON = "apis.sptalentrv.cali.indicator.template.export.file.header.reason";
    private static final String RECOMMEND = "apis.sptalentrv.cali.indicator.template.export.file.header.recommend";
    private static final String CALBEFORE = "apis.sptalentrv.cali.indicator.template.export.file.cali.before";
    private static final String CALAFTER = "apis.sptalentrv.cali.indicator.template.export.file.cali.after";
    private static final String ERROR = "apis.sptalentrv.cali.indicator.template.export.file.header.error";

    private final I18nComponent i18nComponent;

    @Override
    String getTaskName() {
        return i18nComponent.getI18nValue(TASK_NAME);
    }

    @Override
    List<List<Object>> getRemarkDatas() {
        List<List<Object>> remarkData = new ArrayList<>();
        List<Object> remarkList = new ArrayList<>();
        remarkList.add(i18nComponent.getI18nValue(REMARK));
        remarkData.add(remarkList);
        return remarkData;
    }

    @Override
    List<List<String>> getHeadDatas(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;
            List<String> heads = new ArrayList<>();
            heads.add(i18nComponent.getI18nValue(FULL_NAME));
            heads.add(i18nComponent.getI18nValue(USER_NAME));
            Map<String, List<String>> headDatas = errorDatas.get(0).getHeadDatas();

            List<List<String>> userHeadTitle =
                concatDynamicHead(Lists.newArrayList(i18nComponent.getI18nValue(FULL_NAME),i18nComponent.getI18nValue(USER_NAME)), null);
            List<List<String>> headList = new ArrayList<>(userHeadTitle);

            headDatas.forEach((dm,indicators) -> {
                List<List<String>> dynamicHead =
                    concatDynamicHead(Lists.newArrayList(dm), indicators, Lists.newArrayList(i18nComponent.getI18nValue(CALBEFORE), i18nComponent.getI18nValue(CALAFTER)));
                headList.addAll(dynamicHead);
            });

            List<List<String>> reasonHeadTitle =
                concatDynamicHead(Lists.newArrayList(i18nComponent.getI18nValue(REASON),i18nComponent.getI18nValue(RECOMMEND),i18nComponent.getI18nValue(ERROR)), null);
            headList.addAll(reasonHeadTitle);
            return headList;
        }


        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
        LinkedHashMap<String, List<CaliIndicatorDTO>> dmIndicators = caliTempExportBean.getDmIndicators();

        List<List<String>> userHeadTitle =
            concatDynamicHead(Lists.newArrayList(i18nComponent.getI18nValue(FULL_NAME),i18nComponent.getI18nValue(USER_NAME)), null);
        List<List<String>> headList = new ArrayList<>(userHeadTitle);

        dmIndicators.forEach((dm,indicators) -> {
//            List<String> indicatorTitle = StreamUtil.mapList(indicators, CaliIndicatorDTO::getIndicatorName);
            List<String> indicatorTitle = new ArrayList<>();
            for (CaliIndicatorDTO caliIndicatorDTO : indicators){
                if (caliTempExportBean.getResultType() == 0){
                    indicatorTitle.add(caliIndicatorDTO.getTotalScore() != null ? caliIndicatorDTO.getIndicatorName()+"(总分"+caliIndicatorDTO.getTotalScore()+"分"+")": caliIndicatorDTO.getIndicatorName());
                }else{
                    indicatorTitle.add(caliIndicatorDTO.getIndicatorName());
                }
            }

            List<List<String>> dynamicHead =
                concatDynamicHead(Lists.newArrayList(dm), indicatorTitle, Lists.newArrayList(i18nComponent.getI18nValue(CALBEFORE), i18nComponent.getI18nValue(CALAFTER)));
            headList.addAll(dynamicHead);
        });

        List<List<String>> reasonHeadTitle =
            concatDynamicHead(Lists.newArrayList(i18nComponent.getI18nValue(REASON),i18nComponent.getI18nValue(RECOMMEND)), null);
        headList.addAll(reasonHeadTitle);

        return headList;
    }

    @Override
    List<List<Object>> getDatas(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;

            List<List<Object>> datas = new ArrayList<>();
            for (CaliUserImportDTO info : errorDatas) {
                List<Object> userData = new ArrayList<>();
                userData.add(info.getFullName());
                userData.add(info.getUserName());
                for (String data : info.getOriginIndicatorDatas()){
                    userData.add(Optional.ofNullable(data).orElse(""));
                }
                userData.add(info.getReason());
                userData.add(info.getRecommend());
                userData.add(info.getErrorMsg());
                datas.add(userData);
            }
            return datas;
        }

        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
        LinkedHashMap<String, List<CaliIndicatorDTO>> dmIndicators = caliTempExportBean.getDmIndicators();
        List<CaliUserImportDTO> userDatas = caliTempExportBean.getUserDatas();
        List<List<Object>> datas = new ArrayList<>();
        for (CaliUserImportDTO info : userDatas) {
            List<Object> userData = new ArrayList<>();
            userData.add(info.getFullName());
            userData.add(info.getUserName());
            Map<String, String> dmResults = info.getDmResults();
            Map<String, String> dmCaliResults = info.getDmCaliResults();
            // 遍历维度下指标结果
            dmIndicators.forEach((dm,indicators) -> {
                //indicators 必定会有
                for (CaliIndicatorDTO indicator : indicators){
                    String key = indicator.getIndicatorId();
                    if (dmResults != null){
                        userData.add(Optional.ofNullable(dmResults.get(key)).orElse(""));
                    }else {
                        userData.add("");
                    }
                    if (dmCaliResults != null){
                        userData.add(Optional.ofNullable(dmCaliResults.get(key)).orElse(""));
                    }else{
                        userData.add("");
                    }
                }
            });
            userData.add(StringUtils.isBlank(info.getReason()) ? "" : info.getReason());
            userData.add(StringUtils.isBlank(info.getRecommend()) ? "" : info.getRecommend());
            datas.add(userData);
        }
        return datas;
    }

    @Override
    int getRemardLastColumn(Object dataBean) {
        if (dataBean instanceof List<?>){
            List<CaliUserImportDTO> errorDatas = (List<CaliUserImportDTO>)dataBean;
            return errorDatas.get(0).getOriginIndicatorDatas().size()+4;
        }

        CaliTempExportBean caliTempExportBean = (CaliTempExportBean)dataBean;
        LinkedHashMap<String, List<CaliIndicatorDTO>> dmIndicators = caliTempExportBean.getDmIndicators();
        AtomicInteger columnsize = new AtomicInteger();
        dmIndicators.forEach((dm,indicators) -> {
            columnsize.getAndAdd(indicators.size()*2);
        });
        return columnsize.get() + 3;
    }

}
