package com.yxt.talent.rv.application.calimeet.impt;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 盘点项目校准会记录导出
 *
 * @date 2025/5/29
 */

@Component
@RequiredArgsConstructor
public class CaliXpdRecordExportStrategy extends AbstractExportStrategy {
    private final I18nComponent i18nComponent;

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        DynamicExcelExportContent prjResult = (DynamicExcelExportContent) data;
        ExcelUtils.exportWithDynamicHeader(
            prjResult.getHeaders(), prjResult.getSheets(), prjResult.getData(), filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue("apis.sptalentrv.cali.xpd.record.filename");
        return buildDownInfo(userCache, fileName, taskName);
    }
}
