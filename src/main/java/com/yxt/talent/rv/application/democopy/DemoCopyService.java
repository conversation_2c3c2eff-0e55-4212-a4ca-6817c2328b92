package com.yxt.talent.rv.application.democopy;

import com.yxt.aom.base.bean.common.ProjectCopyReq;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.project.component.AomProjectComponent;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.o2ofacade.bean.project.DemoCopyIDResp;
import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.remote.O2oAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talentbkfacade.service.TalentbkFacade;
import com.yxt.talentrvfacade.bean.EntityIdMap;
import com.yxt.ubiz.tree.domain.model.req.UTreeCopyReq;
import com.yxt.ubiz.tree.domain.model.resp.UTreeCopyResp;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.StreamUtil.list2map;
import static com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_DIMENSION_SETTING_ID;
import static com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_ID;
import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.AOM_ACTV_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.AOM_REF_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.O2O_TRAINING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPBK_POOL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_DIM_SETTING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_EVAL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_VALUE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_RULE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_XPD_IMPT_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_DIM_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_MODEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.UBIZ_TREE_ID;


@Slf4j
@Service
@RequiredArgsConstructor
public class DemoCopyService {

    private final DemoTableProvider demoTableProvider;
    private final AppProperties appProperties;
    private final SpmodelAclService spmodelAclService;
    private final O2oAclService o2oAclService;
    private final SpsdAclService spsdAclService;
    private final SpevalAclService spevalAclService;
    private final UTreeComponent uTreeComponent;
    private final TalentbkFacade talentbkFacade;
    private final AomProjectComponent aomProjectComponent;
    private final XpdMapper xpdMapper;

    /**
     * 奇点机构复制第一步
     *
     * @param orgInit
     */
    public void preGenIdMap(OrgInit4Mq orgInit) {
        log.info("LOG21393:");
        demoTableProvider.buildRunner(orgInit).preSetIdMapGen();
        log.info("LOG21403:");
//        List<String> strings = runner.deleteEntitySQL(orgInit.getTargetOrgId());
//        log.info("LOG21303:delete entity sql:{}", strings);
    }

    /**
     * 奇点机构复制第二步
     *
     * @param orgInit
     */
    public void demoCopy(OrgInit4Mq orgInit)  {
        log.info("LOG21413:");
        String sourceOrgId = orgInit.getSourceOrgId();
        String targetOrgId = orgInit.getTargetOrgId();
        String adminUserId = orgInit.getAdminUserId();

        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);

//        runner.setSkipInsert(false);

        log.info("LOG21363:");
        addTreeIdMap(sourceOrgId, targetOrgId, adminUserId, runner);
        addO2oIdMap(targetOrgId, runner);
        addSpbkIdMap(sourceOrgId, targetOrgId, runner);
        addSpsdIdMap(sourceOrgId, targetOrgId, runner);
        addSpevalIdMap(sourceOrgId, targetOrgId, runner);
        addSpmIdMap(targetOrgId, runner);

        log.info("LOG21373:");
        doCopyActv(sourceOrgId, targetOrgId, runner);

        //执行复制
        log.info("LOG21383:执行复制");
        runner.copyRun();
        log.info("LOG21423:demo copy end");
    }

    private void doCopyActv(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        List<XpdPO> xpds = xpdMapper.selectByOrgId(sourceOrgId);
        if (CollectionUtils.isEmpty(xpds)) {
            return;
        }

        Map<String, String> actvIdMap = new HashMap<>(8);
        Map<String, String> refIdMap = new HashMap<>(8);
        for (XpdPO xpd : xpds) {
            if (StringUtils.isBlank(xpd.getActvRegId())) {
                continue;
            }
            try {
                doCopyActvSingle(sourceOrgId, targetOrgId, runner, xpd, actvIdMap, refIdMap);
            } catch(Exception e) {
                log.error("LOG21433:", e);
            }
        }
        runner.addPreSetIdMap(AOM_ACTV_ID, actvIdMap);

        // 导入活动在有些表中也会作为refId存入，所以这里需要合并起来
        Map<String, String> imptIdMap = runner.queryPreSetStrIdMap(SPRV_XPD_IMPT_ID);
        refIdMap.putAll(imptIdMap);
        runner.addPreSetIdMap(AOM_REF_ID, refIdMap);
    }

    public void doCopyActvSingle(
        String sourceOrgId, String targetOrgId, DemoCopyRunner runner, XpdPO xpd, Map<String, String> actvIdMap,
        Map<String, String> refIdMap) {
        String srcActvId = xpd.getAomPrjId();
        ProjectCopyReq req = new ProjectCopyReq();
        req.setRegId(xpd.getActvRegId());
        req.setSrcOrgId(sourceOrgId);
        req.setSrcProjectId(srcActvId);
        req.setTgtOrgId(targetOrgId);
        req.setTgtModelId(runner.getIdMapValue(SPSD_MODEL_ID, xpd.getModelId()));
        req.setTgtCategoryId(runner.getIdMapValue(UBIZ_TREE_ID, xpd.getCategoryId()));
        log.debug("LOG21453:{}", BeanHelper.bean2Json(req, ALWAYS));
        req.setUserMap(runner.queryPreSetIdMap(UDP_USER_ID));
        Activity tgtActv = aomProjectComponent.demoCopy(req);
        log.debug("LOG21463:srcActvId={}, newActvId={}, newActvName={}", srcActvId, tgtActv.getId(), tgtActv.getActvName());
        String tgtActvId = tgtActv.getId();
        actvIdMap.put(srcActvId, tgtActvId);

        ApiUtil.sleep(3_000L);

        Map<String, String> demoCopyRefIdMap =
            aomProjectComponent.getDemoCopyRefIdMap(
                UacdTypeEnum.PRJ_XPD.getRegId(), sourceOrgId, targetOrgId,
                srcActvId, tgtActvId);
        log.info("LOG21443:{}", demoCopyRefIdMap);
        refIdMap.putAll(demoCopyRefIdMap);
    }

    private void addSpbkIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        Map<String, String> poolIdMap = talentbkFacade.demoCopyIdMap(sourceOrgId, targetOrgId, SPBK_POOL_ID);
        runner.addPreSetIdMap(SPBK_POOL_ID, poolIdMap);
    }

    private void addTreeIdMap(String sourceOrgId, String targetOrgId, String adminUserId, DemoCopyRunner runner) {
        // 先删后增
        xpdMapper.clearOrgTreeNodes(targetOrgId);
        xpdMapper.clearOrgTreeNodeRelations(targetOrgId);
        xpdMapper.clearOrgTreeActionPermissions(targetOrgId);

        adminUserId = Optional.ofNullable(adminUserId).orElse("demoCopy");
        UTreeCopyReq copyReq = new UTreeCopyReq();
        copyReq.setSourceOrgId(sourceOrgId);
        copyReq.setTargetOrgId(targetOrgId);
        copyReq.setAdminUserId(adminUserId);
        Map<String, String> userIdMap = runner.queryPreSetStrIdMap(UDP_USER_ID);
        List<UTreeCopyReq.UserIdPair> userIdPairs = userIdMap.entrySet().stream().map(entry -> {
            String sourceUserId = entry.getKey();
            String targetUserId = entry.getValue();
            UTreeCopyReq.UserIdPair userIdPair = new UTreeCopyReq.UserIdPair();
            userIdPair.setSourceUserId(sourceUserId);
            userIdPair.setTargetUserId(targetUserId);
            return userIdPair;
        }).toList();
        copyReq.setUserIdPairs(userIdPairs);

        List<UTreeCopyResp> uNodeDemoResps = uTreeComponent.copyTrees(copyReq);
        Map<String, String> nodeIdMap = new HashMap<>();
        for (UTreeCopyResp node : uNodeDemoResps) {
            if (UTreeEnum.XPD_BASE.getTreeId().equals(node.getTreeId())) {
                Map<String, String> sourceMap = node.getNodeIdPairs();
                nodeIdMap.putAll(sourceMap);
                break;
            }
        }
        runner.addPreSetIdMap(UBIZ_TREE_ID, nodeIdMap);
    }

    private void addSpevalIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        // 测评方案id
        Map<String, String> entityIdMapping = spevalAclService.getEntityIdMap(sourceOrgId, targetOrgId, MAP_KEY_SPEVAL_EVALUATION_ID.getIdMapKey());
        runner.addPreSetIdMap(SPEVAL_EVAL_ID, entityIdMapping);

        // 测评维度id(上级，下级，平级，自定义维度id)
        Map<String, String> settingIdMapping = spevalAclService.getEntityIdMap(sourceOrgId, targetOrgId, MAP_KEY_SPEVAL_EVALUATION_DIMENSION_SETTING_ID.getIdMapKey());
        runner.addPreSetIdMap(SPEVAL_DIM_SETTING_ID, settingIdMapping);
    }

    private void addSpsdIdMap(String sourceOrgId, String targetOrgId, DemoCopyRunner runner) {
        runner.addPreSetIdMap(SPSD_MODEL_ID,
            spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SPSD_MODEL_ID));
        runner.addPreSetIdMap(SPSD_DIM_ID,
            spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SPSD_DIM_ID));
        runner.addPreSetIdMap(SPSD_INDICATOR_ID,
            spsdAclService.getDemoOrgMapping(sourceOrgId, targetOrgId, SPSD_INDICATOR_ID));

//        runner.addPreSetIdMap(SPSD_MODEL_ID,
//            new HashMap());
//        runner.addPreSetIdMap(SPSD_DIM_ID,
//            new HashMap());
//        runner.addPreSetIdMap(SPSD_INDICATOR_ID,
//            new HashMap());

    }

    private void addO2oIdMap(String targetOrgId, DemoCopyRunner runner) {
        // 0:查询项目 1:查询培训计划
        int queryType = 0;
        List<DemoCopyIDResp> demoCopyNewId = o2oAclService.getDemoCopyNewId(targetOrgId, queryType);
        Map<Long, Long> idMapping =
                list2map(demoCopyNewId, DemoCopyIDResp::getOldId, DemoCopyIDResp::getNewId);
        runner.addPreSetIdMap(O2O_TRAINING_ID, idMapping);
    }

    private void addSpmIdMap(String targetOrgId, DemoCopyRunner runner) {
        OrgDemoIdMappingVO orgDemoIdMappingVO = spmodelAclService.getOrgDemoIdMappingVO(targetOrgId);
        runner.addPreSetIdMap(SPM_LABEL_ID, orgDemoIdMappingVO.getLabelIdMap());
        runner.addPreSetIdMap(SPM_INDICATOR_ID, orgDemoIdMappingVO.getIndicatorIdMap());
        runner.addPreSetIdMap(SPM_LABEL_VALUE_ID, orgDemoIdMappingVO.getLabelValueIdMap());
        runner.addPreSetIdMap(SPM_RULE_ID, orgDemoIdMappingVO.getRuleIdMap());

//        runner.addPreSetIdMap(SPM_LABEL_ID, new HashMap());
//        runner.addPreSetIdMap(SPM_INDICATOR_ID, new HashMap());
//        runner.addPreSetIdMap(SPM_LABEL_VALUE_ID, new HashMap());
//        runner.addPreSetIdMap(SPM_RULE_ID, new HashMap());
    }

    @SuppressWarnings("rawtypes")
    public EntityIdMap getEntityIdMap(String sourceOrgId, String targetOrgId) {
        List<String> idMapKeys =
                Optional.ofNullable(appProperties.getIdMapKeys()).orElse(Collections.emptyList());

        OrgInit4Mq orgInit = new OrgInit4Mq();
        orgInit.setSourceOrgId(sourceOrgId);
        orgInit.setTargetOrgId(targetOrgId);
        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
//        runner.preSetIdMapGen();

        Map<String, Map> idMap = new HashMap<>();
        idMapKeys.forEach(key -> {
            Map map = runner.queryPreSetIdMap(key);
            idMap.put(key, map);
        });
        return new EntityIdMap(idMap);
    }
}
