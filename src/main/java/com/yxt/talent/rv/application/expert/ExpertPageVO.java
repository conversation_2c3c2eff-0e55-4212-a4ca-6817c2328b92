package com.yxt.talent.rv.application.expert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class ExpertPageVO {
    /**
     * 账号
     */
    @Schema(
        description = "账号",
        example = "sok"
    )
    private String username;
    /**
     * 姓名
     */
    @Schema(
        description = "姓名",
        example = "张三"
    )
    private String fullname;
    /**
     * 用户ID
     */
    private String userId;

    @Schema(
        description = "专家所在部门"
    )
    private String deptName;

    @Schema(
        description = "专家表主键"
    )
    private String id;

    @Schema(
        description = "认证指标"
    )
    private List<String> indicators;

    @Schema(
        description = "认证部门"
    )
    private List<String> depts;

    @Schema(
        description = "认证指标编号"
    )
    private List<String> indicatorNums;

    private String positionName;

    private List<String> deptIds;
}
