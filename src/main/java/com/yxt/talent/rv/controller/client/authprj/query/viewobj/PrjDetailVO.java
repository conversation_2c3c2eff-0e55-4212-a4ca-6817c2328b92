package com.yxt.talent.rv.controller.client.authprj.query.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PrjDetailVO {
    @Schema(description = "主键id")
    private String id;

    @Schema(description = "活动/项目的具体类型(UACD注册表中定义)")
    private String actvRegId;

    /**
     * 活动/项目名称
     */
    @Schema(description = "活动/项目名称")
    private String actvName;

    @Schema(description = "状态 1-未发布, 2-进行中, 3-已结束,")
    private Integer actvStatus;

    /**
     * 时间模式(0-固定, 1-相对)
     */
    @Schema(description = "时间模式 0-固定, 1-相对")
    private Integer timeModel;

    /**
     * 固定开始时间
     */
    @Schema(description = "固定开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    /**
     * 固定截止时间
     */
    @Schema(description = "固定截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    /**
     * 相对截止天数
     */
    @Schema(description = "相对截止天数")
    private Integer endDayOffset;
    /**
     * 简介
     */
    @Schema(description = "简介")
    private String description;

}
