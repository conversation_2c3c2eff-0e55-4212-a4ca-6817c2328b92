package com.yxt.talent.rv.controller.facade;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.bean.common.Activity4Create;
import com.yxt.aom.base.component.common.AomActivityComponent;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.common.annotation.Auth;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.authprj.AuthPrjService;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeForAuthPrjEnum;
import com.yxt.talent.rv.controller.facade.cmd.AuthProject4Create;
import com.yxt.talent.rv.controller.facade.viewobj.AuthPrjDTO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yxt.common.enums.AuthType.AKSK;
import static com.yxt.common.enums.AuthType.CUSTOM;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("")
@Tag(name = "facade接口-认证项目相关接口", description = "facade接口-认证项目相关接口")
public class AuthprjFacadeController {
    private final AomActivityComponent activityComponent;
    private final UTreeComponent uTreeComponent;
    private final AuthPrjService authPrjService;

    @Operation(summary = "学习地图上创建认证项目：项目名称为岗位名称+认证项目，负责人为当前创建人，周期项目，周期默认30天。")
    @PostMapping(value = "/facade/authprj/create", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(type = {AKSK, CUSTOM})
    @ResponseBody
    public AuthPrjDTO createAuthprj(@Validated @RequestBody AuthProject4Create bean) {
        String orgId = bean.getOrgId();
        String opUserId = bean.getOpUserId();
        Activity4Create activity4Create = new Activity4Create();
        BeanHelper.copyProperties(bean, activity4Create);
        activity4Create.setMgrUserIds(bean.getPrjMgrs());
        activity4Create.setActvType(2);
        activity4Create.setActvRegId(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
        activity4Create.setOrgId(orgId);

        if (CollectionUtils.isEmpty(activity4Create.getMgrUserIds())) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_MGR_EMPTY);
        }
        if (activity4Create.getMgrUserIds().size() > 30) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_PROJECT_MANAGER_MAX);
        }

        // 设置根目录的categoryId
        String categoryId = uTreeComponent.getRootNodeId(orgId, UTreeEnum.AUTHPRJ_BASE.getTreeId());
        if (StringUtils.isBlank(categoryId)) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_CATEGORYID_EMPTY);
        }
        activity4Create.setCategoryId(categoryId);

        log.info("Activity4Create bean:{}", JSON.toJSONString(activity4Create));
        UserCacheDetail user = new UserCacheDetail();
        user.setOrgId(orgId);
        user.setUserId(opUserId);
        user.setAdmin(bean.getAdmin());
        String aomId = activityComponent.createActivity(user, activity4Create);
        return authPrjService.getAuthInfoByAomId(orgId, aomId);
    }


    @Operation(summary = "获取认证项目明细")
    @GetMapping(value = "/facade/authprj/detail")
    @Auth(type = {AKSK, CUSTOM})
    @ResponseBody
    public AuthPrjDTO getInfoByAuthPrjId(@RequestParam String orgId,@RequestParam String authPrjId) {
        Activity activity = authPrjService.getActivity(orgId, authPrjId);
        AuthPrjDTO authPrjDTO = new AuthPrjDTO();
        authPrjDTO.setAuthPrjId(authPrjId);
        authPrjDTO.setAomId(activity.getId());
        authPrjDTO.setStatus(activity.getActvStatus());
        return authPrjDTO;
    }

}
