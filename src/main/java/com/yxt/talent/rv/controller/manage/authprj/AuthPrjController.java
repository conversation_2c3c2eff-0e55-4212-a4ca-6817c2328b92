package com.yxt.talent.rv.controller.manage.authprj;

import com.google.common.collect.Lists;
import com.yxt.aom.base.bean.arrange.ActivityArrangeItem4ActvMgr;
import com.yxt.aom.base.bean.common.*;
import com.yxt.aom.base.controller.common.ActivityController;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.AuthType;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.application.activity.RvAomActivityService;
import com.yxt.talent.rv.application.authprj.AuthPrjService;
import com.yxt.talent.rv.application.authprj.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.ProjectSourceTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeForAuthPrjEnum;
import com.yxt.talent.rv.controller.BaseController;
import com.yxt.talent.rv.controller.facade.viewobj.AuthPrjVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjDetailVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.talent.rv.infrastructure.persistence.cache.RedisRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Tag(name = "认证项目接口")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/mgr/auth_prj")
public class AuthPrjController extends BaseController {
    private final ActivityController activityController;
    private final AuthService authService;
    private final AuthPrjService authPrjService;
    private final RvAomActivityService aomActivityService;
    private final SptalentsdFacade spTalentSdFacade;
    private final UTreeComponent uTreeComponent;
    private final AuthprjMapper authprjMapper;
    private final static String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";
    private final RedisRepo redisRepo;

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "项目列表")
    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<AuthProject4Get> searchPrjs(HttpServletRequest request, @RequestBody AuthPrj4Search bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        PagingList<Activity4List> pagingList =
            activityController.searchActivity(request, getReq(userCacheDetail.getOrgId(), bean));

        //6.4 负责人删除也需要显示
        List<Activity4List> pagingListDatas = pagingList.getDatas();
        List<AuthProject4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingListDatas));

        if (CollectionUtils.isNotEmpty(pagingListDatas)) {
            List<String> aomIds = StreamUtil.mapList(pagingListDatas, Activity4List::getId);
            List<AuthprjPO> authPrjs = authPrjService.findAuthPrjsByAomIds(userCacheDetail.getOrgId(), aomIds);
            Map<String, AuthprjPO> authprjPOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(authPrjs)) {
                authprjPOMap = StreamUtil.list2map(authPrjs, AuthprjPO::getAomPrjId);
            }

            //分类处理
            List<String> categoryIds = StreamUtil.mapList(pagingListDatas, Activity4List::getCategoryId);
            Map<String, String> categoryMap =
                uTreeComponent.getNodesName(userCacheDetail.getOrgId(), UTreeEnum.AUTHPRJ_BASE.getTreeId(),categoryIds);
            Iterator var6 = pagingListDatas.iterator();
            while (var6.hasNext()) {
                Activity4List obj = (Activity4List) var6.next();
                AuthProject4Get get = new AuthProject4Get();
                BeanHelper.copyProperties(obj, get);
                if (categoryMap != null){
                    get.setCategoryName(categoryMap.getOrDefault(obj.getCategoryId(), ""));
                }
                if (authprjPOMap.containsKey(obj.getId())){
                    get.setAuthPrjId(authprjPOMap.get(obj.getId()).getId());
                }
                resultList.add(get);
            }
        }

        PagingList<AuthProject4Get> getPagingList = new PagingList<>();
        getPagingList.setPaging(pagingList.getPaging());
        getPagingList.setDatas(resultList);
        return getPagingList;
    }

    @Operation(summary = "项目创建")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseBody
    @DbHintMaster
    public AuthPrjVO add(HttpServletRequest request, @Validated @RequestBody AuthProjectDto bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        validateDate(bean);
        Activity4Create activity4Create = new Activity4Create();
        BeanHelper.copyProperties(bean, activity4Create);
        activity4Create.setMgrUserIds(bean.getPrjMgrs());
        activity4Create.setActvType(2);
        activity4Create.setSourceType(ProjectSourceTypeEnum.PRJ_AUTH.getCode());
        activity4Create.setActvRegId(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());

        if (CollectionUtils.isEmpty(activity4Create.getMgrUserIds())) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_MGR_EMPTY);
        }
        if (activity4Create.getMgrUserIds().size() > 30) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_PROJECT_MANAGER_MAX);
        }
        if (activity4Create.getTimeModel() == 1){
            activity4Create.setAutoEnd(0);
        }
        ResponseEntity<String> responseEntity = activityController.create(request, activity4Create);
        String id = responseEntity.getBody();
        AuthPrjVO authPrjDTO = new AuthPrjVO();
        authPrjDTO.setId(id);
        AuthprjPO authprjPO = authprjMapper.selectByAomPrjId(userCacheDetail.getOrgId(), id);
        if (authprjPO != null){
            authPrjDTO.setAuthPrjId(authprjPO.getId());
        }
        return authPrjDTO;
    }

    @Operation(summary = "项目编辑")
    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @PutMapping(value = "/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.TOKEN})
    public void edit(
        HttpServletRequest request, @PathVariable String id, @Validated @RequestBody AuthProjectDto bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        validateDate(bean);
        Activity4Update activity4Update = new Activity4Update();
        BeanHelper.copyProperties(bean, activity4Update);
        activity4Update.setOrgId(userCacheDetail.getOrgId());
        activity4Update.setActvRegId(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
        activity4Update.setId(id);
        activity4Update.setMgrUserIds(bean.getPrjMgrs());
        if (CollectionUtils.isEmpty(activity4Update.getMgrUserIds())) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_MGR_EMPTY);
        }
        if (activity4Update.getMgrUserIds().size() > 30) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_PROJECT_MANAGER_MAX);
        }
        activity4Update.setCopyFields(new String[] {
            "actvName","categoryId","actvType","timeModel","startTime","endTime","description","endDayOffset",
            "modelId","autoEnd","mgrUserIds"
        });
        activityController.update(request, id, activity4Update);
    }

    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @Operation(summary = "项目详情")
    @GetMapping(value = "/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public AuthPrjDetailVO detail(@PathVariable String id) {
        Activity4Get activity4Get = activityController.getActivity4Mgr(id, UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
        if (StringUtils.isBlank(activity4Get.getId())) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_NOT_EXIST);
        }
        AuthPrjDetailVO authPrjDetailVO = new AuthPrjDetailVO();
        BeanHelper.copyProperties(activity4Get, authPrjDetailVO);
        if (StringUtils.isNotBlank(activity4Get.getModelId())){
            ModelInfo modelInfo = spTalentSdFacade.getModelInfo(activity4Get.getOrgId(), authPrjDetailVO.getModelId());
            if (modelInfo != null) {
                authPrjDetailVO.setModelName(modelInfo.getTitle());
            }
        }

        if (StringUtils.isNotBlank(activity4Get.getCategoryId())) {
            Map<String, String> categoryMap =
                uTreeComponent.getNodesName(activity4Get.getOrgId(), UTreeEnum.AUTHPRJ_BASE.getTreeId(),
                    Lists.newArrayList(activity4Get.getCategoryId()));
            authPrjDetailVO.setCategoryName(categoryMap.getOrDefault(activity4Get.getCategoryId(), ""));
        }

        return authPrjDetailVO;
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "认证项目活动列表")
    @PostMapping(value = "/actv/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<ActivityArrangeItem4ActvMgr> searchActivitys(HttpServletRequest request, @RequestBody AuthProjectTask4Get bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        Activity activity = authPrjService.getActivity(userCacheDetail.getOrgId(), bean.getAuthPrjId());
        return aomActivityService.listArrange(ApiUtil.getPageRequest(request), userCacheDetail.getOrgId(), activity.getId(), bean.getRegId(), bean.getKeyword());
    }

    public void validateDate(AuthProjectDto bean){
        if (bean.getTimeModel() == null){
            throw new ApiException(ExceptionKeys.AUTHPRJ_TIMEMODE_EMPTY);
        }
        if (bean.getTimeModel() == 0){
            // 固定周期
            Date start = bean.getStartTime();
            Date end = bean.getEndTime();
            if (start == null){
                throw new ApiException(ExceptionKeys.AUTHPRJ_STARTTIME_EMPTY);
            }
            if (end == null){
                throw new ApiException(ExceptionKeys.AUTHPRJ_ENDTIME_EMPTY);
            }
            Date now = new Date();
            if (end.compareTo(now) < 0){
                throw new ApiException(ExceptionKeys.AUTHPRJ_STARTTIME_BEFORE);
            }
            if (end.compareTo(start) <= 0) {
                throw new ApiException(ExceptionKeys.AUTHPRJ_DATE_RANGE_ERROR);
            }
        }
        if (bean.getTimeModel() == 1){
            if (bean.getEndDayOffset() == null) {
                throw new ApiException(ExceptionKeys.AUTHPRJ_TIME_EMPTY);
            }
        }
    }

    private ActivitySearchReq getReq(String orgId, AuthPrj4Search bean) {
        ActivitySearchReq activitySearchReq = new ActivitySearchReq();
        BeanHelper.copyProperties(bean, activitySearchReq);
        activitySearchReq.setActvRegId(UacdTypeForAuthPrjEnum.PRJ_AUTH.getRegId());
        activitySearchReq.setActvType(2);
        if (bean.getPrjStatus() != null) {
            activitySearchReq.setActvStatusSet(Set.of(bean.getPrjStatus()));
        }

        if (StringUtils.isNotBlank(bean.getCategoryId())) {
            List<String> categoryIds =
                uTreeComponent.getChildrenNodeIds(orgId, UTreeEnum.AUTHPRJ_BASE.getTreeId(), bean.getCategoryId());
            activitySearchReq.setCategoryIds(new HashSet<>(categoryIds));
        }

        return activitySearchReq;
    }

}
