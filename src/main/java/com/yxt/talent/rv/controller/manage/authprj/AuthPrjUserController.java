package com.yxt.talent.rv.controller.manage.authprj;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.aom.base.annotation.ConvertTimePage;
import com.yxt.aom.base.bean.md.AomActivityPartMember4Get;
import com.yxt.aom.base.bean.part.PartMember4List;
import com.yxt.aom.base.bean.part.PartMemberReq;
import com.yxt.aom.base.controller.part.ActivityPartMemberController;
import com.yxt.aom.base.custom.CustomActivityPartMemberCompo;
import com.yxt.aom.common.util.AomBeanNameUtils;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.spsdk.common.bean.UserBasicInfo;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.authprj.AuthPrjService;
import com.yxt.talent.rv.application.authprj.AuthPrjUserAppService;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjChangeUserResDTO;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjRestart4Post;
import com.yxt.talent.rv.application.authprj.dto.AuthRepeatDto;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjRuleLevelPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "认证项目-员工", description = "认证项目-员工")
@RequestMapping(value = "/mgr/authprj/{authprjId}/user")
public class AuthPrjUserController {

    private final AuthPrjUserAppService authPrjUserAppService;
    private final AuthService authService;
    private final AuthPrjService authPrjService;
    private final ActivityPartMemberController activityPartMemberController;

    @Operation(summary = "员工报告-统计（总得分，认证进度，认证分层结果，获得证书个数）")
    @GetMapping(value = "/{userId}/statistics")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public AuthPrjUserUserStatisticsVO getUserStatistics(@PathVariable String authprjId, @PathVariable String userId) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        return authPrjUserAppService.getUserStatistics(userCache.getOrgId(), authprjId, userId);
    }

    @Operation(summary = "员工报告-认证指标结果明细")
    @GetMapping(value = "/{userId}/indicators")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public List<AuthPrjUserReportVO> getUserIndicatorDetails(@PathVariable String authprjId, @PathVariable String userId) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        return authPrjUserAppService.getUserIndicatorDetails(userCache.getOrgId(), authprjId, userId);
    }


    @Parameters({
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "项目参与成员列表")
    @PostMapping(value = {"/listpage/search"}, consumes = {"application/json"}, produces = {"application/json"})
    @ResponseBody
    @Auth(action = 2, type = {AuthType.TOKEN})
    @ConvertTimePage
    public PagingList<AuthPrjPartMember4List> pageList(HttpServletRequest request, @PathVariable String authprjId, @RequestBody PartMemberReq req) {
        log.info("分页查询 req={}", JSON.toJSONString(req));
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        PagingList<PartMember4List> pagingList = activityPartMemberController.groupMembers(request, req);
        List<PartMember4List> pagingListDatas = pagingList.getDatas();
        List<AuthPrjPartMember4List> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingListDatas));


        if (CollectionUtils.isNotEmpty(pagingListDatas)) {
            List<String> userIds = StreamUtil.mapList(pagingListDatas, PartMember4List::getUserId);
            Map<String, AuthprjRuleLevelPO> levalMap = authPrjUserAppService.getUserLevalMap(userCacheDetail.getOrgId(), authprjId, userIds);

            Iterator var7 = pagingListDatas.iterator();
            while(var7.hasNext()) {
                PartMember4List obj = (PartMember4List)var7.next();
                AuthPrjPartMember4List get = new AuthPrjPartMember4List();
                BeanHelper.copyProperties(obj, get);

                AuthprjRuleLevelPO authprjRuleLevelPO = levalMap.get(obj.getUserId());
                if (authprjRuleLevelPO != null){
                    get.setPrjResult(authprjRuleLevelPO.getLevelName());
                }

                resultList.add(get);
            }
        }

        PagingList<AuthPrjPartMember4List> getPagingList = new PagingList();
        getPagingList.setPaging(pagingList.getPaging());
        getPagingList.setDatas(resultList);
        return getPagingList;
    }

    @Operation(summary = "项目是否支持指派重学")
    @GetMapping(value = "/canrestart")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseBody
    public AuthRepeatDto canRestart(@PathVariable String authprjId) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return authPrjService.canReStartPrj(userCacheDetail.getOrgId(), authprjId);
    }

    @Operation(summary = "指定用户进行指派重新")
    @PostMapping(value = "/restart")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseStatus(OK)
    public void restartPrj(@PathVariable String authprjId, @RequestBody AuthPrjRestart4Post authPrjRestart4Post) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        authPrjService.reStartPrj(userCacheDetail.getOrgId(), userCacheDetail.getUserId(), authprjId, authPrjRestart4Post.getUserIds());
    }

    @Operation(summary = "历史认证列表")
    @GetMapping(value = "/userhistory/{userId}")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseBody
    public AuthPrjUserHistoryVO getUserHistory(@PathVariable String authprjId, @PathVariable String userId) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return authPrjUserAppService.getUserHistory(userCacheDetail.getOrgId(), authprjId, userId);
    }

    @Operation(summary = "用户认证明细")
    @GetMapping(value = "/authresult/{userId}")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseBody
    public AuthPrjUserResultVO getUserAuthResult(@PathVariable String authprjId, @PathVariable String userId) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return authPrjUserAppService.getUserAuthResult(userCacheDetail.getOrgId(), authprjId, userId);
    }

    @Operation(summary = "修改认证结果")
    @PostMapping(value = "/changeresult")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseStatus(OK)
    public void changeAuthResult(@RequestBody AuthPrjChangeUserResDTO authPrjChangeUserResDTO) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        authPrjUserAppService.changeAuthResult(userCacheDetail.getOrgId(),userCacheDetail.getUserId(), authPrjChangeUserResDTO);
    }

    @Operation(summary = "认证结果修改记录")
    @GetMapping(value = "/chglog/{userId}")
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @ResponseBody
    public List<AuthPrjUserChgLogVO> getChangeLog(@PathVariable String authprjId, @PathVariable String userId) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return authPrjUserAppService.getChgLog(userCacheDetail.getOrgId(), authprjId, userId);
    }

}
