package com.yxt.talent.rv.infrastructure.common.constant;

import cn.hutool.core.lang.Pair;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;


@UtilityClass
public final class AppConstants {
    public static final String DB_0_TRANSACTION_MANAGER = "db0TransactionManager";
    public static final String RV_TRANSACTION_MANAGER = DB_0_TRANSACTION_MANAGER;
    /* 可迁移资源code */
    public static final String TRANSFERABLE_RESOURCES_CODE_PRJ = "sprv_inventory";
    public static final String TRANSFERABLE_RESOURCES_CODE_DMP = "sprv_dynamic_inventory";
    public static final String TRANSFERABLE_RESOURCES_CODE_CALI_MEET = "sprv_calibrations";
    public static final Pair<Long, TimeUnit> DEMO_COPY_RUN_DATA_KEEP_TIME = Pair.of(10L, TimeUnit.DAYS);
    public static final String CODE_OPERATOR_ID = "javaapi0-0000-0000-0000-000000000000";
    public static final String SAAS_ORG_ID = "00000000-0000-0000-0000-000000000000";
    public static final String TEMPLATE_XPD_ID = "00000000-0000-0000-0000-000000000000";
    public static final String SPMODEL_RULE_APP_CODE = "sptalent-jq";
    public static final String ORIGIN = "origin";
    public static final BigDecimal HUNDRED = BigDecimal.valueOf(100);
    public static final String DOWNLOAD_MODULE_CODE = "talentRvProject";
    public static final String DOWNLOAD_APP_CODE = "gwnl";
    public static final String FUNCTION_NAME = "pc_dlc_gwnl_talentProject";
    public static final String ROOT_ID = "0";
    public static final String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";
    /**认证项目证书来源类型（0：系统发证、1：项目发证、2：考试发证、3：岗位能力、4：面授中心、5：认证中心） */
    public static final Integer AUTHPRJ_CERT_SOURCE_FLAG = 5;
}
