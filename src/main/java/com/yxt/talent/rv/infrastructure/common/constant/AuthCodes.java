package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

@UtilityClass
public final class AuthCodes {
    // @formatter:off
    public static final String AUTH_CODE_ALL = "*";

    // --------------------绩效-------------------------
    public static final String PERF_IMPORT = "sp_gwnl_talentrv_achievements|sp_talentrv_achievementsoperate_input";
    public static final String PERF_CYCLE_SET = "sp_gwnl_talentrv_achievements|sp_talentrv_achievementsoperate_cycle";
    public static final String PERF_SET = "sp_gwnl_talentrv_achievements|sp_achievements_setting_operation";
    public static final String PERF_CLEAR = "sp_gwnl_talentrv_achievements|sp_talentrv_achievement_drop";

    // ---------------校准会跟踪管理--------------------

    // -------------------XPD项目-----------------------

    // -------------------校准会(新)-----------------------
    public static final String SP_TALENTRV_CALIBRATIONMEMBERLIST_DEP_EXTENT = "sp_talentrv_calibrationmemberlist_dep_extent";

    // -------------------Activity活动模型-----------------------


}
