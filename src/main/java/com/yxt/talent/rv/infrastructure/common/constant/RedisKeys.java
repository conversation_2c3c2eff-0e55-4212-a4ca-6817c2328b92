package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 锁表头：LK - lock key
 * 缓存标识:CK - cache Key
 */
@UtilityClass
public final class RedisKeys {

    /* cache key*/
    public static final String CK_ORG = "sprv:ck:org";
    public static final String CK_ORG_ROOT_DEPT = "sprv:ck:org:dept:root";
    public static final String CK_ORG_INIT_DEMO = "sprv:ck:org:init:%s";
    public static final String CK_ORG_LOCALIZATION = "sprv:ck:org:localization:%s";
    public static final String CK_ORG_PERF_GRADE = "sprv:ck:org:perf:grade";

    /**
     * 用户信息缓存
     */
    public static final String CK_USER_INFO_CACHE = "sprv:ck:user:info:%s";
    public static final String CK_USER_AUTH_CLIENT = "sprv:ck:user:auth:client:%s:%s";
    public static final String CK_USER_AUTH_CLIENT_TEAM = "sprv:ck:user:auth:client:team:%s:%s:%s";

    /* lock Key */
    public static final String LK_ORG_PROFILE_RV_DETAIL_EXPT = "sprv:lk:org:profile:rv:detail:%s:%s";
    public static final String LK_ORG_PERF_GRADE_INIT = "sprv:lk:org:perf:grade:init:%s";
    public static final String LK_ORG_PERF_GRADE_OP = "sprv:lk:org:perf:grade:op:%s";
    public static final String LK_PERF_IMPT = "sprv:lk:perf:impt:%s:%s:%s";
    public static final String LK_CALI_DIM_LEVEL_IMPT = "sprv:lk:cali:dim:level:impt:%s:%s:%s";
    public static final String LK_CALI_DIM_IMPT = "sprv:lk:cali:dim:impt:%s:%s:%s";
    public static final String CACHE_COPY_ORG_IDMAP = "sprv:copy:org:idmap:%s";
    public static final String CACHE_ACT_PROFILE_CAL = "sprv:act:profile:cal:%s:%s";
    public static final String LK_XPD_RULE_GEN = "sprv:lk:xpd:rule:gen:%s:%s";
    public static final String CACHE_ACT_PERF_CAL = "sprv:act:perf:cal:%s:%s";
    public static final String LK_XPD_ACT_IMPORT = "sprv:lk:xpd:import:act:%s:%s";
    public static final String ORG_INFO_CACHE_KEY = "sprv:org:info:%s:%s";

    /**
     * 新盘点结果计算锁 param xpdId
     */
    public static final String LK_XPD_CALC_EXECUTE = "sprv:lk:xpd:calc:execute:%s";

    public static final String CACHE_SEND_MESSAGE = "sprv:cache:send:message:%s:%s";

    //盘点迁移指标结果计算，参数：xpdId
    public static final String CACHE_CALC_MIG_INDICATOR_RESULT = "sprv:calc:mig:indicator:result:%s";

    public static final String CACHE_TALENT_USER_EXT_IMPORT = "sprv:talent:user:ext:import:%s:%s";

    public static final String LK_CALI_IMPT = "sprv:lk:cali:impt:%s:%s";

    public static final String CK_CALI_XPD_CALC_FORMULA = "sprv:ck.cali.xpd.calc.formula.%s";
    public static final String CACHE_KEY_EXPERT_EXPORT = "sprv:lk:expert:export:%s:%s";
    
    /**
     * 活动创建时间缓存
     */
    public static final String CK_ACTIVITY_CREATE_TIME = "sprv:ck:activity:create:time";

    public static final String LK_EXPERT_IMPT = "sprv:lk:expert:impt:%s:%s:%s";
}
