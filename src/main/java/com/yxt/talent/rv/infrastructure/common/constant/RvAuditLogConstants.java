package com.yxt.talent.rv.infrastructure.common.constant;

/**
 * @Description rv日志
 * <AUTHOR>
 * @Date 2024/8/6 11:24
 **/
public class RvAuditLogConstants {


    // 删除自定义绩效等级
    public static final String PERF_GRADE_DELETE = "PERF_GRADE_DELETE";

    // 绩效周期新增
    public static final String PERF_PERIOD_ADD = "PERF_PERIOD_ADD";

    // 绩效周期编辑
    public static final String PERF_PERIOD_UPDATE = "PERF_PERIOD_UPDATE";

    // 绩效周期删除
    public static final String PERF_PERIOD_DELETE = "PERF_PERIOD_DELETE";

    // 绩效周期名称编辑
    public static final String PERF_PERIOD_NAME_UPDATE = "PERF_PERIOD_NAME_UPDATE";


    public static final String XPD_CREATE = "XPD_CREATE";

    public static final String XPD_UPDATE = "XPD_UPDATE";

    public static final String XPD_CALC = "XPD_CALC";

    public static final String XPD_EXPORT = "XPD_EXPORT";

    public static final String XPD_ADD_TRAINING = "XPD_ADD_TRAINING";

    public static final String XPD_CREATE_TRAINING = "XPD_CREATE_TRAINING";

    public static final String XPD_DELETE_TRAINING = "XPD_DELETE_TRAINING";

    public static final String XPD_ADD_POOL = "XPD_ADD_POOL";

    public static final String XPD_USERREPORT_EXPORT = "XPD_USERREPORT_EXPORT";

    public static final String XPD_RULECONFIG_CREATE = "XPD_RULECONFIG_CREATE";

    public static final String XPD_RULECONFIG_EXECCREATE = "XPD_RULECONFIG_EXECCREATE";

    public static final String XPD_RULECONFIG_UPDATE = "XPD_RULECONFIG_UPDATE";

    public static final String XPD_DIM_COMB_EXPORT = "XPD_DIM_COMB_EXPORT";

    public static final String XPD_DIM_LAYER_EXPORT = "XPD_DIM_LAYER_EXPORT";

    public static final String XPD_TALENT_USER_EXPORT = "XPD_TALENT_USER_EXPORT";

    public static final String XPD_RULE_PERF_UPDATE = "XPD_RULE_PERF_UPDATE";

    public static final String XPD_RULE_OTHER_UPDATE = "XPD_RULE_OTHER_UPDATE";

    public static final String XPD_RULE_XPD_UPDATE = "XPD_RULE_XPD_UPDATE";


    public static final String DIM_COMB_ADD = "DIM_COMB_ADD";

    public static final String DIM_COMB_EDIT = "DIM_COMB_EDIT";

    public static final String DIM_COMB_DEL = "DIM_COMB_DEL";

    public static final String XPD_GRID_CREATE = "XPD_GRID_CREATE";


    public static final String XPD_GRID_EDIT = "XPD_GRID_EDIT";


    public static final String XPD_GRID_PUBLISH = "XPD_GRID_PUBLISH";

    public static final String XPD_GRID_WITHDRAW = "XPD_GRID_WITHDRAW";

    public static final String XPD_GRID_DELETE = "XPD_GRID_DELETE";

    public static final String XPD_IMPT_ACT_CREATE = "XPD_IMPT_ACT_CREATE";

    public static final String XPD_GRID_CELL_EDIT = "XPD_GRID_CELL_EDIT";


    public static final String XPD_LEVEL_CREATE = "XPD_LEVEL_CREATE";

    public static final String XPD_LEVEL_UPDATE = "XPD_LEVEL_UPDATE";

    public static final String XPD_LEVEL_DELETE = "XPD_LEVEL_DELETE";

    public static final String XPD_GRID_LEVEL_EDIT = "XPD_GRID_LEVEL_EDIT";

    public static final String XPD_GRID_RATIO_EDIT = "XPD_GRID_RATIO_EDIT";

    public static final String XPD_REPORT_EXPORT= "XPD_REPORT_EXPORT";

    // 更新校准任务记录
    public static final String CALI_MEET_ADD_NEW = "CALI_MEET_ADD_NEW";
    public static final String CALI_MEET_UPDATE_NEW = "CALI_MEET_UPDATE_NEW";
    public static final String CALI_DELETE_NEW = "CALI_DELETE_NEW";
    public static final String CALI_START = "CALI_START";
    public static final String CALI_END = "CALI_END";
    public static final String CALI_WITHDRAW = "CALI_WITHDRAW";
    public static final String CALI_USER_ADD = "CALI_USER_ADD";
    public static final String CALI_USER_DEL = "CALI_USER_DEL";
    public static final String CALI_USER_EXPORT = "CALI_USER_EXPORT";
    public static final String CALI_RECORD_EXPORT = "CALI_RECORD_EXPORT";
    public static final String XPD_CALI_RECORD_EXPORT = "XPD_CALI_RECORD_EXPORT";
}
