package com.yxt.talent.rv.infrastructure.common.constant;

import com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum;
import com.yxt.spsdfacade.constants.SdDemoConstants;
import lombok.experimental.UtilityClass;

/**
 * 起点demo机构复制用常量
 */
@UtilityClass
public class SprvDemoOrgCopyConstants {

    /*当前模块的约定code，用于总控服务[spbk]识别*/
    public static final String MODULE_CODE_SPRV = "sprv";

    /* @deprecated start */
    /*盘点项目*/
    public static final String SPRV_PRJ_CATEGORY_ID = "sprv_prj_category_id";
    /*校准会*/
    public static final String SPRV_CALIMEET_ID = "sprv_calimeet_id";


    /** 新版本demo复制 2025-06-05 */

    /* 分类树 */
    public static final String UBIZ_TREE_ID = "ubiz_tree_id";

    /*绩效*/
    public static final String SPRV_PERF_PERIOD_ID = "sprv_perf_period_id"; // 绩效周期id
    public static final String SPRV_PERF_GRADE_ID = "sprv_perf_grade_id"; // 绩效等级id

    /*AOM*/
    public static final String AOM_ACTV_ID = "aom_actv_prj_id"; // aom项目id
    public static final String AOM_REF_ID = "aom_ref_id"; // aom 活动id

    /*新盘点项目*/
    public static final String SPRV_XPD_ID = "sprv_xpd_id"; // 盘点项目id
    public static final String SPRV_XPD_GRID_ID = "sprv_xpd_grid_id";
    public static final String SPRV_XPD_GRID_LEVEL_ID = "sprv_xpd_grid_level_id";
    public static final String SPRV_XPD_GRID_CELL_ID = "sprv_xpd_grid_cell_id";
    public static final String SPRV_XPD_DIM_COMB_ID = "sprv_xpd_dim_comb_id";
    public static final String SPRV_XPD_DIM_RULE_ID = "sprv_xpd_dim_rule_id";
    public static final String SPRV_XPD_RULE_ID = "sprv_xpd_rule_id";
    public static final String SPRV_XPD_IMPT_ID = "sprv_xpd_impt_id";
    public static final String SPRV_XPD_RESULT_USER_DIM_ID = "sprv_xpd_result_user_dim_id";
    public static final String SPRV_XPD_LEVEL_ID = "sprv_xpd_level_id";
    public static final String SPRV_XPD_RULE_CONF_ID = "sprv_xpd_rule_conf_id";

    /*绩效活动*/
    public static final String SPRV_ACTV_PERF_ID = "sprv_actv_perf_id"; // 绩效活动 rv侧的id
    public static final String SPRV_ACTV_PERF_RESULT_CONF_ID = "sprv_actv_perf_result_conf_id"; // 绩效结果配置id

    /*动态人才评估活动*/
    public static final String SPRV_ACTV_PROF_ID = "sprv_actv_prof_id"; // 动态人才评估活动id
    public static final String SPRV_ACTV_PROF_RESULT_ID = "sprv_actv_prof_result_id"; // 动态人才评估活动结果id

    /* 外部依赖主键 v2 */
    public static final String DYN_XPD_ACTION_PLAN_TARGET_ID = "dyn_xpd_action_plan_target_id"; // 培训项目id or 人才池id
    public static final String O2O_TRAINING_ID = "o2o_training_id"; // 培训项目id
    public static final String SPBK_POOL_ID = "bk_pool_id"; // 人才池id

    /** 以下的常量需要于{@link SdDemoConstants}中常量值保持一致 */
    public static final String SPSD_MODEL_ID = "sd_model_id"; // 模型id
    public static final String SPSD_DIM_ID = "sd_dimension_id"; // 维度id
    public static final String SPSD_INDICATOR_ID = "sd_indicator_id"; // 指标id
    /** 以下的常量需要于{@link DemoIdMapKeyEnum}中常量值保持一致 */
    public static final String SPEVAL_EVAL_ID = "speval_evaluation_id"; // 测评方案id
    public static final String SPEVAL_DIM_SETTING_ID = "speval_evaluation_dimension_setting_id"; // 测评维度id
    // spmodel
    public static final String DYN_SPM_LABEL_ID = "dyn_spm_label_or_indicator_id"; // 标签|指标id
    public static final String SPM_LABEL_ID = "spm_label_id"; // 标签id
    public static final String SPM_INDICATOR_ID = "spm_indicator_id"; // 指标id
    public static final String SPM_LABEL_VALUE_ID = "spm_label_value_id";
    public static final String SPM_RULE_ID = "spm_rule_id";
}
