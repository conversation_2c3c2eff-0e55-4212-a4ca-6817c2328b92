package com.yxt.talent.rv.infrastructure.common.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Getter
@RequiredArgsConstructor
public enum EducationEnum {


    // 学历(0-其他;10-小学;20-初中;21-中专/中技;30-高中;31-职高;40-大专;50-本科;60-硕士;70-博士;)")
    OTHEER(0, "其他"),
    PRIMARY_SCHOOL(10, "小学"),
    JUNIOR_HIGH_SCHOOL(20, "初中"),
    TECHNICAL_SCHOOL(21, "中专/中技"),
    HIGH_SCHOOL(30, "高中"),
    VOCATIONAL_HIGH_SCHOOL(31, "职高"),
    JUNIOR_COLLEGE(40, "大专"),
    UNDERGRADUATE(50, "本科"),
    MASTER(60, "硕士"),
    DR(70, "博士");

    private Integer number;

    private String education;

    EducationEnum(Integer number, String education) {
        this.number = number;
        this.education = education;
    }

    public static String getByCode(Integer code) {
        for (EducationEnum educationEnum : EducationEnum.values()) {
            if (Objects.equals(educationEnum.getNumber(), code)) {
                return educationEnum.getEducation();
            }
        }
        return "";
    }
}
