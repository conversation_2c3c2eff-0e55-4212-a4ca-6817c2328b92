package com.yxt.talent.rv.infrastructure.common.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum LogicEnum {
    /**
     * 逻辑关系
     */
    AND(1, "与"),
    OR(2, "或");

    private final int code;
    private final String name;

    public static boolean isAnd(Integer code) {
        return Objects.equals(AND.getCode(), code);
    }

    public static boolean isOr(Integer code) {
        return Objects.equals(OR.getCode(), code);
    }
}
