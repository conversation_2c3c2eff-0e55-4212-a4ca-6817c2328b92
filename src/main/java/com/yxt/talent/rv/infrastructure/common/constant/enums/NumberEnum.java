package com.yxt.talent.rv.infrastructure.common.constant.enums;

import lombok.*;

@Getter
@NoArgsConstructor
public enum NumberEnum {

    /**
     * 枚举类
     */
    ALL(-1, ""),

    ZERO(0, "零"),

    ONE(1, "一"),

    <PERSON><PERSON><PERSON>(2, "二"),

    <PERSON><PERSON><PERSON><PERSON>(3, "三"),

    FO<PERSON>(4, "四"),

    FIVE(5, "五"),

    SIX(6, "六");

    private Integer number;

    private String numCh;

    NumberEnum(Integer number, String numCh) {
        this.number = number;
        this.numCh = numCh;
    }

}
