package com.yxt.talent.rv.infrastructure.common.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum PerfEvalTypeEnum {
    /**
     * 绩效评估方式
     */
    LEVEL(1, "绩效等级"),
    SCORE(2, "绩效得分");

    private final int code;
    private final String name;

    public static boolean byScore(Integer code) {
        return Objects.equals(SCORE.code, code);
    }

    public static boolean byLevel(Integer code) {
        return Objects.equals(LEVEL.code, code);
    }
}
