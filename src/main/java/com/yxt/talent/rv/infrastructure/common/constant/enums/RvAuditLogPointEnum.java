package com.yxt.talent.rv.infrastructure.common.constant.enums;

import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.base.AuditLogPointBase;
import com.yxt.spsdk.audit.enums.AuditLogicTypeEnum;
import com.yxt.talent.rv.controller.manage.calimeet.log.*;
import com.yxt.talent.rv.controller.manage.perf.log.*;
import com.yxt.talent.rv.controller.manage.xpd.log.*;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;

public enum RvAuditLogPointEnum implements AuditLogPointBase {

    CALI_MEET_ADD_NEW(RvAuditLogConstants.CALI_MEET_ADD_NEW, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, CaliAddProvider.class, "盘点-%s-创建校准任务"),

    CALI_MEET_UPDATE_NEW(RvAuditLogConstants.CALI_MEET_UPDATE_NEW, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, CaliEditProvider.class, "盘点-%s-编辑校准任务"),

    CALI_DELETE_NEW(RvAuditLogConstants.CALI_DELETE_NEW, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.DELETE, CaliDelProvider.class, "盘点-%s-删除校准任务"),

    CALI_START(RvAuditLogConstants.CALI_START, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE, CaliStartProvider.class, "盘点-%s-启动校准任务"),

    CALI_END(RvAuditLogConstants.CALI_END, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE, CaliEndProvider.class, "盘点-%s-结束校准任务"),

    CALI_WITHDRAW(RvAuditLogConstants.CALI_WITHDRAW, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.UPDATE, CaliWithdrawProvider.class, "盘点-%s-撤回校准任务"),

    CALI_USER_ADD(RvAuditLogConstants.CALI_USER_ADD, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, CaliUserAddProvider.class,
            "盘点-%s-添加校准人员"),
    CALI_USER_DEL(RvAuditLogConstants.CALI_USER_DEL, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.DELETE, CaliUserDelProvider.class,
            "盘点校准-%s-删除校准人员"),

    CALI_USER_EXPORT(RvAuditLogConstants.CALI_USER_EXPORT, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, CaliUserExportProvider.class,
            "盘点校准-%s-导出校准人员"),

    CALI_RECORD_EXPORT(RvAuditLogConstants.CALI_RECORD_EXPORT, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, CaliRecordExportProvider.class,
            "盘点校准-%s-导出校准记录"),

    XPD_CALI_RECORD_EXPORT(RvAuditLogConstants.XPD_CALI_RECORD_EXPORT, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, XpdCaliRecordExportProvider.class,
            "盘点-%s-导出校准记录"),

    // 删除绩效等级
    PERF_GRADE_DELETE(RvAuditLogConstants.PERF_GRADE_DELETE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PerfGradeDeleteProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效等级}"),


    // 绩效周期新增
    PERF_PERIOD_ADD(RvAuditLogConstants.PERF_PERIOD_ADD, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PerfPeriodAddProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),

    // 绩效周期编辑
    PERF_PERIOD_UPDATE(RvAuditLogConstants.PERF_PERIOD_UPDATE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PerfPeriodUpdateProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),

    // 绩效周期名称编辑
    PERF_PERIOD_NAME_UPDATE(RvAuditLogConstants.PERF_PERIOD_NAME_UPDATE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PerfPeriodNameUpdateProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),

    // 绩效周期删除
    PERF_PERIOD_DELETE(RvAuditLogConstants.PERF_PERIOD_DELETE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PerfPeriodDeleteProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),


    XPD_CREATE(RvAuditLogConstants.XPD_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdCreateLogProvider.class, "盘点-%s"),

    XPD_UPDATE(RvAuditLogConstants.XPD_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdUpdateLogProvider.class, "盘点-%s"),

    XPD_CALC(RvAuditLogConstants.XPD_CALC, AuditLogHelper.Module.PROJECT.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, XpdCalcLogProvider.class, "盘点-%s-开始计算"),

    XPD_EXPORT(RvAuditLogConstants.XPD_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.EXPORT, XpdExportlogProvider.class, "盘点-%s-项目报告"),

    XPD_ADD_TRAINING(RvAuditLogConstants.XPD_ADD_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdTrainingLogProvider.class, "盘点-%s-发起培训"),

    XPD_CREATE_TRAINING(RvAuditLogConstants.XPD_CREATE_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdPrjLaunchTrainingLogProvider.class,
            "盘点-%s-发起培训"),

    XPD_DELETE_TRAINING(RvAuditLogConstants.XPD_DELETE_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.DELETE, XpdPrjDelTrainingLogProvider.class,
            "盘点-%s-移除培训"),

    XPD_ADD_POOL(RvAuditLogConstants.XPD_ADD_POOL, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdAddPoolLogProvider.class, "盘点-%s-加入人才池"),

    XPD_USERREPORT_EXPORT(RvAuditLogConstants.XPD_USERREPORT_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdUserReportExportlogProvider.class,
            "盘点-%s-人员盘点报告"),

    XPD_RULECONFIG_CREATE(RvAuditLogConstants.XPD_RULECONFIG_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdRuleConifgCreateLogProvider.class,
            "盘点-%s-配置全局规则"),

    XPD_RULECONFIG_EXECCREATE(RvAuditLogConstants.XPD_RULECONFIG_EXECCREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdRuleConifgExecCreateLogProvider.class,
            "盘点-%s-快速生成计算规则"),

    XPD_RULECONFIG_UPDATE(RvAuditLogConstants.XPD_RULECONFIG_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRuleConifgUpdateLogProvider.class,
            "盘点-%s-配置全局规则"),

    XPD_DIM_COMB_EXPORT(RvAuditLogConstants.XPD_DIM_COMB_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdDimCombExportLogProvider.class,
            "盘点-%s-落位结果-%s"),

    XPD_DIM_LAYER_EXPORT(RvAuditLogConstants.XPD_DIM_LAYER_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdDimLayerExportLogProvider.class,
            "盘点-%s-维度分层-%s"),

    XPD_TALENT_USER_EXPORT(RvAuditLogConstants.XPD_TALENT_USER_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdTalentUserExportLogProvider.class,
            "盘点-%s-人才分层"),

    XPD_RULE_PERF_UPDATE(RvAuditLogConstants.XPD_RULE_PERF_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulePerfCreateLogProvider.class,
            "盘点-%s-编辑维度规则-%s"),

    XPD_RULE_OTHER_UPDATE(RvAuditLogConstants.XPD_RULE_OTHER_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulOtherCreateLogProvider.class,
            "盘点-%s-编辑维度规则-%s"),

    XPD_RULE_XPD_UPDATE(RvAuditLogConstants.XPD_RULE_XPD_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulCreateLogProvider.class,
            "盘点-%s-编辑项目计算规则"), DIM_COMB_ADD(RvAuditLogConstants.DIM_COMB_ADD,
            AuditLogHelper.Module.PROJECT.getCode(), AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            DimCombCreateLogProvider.class, "维度组合-%s"),

    DIM_COMB_EDIT(RvAuditLogConstants.DIM_COMB_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, DimCombEditLogProvider.class, "维度组合-%s"),

    DIM_COMB_DEL(RvAuditLogConstants.DIM_COMB_DEL, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.DELETE, DimCombDelLogProvider.class, "维度组合-%s"),

    XPD_GRID_CREATE(RvAuditLogConstants.XPD_GRID_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdGridCrateLogProvider.class, "宫格模板-%s"),

    XPD_GRID_EDIT(RvAuditLogConstants.XPD_GRID_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridEditLogProvider.class, "宫格模板-%s"),

    XPD_GRID_PUBLISH(RvAuditLogConstants.XPD_GRID_PUBLISH, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, XpdGridPublishLogProvider.class, "宫格模板发布-%s"),

    XPD_GRID_WITHDRAW(RvAuditLogConstants.XPD_GRID_WITHDRAW, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, XpdGridWithdrawLogProvider.class, "宫格模板发布-%s"),

    XPD_GRID_DELETE(RvAuditLogConstants.XPD_GRID_DELETE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, XpdGridDeleteLogProvider.class, "宫格模板删除-%s"),

    XPD_GRID_CELL_EDIT(RvAuditLogConstants.XPD_GRID_CELL_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridCellEditLogProvider.class, "模板宫格编辑-%s"),

    XPD_LEVEL_CREATE(RvAuditLogConstants.XPD_LEVEL_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdLevelCreateLogProvider.class, "创建人才分层-%s"),


    XPD_LEVEL_UPDATE(RvAuditLogConstants.XPD_LEVEL_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdLevelUpdateLogProvider.class, "编辑人才分层-%s"),

    XPD_LEVEL_DELETE(RvAuditLogConstants.XPD_LEVEL_DELETE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, XpdLevelDeleteLogProvider.class, "删除人才分层-%s"),

    XPD_GRID_LEVEL_EDIT(RvAuditLogConstants.XPD_GRID_LEVEL_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridLevelEditLogProvider.class,
            "编辑分层标准-%s"),

    XPD_GRID_RATIO_EDIT(RvAuditLogConstants.XPD_GRID_RATIO_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridRatioEditLogProvider.class,
            "编辑落位比例-%s"),

    XPD_REPORT_EXPORT(RvAuditLogConstants.XPD_REPORT_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, XpdReportExportProvider.class, "项目报告导出-%s"),

    XPD_IMPT_ACT_CREATE(RvAuditLogConstants.XPD_IMPT_ACT_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.CREATE, XpdImptActCreatLogProvider.class,
            "创建导入活动-%s");

    RvAuditLogPointEnum(String pointCode, String module, int logicType, String auditAction, Class dataProvider,
            String pointName) {
        this.pointCode = pointCode;
        this.module = module;
        this.logicType = logicType;
        this.auditAction = auditAction;
        this.dataProvider = dataProvider;
        this.pointName = pointName;
    }

    private String pointCode;
    private String module;
    /**
     * ref AuditLogicTypeEnum
     */
    private int logicType;
    private String auditAction;
    private Class<? extends AuditLogDataProvider> dataProvider;
    private String pointName;

    @Override
    public String getPointCode() {
        return pointCode;
    }

    @Override
    public String getModule() {
        return module;
    }

    @Override
    public int getLogicType() {
        return logicType;
    }

    @Override
    public String getAuditAction() {
        return auditAction;
    }

    @Override
    public Class<? extends AuditLogDataProvider> getDataProvider() {
        return dataProvider;
    }

    @Override
    public String getPointName() {
        return pointName;
    }
}
