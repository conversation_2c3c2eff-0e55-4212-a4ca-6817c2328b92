package com.yxt.talent.rv.infrastructure.common.constant.enums;

import cn.hutool.core.lang.Pair;
import com.yxt.talent.rv.application.common.UTreeNodeDelInterceptor;

import java.util.function.BiConsumer;

/**
 * UTreeEnum
 *
 * <AUTHOR> harleyge
 * @Date 5/12/24 3:01
 */
public enum UTreeEnum {
    XPD_BASE("1075876045620629505", "盘点项目", UTreeNodeDelInterceptor.getInstance()::checkXpdBase),
    // 6.4 分类调整
    AUTHPRJ_BASE("1945303595929384962", "认证项目", UTreeNodeDelInterceptor.getInstance()::checkAuthPrjBase);

    private String treeId;
    private String name;

    private BiConsumer<String, Pair<UTreeEnum, String>> checkNodeDel;

    UTreeEnum(String treeId, String name, BiConsumer<String, Pair<UTreeEnum, String>> checkNodeDel) {
        this.treeId = treeId;
        this.name = name;
        this.checkNodeDel = checkNodeDel;
    }

    public String getTreeId() {
        return treeId;
    }

    public String getName() {
        return name;
    }

    public BiConsumer<String, Pair<UTreeEnum, String>> getCheckNodeDel() {
        return checkNodeDel;
    }

    public static UTreeEnum getByTreeId(String treeId) {
        for (UTreeEnum value : UTreeEnum.values()) {
            if (value.treeId.equals(treeId)) {
                return value;
            }
        }
        return null;
    }
}
