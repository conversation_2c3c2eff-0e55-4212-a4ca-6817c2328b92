package com.yxt.talent.rv.infrastructure.common.dto;

import com.yxt.common.pojo.api.PagingList;
import com.yxt.usdk.framework.webimpl.config.UsdkThreadPoolConfig;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.yxt.common.component.SpringContextHolder.getBean;
import static java.util.concurrent.CompletableFuture.allOf;
import static java.util.concurrent.CompletableFuture.runAsync;

/**
 * 视图字段包装基类，一般用于列表或者分页查询之后，额外填充部分渲染字段时使用
 */
public interface BaseVoWrapper<V> {

    default PagingList<V> wrapper(PagingList<V> pages) {
        wrapper(pages.getDatas());
        return pages;
    }

    default List<V> wrapper(List<V> list) {
        Executor executor = getBean(UsdkThreadPoolConfig.WAF_EXECUTOR_BEAN_NAME);
        allOf(list.stream()
                .map(vo -> runAsync(() -> wrapper4Batch(vo), executor))
                .toArray(CompletableFuture[]::new)).join();
        return list;
    }

    /**
     * 部分分页列表需要填充的字段与单个详情需要填充的字段可能不一致，可以实现并使用该方法
     */
    default V wrapper4Batch(V vo) {
        return wrapper(vo);
    }

    V wrapper(V vo);

}
