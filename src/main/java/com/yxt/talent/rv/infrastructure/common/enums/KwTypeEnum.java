package com.yxt.talent.rv.infrastructure.common.enums;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum KwTypeEnum {

    /**
     * 账号
     */
    USER_NAME("userName", 2),

    /**
     * 姓名
     */
    FULL_NAME("name", 1),

    /**
     * 未知
     */
    UNKNOWN("unknown", -1)

    ;

    private final String relatedMetaCode;
    private final int code;

    @Nonnull
    public static KwTypeEnum of(@Nullable String keyworkType) {
        for (KwTypeEnum kwTypeEnum : KwTypeEnum.values()) {
            if (Objects.equals(kwTypeEnum.getRelatedMetaCode(), keyworkType)) {
                return kwTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
