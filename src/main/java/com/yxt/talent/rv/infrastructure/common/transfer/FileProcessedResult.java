package com.yxt.talent.rv.infrastructure.common.transfer;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Setter
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FileProcessedResult<T extends ImportContent> extends GenericProcessedResult<T> {

    /**
     * 导出失败时的文件路径
     */
    @Builder.Default
    private String errorFilePath = "";

    public FileProcessedResult(
            @Nullable List<T> successData,
            @Nullable List<T> failedData,
            @Nullable List<T> totalData) {
        super(successData, failedData, totalData);
    }

}
