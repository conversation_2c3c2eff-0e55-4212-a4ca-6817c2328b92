package com.yxt.talent.rv.infrastructure.common.transfer;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入数据处理结果
 */
@Setter
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GenericProcessedResult<T extends ImportContent> implements ProcessedResult<T> {

    /**
     * 导入成功的数据
     */
    @jakarta.annotation.Nullable
    @Builder.Default
    private List<T> successData = new ArrayList<>();

    /**
     * 导入失败的数据
     */
    @jakarta.annotation.Nullable
    @Builder.Default
    private List<T> failedData = new ArrayList<>();

    /**
     * 处理的总数据
     */
    @jakarta.annotation.Nullable
    @Builder.Default
    private List<T> totalData = new ArrayList<>();


}
