package com.yxt.talent.rv.infrastructure.common.transfer;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.service.ILock;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport.MAX_LEASE_TIME;
import static com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport.MAX_WAIT_TIME;

/**
 * 超大数据集的传输抽象类
 */
@Slf4j
public abstract class GenericTransfer<S extends TransferSupport, R> implements Transfer {

    protected R handle(S transferSupport, Function<S, R> action) {
        ILock lockService = getLockService();
        String tranId = transferSupport.getTranId();
        try {
            log.debug("LOG66290:{}", transferSupport);
            int maxWaitTime = getMaxWaitTime(transferSupport.getTranMaxWaitTime());
            int maxLeaseTime = getMaxLeaseTime(transferSupport.getTranMaxLeaseTime());
            if (!lockService.tryLock(tranId, maxWaitTime, maxLeaseTime, TimeUnit.SECONDS)) {
                throw new ApiException(ExceptionKeys.SYS_CONCURRENT_ERROR);
            }
            return action.apply(transferSupport);
        } finally {
            lockService.unLock(tranId);
        }
    }

    protected int getMaxLeaseTime(int tranMaxLeaseTime) {
        return Integer.max(MAX_LEASE_TIME, tranMaxLeaseTime);
    }

    protected int getMaxWaitTime(int tranWaitTime) {
        return Integer.max(MAX_WAIT_TIME, tranWaitTime);
    }

    @jakarta.annotation.Nonnull
    protected ILock getLockService() {
        ILock lockService = SpringContextHolder.getBean("lockService", ILock.class);
        Validate.isNotNull(lockService, "lockService is null");
        return lockService;
    }

}
