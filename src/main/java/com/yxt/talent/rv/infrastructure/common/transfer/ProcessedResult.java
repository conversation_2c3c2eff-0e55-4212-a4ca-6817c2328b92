package com.yxt.talent.rv.infrastructure.common.transfer;

import java.util.List;
import java.util.Optional;

/**
 * 数据集处理统计结果
 */
public interface ProcessedResult<T> {

    List<T> getSuccessData();

    List<T> getFailedData();

    List<T> getTotalData();

    default int getTotalCount() {
        return Optional.ofNullable(getTotalData()).map(List::size).orElse(0);
    }

    default int getSuccessCount() {
        return Optional.ofNullable(getSuccessData()).map(List::size).orElse(0);
    }

    default int getFailedCount() {
        return Optional.ofNullable(getFailedData()).map(List::size).orElse(0);
    }

}
