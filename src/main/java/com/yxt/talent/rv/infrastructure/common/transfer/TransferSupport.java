package com.yxt.talent.rv.infrastructure.common.transfer;

import com.yxt.criteria.Command;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 超大数据集通用转换上下文的支持类，用于处理一些大数据量耗时耗资源的转换操作，比如文件的导入导出等
 */
@Setter
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false, of = "tranId")
public abstract class TransferSupport implements Command {

    /** 导入导出事务锁最大释放时间,如果不自定义该值,那么 */
    public static final int MAX_WAIT_TIME = 60;
    public static final int MAX_LEASE_TIME = 120;

    /**
     * 机构id, 务必不能为空
     */
    @NonNull
    private String orgId;

    /**
     * 导出人, 用于跟踪和审计
     */
    private String operator;

    /**
     * 导出事务id, 保证导出事务的幂等性和一定范围内的并发 同时用于以后扩展用于导出进度的跟踪, 务必不能为空
     */
    @NonNull
    private String tranId;

    /**
     * 导出事务的最长等待时间(单位:秒)
     * 底层使用lockService实现锁等待,超过该时间之后,会抛出并发异常
     */
    @Builder.Default
    private int tranMaxWaitTime = MAX_WAIT_TIME;

    /**
     * 导出事务的最长执行时间(单位:秒)
     * 底层使用lockService实现,超过改时间,分布式锁会释放,可能会出现并发问题,请谨慎评估
     */
    @Builder.Default
    private int tranMaxLeaseTime = MAX_LEASE_TIME;

}
