package com.yxt.talent.rv.infrastructure.common.transfer.impt;

import lombok.*;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class GenericImportResult implements ImportResult {
    private int totalCount;
    private int failedCount;
    private int failCount;
    private int successCount;

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
        this.failCount = failedCount;
    }
}
