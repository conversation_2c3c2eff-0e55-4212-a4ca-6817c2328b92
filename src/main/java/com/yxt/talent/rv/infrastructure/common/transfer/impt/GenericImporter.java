package com.yxt.talent.rv.infrastructure.common.transfer.impt;

import com.yxt.common.util.Validate;
import com.yxt.talent.rv.infrastructure.common.transfer.GenericTransfer;
import com.yxt.talent.rv.infrastructure.common.transfer.ProcessedResult;
import com.yxt.talent.rv.infrastructure.common.transfer.Reader;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.UnaryOperator;

/**
 * 数据集导入抽象类, 请尽量遵循一个类导出一个文件的原则
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class GenericImporter<S extends GeneticImportSupport<T, U, R>, T extends ImportContent, U extends ProcessedResult<T>, R extends ImportResult>
        extends GenericTransfer<S, R> {

    /**
     * 支持两个形式的文件导入, 一种是文件流直接上传, 另一种是前端上传到文件中心,然后传给我们fileId
     *
     * @param importSupport 导入数据前必要的支持参数
     * @return
     * @throws Exception
     */
    @jakarta.annotation.Nullable
    protected R toImport(S importSupport) {
        return handle(importSupport, this::doImport);
    }

    @jakarta.annotation.Nullable
    protected R doImport(S importSupport) {
        try {
            // 读取数据
            Reader<T> dataReader = importSupport.getDataReader();
            List<T> importDatas = Objects.requireNonNull(dataReader).read();

            // 处理导入数据
            Function<List<T>, U> dataProcessor = importSupport.getDataProcessor();
            U transferResult = dataProcessor.apply(importDatas);
            Validate.isNotNull(transferResult, "transferResult is null");

            // 处理失败数据
            UnaryOperator<U> errorProcessor = importSupport.getErrorProcessor();
            if (errorProcessor != null && transferResult.getFailedCount() > 0) {
                transferResult = errorProcessor.apply(transferResult);
            }

            // 生成导入结果
            Function<U, R> importResultSupplier = importSupport.getResultProcessor();
            @jakarta.annotation.Nullable R importResult = null;
            if (importResultSupplier != null) {
                importResult = importResultSupplier.apply(transferResult);
            }
            return importResult;
        } catch (Exception e) {
            log.error("LOG66280:", e);
            throw e;
        }
    }

}
