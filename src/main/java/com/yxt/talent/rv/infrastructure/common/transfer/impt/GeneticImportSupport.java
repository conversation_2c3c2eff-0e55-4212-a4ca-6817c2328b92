package com.yxt.talent.rv.infrastructure.common.transfer.impt;

import com.yxt.talent.rv.infrastructure.common.transfer.ProcessedResult;
import com.yxt.talent.rv.infrastructure.common.transfer.Reader;
import com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport;
import lombok.*;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;
import java.util.function.UnaryOperator;

@Slf4j
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, exclude = {"dataReader", "dataProcessor", "errorProcessor", "resultProcessor"})
public abstract class GeneticImportSupport<T extends ImportContent, U extends ProcessedResult<T>, R extends ImportResult>
        extends TransferSupport {

    /**
     * 承接导入的数据
     */
    @jakarta.annotation.Nonnull
    private Class<T> importContentClazz;

    /**
     * 数据提取器，用来提取导入的数据
     */
    @jakarta.annotation.Nullable
    private Reader<T> dataReader;

    /**
     * 数据处理器，用来处理导入的数据
     */
    @jakarta.annotation.Nonnull
    private Function<List<T>, U> dataProcessor;

    /**
     * 错误处理器，用于处理错误数据
     */
    @jakarta.annotation.Nullable
    private UnaryOperator<U> errorProcessor;

    /**
     * 结果处理器，用于生成导入结果
     */
    @jakarta.annotation.Nullable
    private Function<U, R> resultProcessor;

}
