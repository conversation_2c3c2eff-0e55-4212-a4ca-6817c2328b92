package com.yxt.talent.rv.infrastructure.common.utilities.factory;

import com.yxt.common.Constants;
import com.yxt.common.pojo.api.PageBean;
import com.yxt.common.util.ApiUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import static com.yxt.common.Constants.PARAM_NAME_LIMIT;
import static com.yxt.common.util.StringUtil.str2Int;

@UtilityClass
public class PageBeanFactory {

    private static final int MAX_LIMIT = 100000;
    private static final int DEFAULT_LIMIT = 20;

    public static PageBean buildPageBean() {
        HttpServletRequest request = ApiUtil.getRequestByContext();
        if (request == null) {
            throw new RuntimeException("request is null");
        }

        int limit = str2Int(request.getParameter(PARAM_NAME_LIMIT), DEFAULT_LIMIT);
        if (limit > MAX_LIMIT) {
            limit = MAX_LIMIT;
        } else if (limit <= 0) {
            limit = DEFAULT_LIMIT;
        }

        int offset = str2Int(request.getParameter(Constants.PARAM_NAME_OFFSET), 0);
        String orderBy = request.getParameter(Constants.PARAM_NAME_ORDERBY);
        String direction = request.getParameter(Constants.PARAM_NAME_DIRECTION);
        direction = StringUtils.equalsIgnoreCase(direction, Constants.PARAM_NAME_DIRECTION_ASC) ?
                Constants.PARAM_NAME_DIRECTION_ASC : Constants.PARAM_NAME_DIRECTION_DESC;

        PageBean page = new PageBean();
        page.setLimit(limit);
        page.setOffset(offset);
        page.setDirection(direction);
        page.setOrderby(orderBy);
        return page;
    }
}
