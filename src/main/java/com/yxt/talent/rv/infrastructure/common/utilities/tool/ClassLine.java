package com.yxt.talent.rv.infrastructure.common.utilities.tool;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ClassLine {

    private ClassInfo classInfo;

    private String logId;

    private String line;

    private long lineNumber;

    private boolean logLine;

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("classInfo", classInfo.toString())
                .append("logId", logId)
                .append("line", line)
                .append("lineNumber", lineNumber)
                .append("logLine", logLine)
                .toString();
    }
}
