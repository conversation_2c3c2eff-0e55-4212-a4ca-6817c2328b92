package com.yxt.talent.rv.infrastructure.common.utilities.tool;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@SuppressWarnings("all")
public class CodeAnalyzer {
    private static final String PRJ_DIRECTORY =
            "C:\\Workspace\\yxt\\sptalentrv\\sptalentrvapi"; // 替换为你的项目目录
    private static final String MESSAGE_PROPERTIES_FILE =
            "C:\\Workspace\\yxt\\sptalentrv\\sptalentrvapi\\src\\main\\resources\\message.properties";
    private static final String I18N_PROPERTIES_FILE =
            "C:\\Workspace\\yxt\\sptalentrv\\sptalentrvapi\\src\\main\\resources\\i18n.properties";

    public static void main(String[] args) {
        List<String> exceptionKeys = findExceptionKeys(PRJ_DIRECTORY);
        List<String> missingKeys = compareKeys(exceptionKeys);

        if (missingKeys.isEmpty()) {
            System.out.println(
                    "所有异常key都已在message.properties文件或i18n.properties文件中找到");
        } else {
            System.out.println("以下异常key在message.properties文件和i18n.properties文件中都缺失：");
            for (String key : missingKeys) {
                System.out.println(key);
            }
        }
    }

    // 查找所有以指定前缀开头的字符串
    public static List<String> findExceptionKeys(String projectDirectory) {
        List<String> keys = new ArrayList<>();

        // 在你的项目中遍历代码，查找以指定前缀开头的字符串，并将它们添加到keys列表中
        CodeTraverser.traverseCode(new File(projectDirectory), keys);

        return keys;
    }

    // 比对异常key和message.properties文件、i18n.properties文件中的key，找出缺失的key
    public static List<String> compareKeys(List<String> exceptionKeys) {
        List<String> missingKeys = new ArrayList<>();

        Properties messageProperties = new Properties();
        Properties i18nProperties = new Properties();
        try (
                FileInputStream messageFis = new FileInputStream(MESSAGE_PROPERTIES_FILE);
                FileInputStream i18nFis = new FileInputStream(I18N_PROPERTIES_FILE)
        ) {
            messageProperties.load(messageFis);
            i18nProperties.load(i18nFis);

            for (String key : exceptionKeys) {
                if (!messageProperties.containsKey(key) && !i18nProperties.containsKey(key)) {
                    missingKeys.add(key);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return missingKeys;
    }
}
