package com.yxt.talent.rv.infrastructure.common.utilities.tool;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

@SuppressWarnings("all")
public class CodeTraverser {

    private static final String PREFIX = "apis.sptalentrv."; // 替换为你的前缀

    public static void traverseCode(File directory, List<String> keys) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        traverseCode(file, keys);
                    } else if (file.getName().endsWith(".java")) {
                        findStrings(file.toPath(), keys);
                    }
                }
            }
        }
    }

    public static void findStrings(Path file, List<String> keys) {
        try {
            List<String> lines = Files.readAllLines(file);
            for (String line : lines) {
                if (line.contains(PREFIX)) {
                    int index = line.indexOf(PREFIX);
                    int endIndex = line.indexOf("\"", index);
                    if (endIndex != -1) {
                        String key = line.substring(index, endIndex);
                        keys.add(key);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
