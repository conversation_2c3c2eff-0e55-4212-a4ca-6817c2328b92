package com.yxt.talent.rv.infrastructure.common.utilities.tool;

import com.yxt.common.Constants;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.HttpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@SuppressWarnings("all")
public final class HttpSender {

    private static final String PATH = "C:\\Users\\<USER>\\Downloads\\2C124C60-7C1F-402e-922F-32BE821149F5.txt"; // NOSONAR
    private static final String TOKEN = "xxx";
    private static final String URL = "https://api-clock-phx-ali.yunxuetang.cn/v2/clock/org/task/close";

    @SneakyThrows
    public static void main(String[] args) {
        Path path = Paths.get(PATH);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", Constants.MEDIATYPE);
        headers.add("Source", "501");
        headers.add("token", TOKEN);
        try (Stream<String> lines = Files.lines(path, Charset.defaultCharset())) {
            lines.forEach(line -> {
                if (StringUtils.isBlank(line)) {
                    return;
                }
                Map<String, String> request = new HashMap<>(8);
                request.put("taskId", line);
                HttpEntity<String> entity =
                        new HttpEntity<>(
                                BeanHelper.bean2Json(request, ALWAYS), headers);
                ResponseEntity<String> exchange = HttpUtil.exchange(URL, HttpMethod.POST, entity);
                if (!exchange.getStatusCode().is2xxSuccessful()) {
                    log.warn("LOG63040:line:{}; resp:{}", line, exchange.getBody());
                }
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
