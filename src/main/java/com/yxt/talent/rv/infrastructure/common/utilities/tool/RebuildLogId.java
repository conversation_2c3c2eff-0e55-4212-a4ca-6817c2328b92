package com.yxt.talent.rv.infrastructure.common.utilities.tool;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于检查数项目中重复和没有设置logId的日志记录，并更新
 */
@Slf4j
@UtilityClass
public class RebuildLogId {
    /**
     * 项目绝对路径，需要手动填写
     */
    @SuppressWarnings("squid:S1075")
    public static final String PATH_PRJ_SRC =
            "C:\\Workspace\\yxt\\sptalentrv\\sptalentrvapi";

    /**
     * log标记前缀
     */
    public static final String LOG_PREFIX = "LOG";

    /**
     * log序号长度
     */
    public static final int LOG_LENGTH = 5;

    /**
     * 项目换行符
     */
    public static final String NEWLINE = "\n";

    /**
     * log标志
     */
    private static final Pattern LOG_PATTERN =
            Pattern.compile("log.(?:trace|debug|info|warn|error)");

    /**
     * 包含logId的log代码匹配
     */
    private static final Pattern SINGLE_LINE_LOGID_PATTERN =
            Pattern.compile("log.(?:trace|debug|info|warn|error)\\(\"(LOG\\d+):");

    /**
     * 换行log匹配
     */
    private static final Pattern MUTIL_LINE_LOGID_PATTERN =
            Pattern.compile("^log.(?:trace|debug|info|warn|error)\\($");

    public static void main(String[] args) {
        Collection<File> files =
                FileUtils.listFiles(new File(PATH_PRJ_SRC), new String[]{"java"}, true);
        List<ClassInfo> classInfos = files.stream().map(file -> {
            ClassInfo classInfo = ClassInfo.builder().file(file).build();
            classInfo.setClassLines(doHandleFile(classInfo));
            return classInfo;
        }).toList();
        List<ClassLine> logLines = classInfos.stream().flatMap(
                        ci -> ci.getClassLines()
                                .stream()
                                .filter(ClassLine::isLogLine)
                                .toList()
                                .stream())
                .toList();
        MultiValuedMap<String, ClassLine> dictMap = new ArrayListValuedHashMap<>();
        logLines.forEach(ll -> dictMap.put(ll.getLogId(), ll));

        dictMap.keySet().forEach(logId -> {
            Collection<ClassLine> classLines = dictMap.get(logId);
            if (classLines.size() > 1) {
                List<String> msgList = new ArrayList<>();
                classLines.forEach(cl -> msgList.add(
                        cl.getClassInfo().getFile().getName() + ":" + cl.getLineNumber()));
                String flag = "重复";
                if (StringUtils.isBlank(logId.trim())) {
                    flag = "缺少";
                }
                log.warn("LOG05360:发现{}日志序号。logId:[{}] ==> {}", flag, logId, msgList);
            }
        });

        long maxLogId = getMaxLogId(dictMap);
        String li = StringUtils.leftPad(String.valueOf(maxLogId), 5, "0");
        log.info("LOG05370:目前使用最大序号。logId:[LOG{}]", li);
    }

    private static long getMaxLogId(MultiValuedMap<String, ClassLine> dictMap) {
        return dictMap.keySet()
                .stream()
                .filter(StringUtils::isNotBlank)
                .map(logId -> Long.parseLong(logId.replace(LOG_PREFIX, "")))
                .max(Comparator.comparingLong(x -> x))
                .orElse(0L);
    }

    @SneakyThrows
    private static List<ClassLine> doHandleFile(ClassInfo classInfo) {
        List<String> lines =
                FileUtils.readLines(classInfo.getFile(), StandardCharsets.UTF_8.name());
        List<ClassLine> classLines = new ArrayList<>();
        LongAdder lineNumber = new LongAdder();
        AtomicBoolean isMultiLineLog = new AtomicBoolean(false);
        lines.forEach(
                line -> extraceClassLine(classInfo, classLines, lineNumber, isMultiLineLog, line));
        return classLines;
    }

    private static void extraceClassLine(
            ClassInfo classInfo, List<ClassLine> classLines,
            LongAdder lineNumber,
            AtomicBoolean isMultiLineLog, String line) {
        line = line.trim();
        ClassLine info = new ClassLine();
        lineNumber.increment();
        info.setLineNumber(lineNumber.longValue());
        if (LOG_PATTERN.matcher(line).find() || isMultiLineLog.get()) {
            info.setLogLine(true);
        }
        String logId = "";

        if (isMultiLineLog.get() && line.contains(LOG_PREFIX)) {
            logId = line.substring(line.indexOf(LOG_PREFIX), LOG_PREFIX.length() + LOG_LENGTH + 1);
        }
        if (isMultiLineLog.get()) {
            isMultiLineLog.set(false);
        }
        if (StringUtils.isBlank(logId)) {
            Matcher m1 = SINGLE_LINE_LOGID_PATTERN.matcher(line);
            if (m1.find()) {
                logId = m1.group(1);
            }
        }
        if (StringUtils.isBlank(logId)) {
            Matcher m2 = MUTIL_LINE_LOGID_PATTERN.matcher(line);
            if (m2.matches()) {
                info.setLogLine(false);
                isMultiLineLog.set(true);
            }
        }
        info.setLogId(logId);
        info.setClassInfo(classInfo);
        info.setLine(line + NEWLINE);
        classLines.add(info);
    }
}
