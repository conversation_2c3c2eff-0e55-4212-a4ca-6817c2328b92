package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.GridCommonVO;

import java.util.*;

public class ApassEntityUtils {

    public static AmUser4DTO createDrawer4UserRespDTO(String fullname, String id) {
        AmUser4DTO amUser4DTO = new AmUser4DTO();
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(id);
        userInfo.setName(fullname);
        amUser4DTO.setDatas(Collections.singletonList(userInfo));
        return amUser4DTO;
    }

    public static AmUser4DTO createDrawer4UserDTO(String fullname, String id, String username, String dept, String status,
        String position, String imgUrl) {
        AmUser4DTO amUser4DTO = new AmUser4DTO();
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(id);
        userInfo.setName(fullname);
        userInfo.setUserName(username);
        userInfo.setImgUrl(imgUrl);
        userInfo.setStatus(status);
        dealDept(dept, userInfo);
        dealPosition(position, userInfo);
        amUser4DTO.setDatas(Collections.singletonList(userInfo));
        return amUser4DTO;
    }

    public static AmUser4DTO createDrawer4UserSimple(String fullname, String id, String username,String imgUrl) {
        AmUser4DTO amUser4DTO = new AmUser4DTO();
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(id);
        userInfo.setName(fullname);
        userInfo.setUserName(username);
        userInfo.setImgUrl(imgUrl);
        amUser4DTO.setDatas(Collections.singletonList(userInfo));
        return amUser4DTO;
    }

    private static void dealPosition(String position, AmUser4DTO.UserInfo userInfo) {
        AmUser4DTO.AmPosition positionList = new AmUser4DTO.AmPosition();
        List<AmUser4DTO.PositionInfo> datas = new ArrayList<>();
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        positionInfo.setId("");
        positionInfo.setName(position);
        datas.add(positionInfo);
        positionList.setDatas(datas);
        userInfo.setPositionList(positionList);
    }

    private static void dealDept(String dept, AmUser4DTO.UserInfo userInfo) {
        AmUser4DTO.AmDept amDept = new AmUser4DTO.AmDept();
        List<AmUser4DTO.DeptInfo> datas = new ArrayList<>();
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        deptInfo.setId("");
        deptInfo.setName(dept);
        datas.add(deptInfo);
        amDept.setDatas(datas);
        userInfo.setDeptList(amDept);
    }


    public static AmSlDrawer4RespDTO createAmSlDrawer4RespDTO(String name, String id, String value, String label) {
        AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
        AmDrawerData amDrawerData = new AmDrawerData();
        amDrawerData.setId(id);
        amDrawerData.setName(name);
        amDrawerData.setLabel(label);
        amDrawerData.setValue(value);
        amSlDrawer4RespDTO.setDatas(Collections.singletonList(amDrawerData));
        return amSlDrawer4RespDTO;
    }

    public static AmSlDrawer4RespDTO createAmSlDrawerIdName(String id, String name) {
        AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
        AmDrawerData amDrawerData = new AmDrawerData();
        amDrawerData.setId(id);
        amDrawerData.setName(name);
        amSlDrawer4RespDTO.setDatas(Collections.singletonList(amDrawerData));
        return amSlDrawer4RespDTO;
    }

    public static AmSlDrawer4RespDTO createAmSlDrawer4RespDTOList(List<Object> datas) {
        AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
        amSlDrawer4RespDTO.setDatas(datas);
        return amSlDrawer4RespDTO;
    }

    public static GridCommonVO create4App(String id, String name) {
        GridCommonVO res = new GridCommonVO();
        List<Object> datas = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        datas.add(map);
        res.setDatas(datas);
        return res;
    }
}
