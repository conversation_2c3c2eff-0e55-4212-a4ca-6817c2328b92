package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.common.Constants;
import jakarta.annotation.Nullable;
import joptsimple.internal.Strings;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.*;

@Slf4j
@UtilityClass
public class DateTimeUtil {
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter FORMATTER_DATE_TIME =
            DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
    public static final DateTimeFormatter FORMATTER_YYYY_MM_DD_HH_MM_SS =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter FORMATTER_YYYY_MM_DD =
            DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter YYYY_MM_DD_HH_MM =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public static String getCurrentDateTime(int day) {
        return LocalDateTime.now().plusDays(day).format(FORMATTER_DATE_TIME);
    }

    @Nullable
    public static String dateToString(Date date, String formatStr) {
        try {
            return DateTimeFormatter.ofPattern(formatStr)
                    .withZone(ZoneId.systemDefault())
                    .format(date.toInstant());
        } catch (Exception e) {
            log.error("LOG11075:convert data error : {}", e.getMessage(), e);
            return null;
        }
    }

    public static String dateToString(Date date, DateTimeFormatter dateTimeFormatter) {
        return dateTimeFormatter.withZone(ZoneId.systemDefault()).format(date.toInstant());
    }

    /**
     * 日期转换成字符串 yyyy-MM-dd
     *
     * @param date 指定date
     * @return 日期字符串
     */
    public static String dateToString(Date date) {
        return DateTimeFormatter.ISO_LOCAL_DATE.withZone(ZoneId.systemDefault())
                .format(date.toInstant());
    }

    /**
     * 日期转换成字符串
     *
     * @param temporalAccessor, pattern
     * @return 指定日期
     */
    public static String dateToString(TemporalAccessor temporalAccessor, String pattern) {
        return DateTimeFormatter.ofPattern(pattern).format(temporalAccessor);
    }

    public static String dateToString(
            TemporalAccessor temporalAccessor,
            DateTimeFormatter formatter) {
        return formatter.format(temporalAccessor);
    }

    /**
     * LocalDateTime转字符串
     *
     * @param date
     * @param formatter
     */
    public static String localDateTime2Str(LocalDateTime date, String formatter) {
        if (Objects.isNull(date)) {
            return Strings.EMPTY;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatter);
        return date.format(dateTimeFormatter);
    }

    public static String formatDate(LocalDateTime date) {
        return DateTimeFormatter.ofPattern(Constants.SDF_YEAR2SECOND).format(date);
    }

    public static String formatSimpleDate(LocalDateTime date) {
        return DateTimeFormatter.ofPattern(Constants.SDF_YEAR2DAY).format(date);
    }

    public static String getNowYear() {
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        return currentYear + "";
    }

    public static boolean isDateWithinRange(
            LocalDate targetDate, LocalDate fromDate,
            LocalDate toDate) {

        if (targetDate == null) {
            return false;
        }

        if (fromDate.isAfter(toDate)) {
            throw new IllegalArgumentException("fromDate cannot be after toDate");
        }

        long fromDays = ChronoUnit.DAYS.between(fromDate, targetDate);
        long toDays = ChronoUnit.DAYS.between(targetDate, toDate);

        return fromDays >= 0 && toDays >= 0;
    }

    // 检查指定时间是否在未来3天内
    public static boolean isDateWithinNextThreeDays(LocalDate targetDate) {
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusDays(3);
        return isDateWithinRange(targetDate, currentDate, futureDate);
    }

    /**
     * 当前时间距离明天0点的秒数
     */
    public static long secondsUntilTomorrow() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime tomorrow = now.plusDays(1).with(LocalTime.MIDNIGHT);
        Duration duration = Duration.between(now, tomorrow);
        return duration.getSeconds();
    }

    @Nullable
    public static Date makeLocalDateTime2Date(LocalDateTime param) {
        if (param == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(param.atZone(zoneId).toInstant());
    }


    /**
     * 时间加毫秒
     *
     * @param localDateTime
     * @param param         毫秒
     * @return
     */
    public static LocalDateTime timePlusMs(LocalDateTime localDateTime, long param) {
        // 选择一个时区，例如系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将LocalDateTime转换为ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        // 给ZonedDateTime增加1毫秒
        ZonedDateTime updatedZonedDateTime = zonedDateTime.plus(param, ChronoUnit.MILLIS);
        // 转换回LocalDateTime（如果需要）
        return updatedZonedDateTime.toLocalDateTime();

    }

    /**
     * 检测日期格式
     *
     * @param param
     * @return
     */
    public static boolean chkDateFormate(String param){
        List<String> datePatterns = Arrays.asList(
            "yyyy/M/d",
            "yyyy/M/dd",
            "yyyy-M-d",
            "yyyy/MM/dd",
            "yyyy-MM-dd",
            "yyyy/MMM/dd"
        );

        for (String pattern : datePatterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                LocalDate localDate = LocalDate.parse(param, formatter);
                return true; // 返回 LocalDateTime
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }

        return false;
    }

    public static LocalDateTime parseFlexibleDate(String param){
        List<String> datePatterns = Arrays.asList(
            "yyyy/M/d",
            "yyyy/M/dd",
            "yyyy-M-d",
            "yyyy/MM/dd",
            "yyyy-MM-dd",
            "yyyy/MMM/dd"
        );

        for (String pattern : datePatterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                LocalDate localDate = LocalDate.parse(param, formatter);
                return localDate.atStartOfDay(); // 返回 LocalDateTime
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }

        return null;
    }

    public static boolean isValidDate(String dateStr, String dateFormat) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        try {
            // 尝试解析日期
            formatter.parse(dateStr);
            return true;
        } catch (DateTimeParseException e) {
            // 如果抛出异常，说明日期格式不正确
            return false;
        }
    }
}
