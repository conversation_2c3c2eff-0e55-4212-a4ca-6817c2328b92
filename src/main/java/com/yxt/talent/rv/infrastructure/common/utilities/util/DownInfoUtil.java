package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.ubiz.export.bean.DownInfo;

/**
 * @description:
 * @author: suocl
 * @time: 2024/1/11 16:30
 */
public class DownInfoUtil {
    /**
     * @param funcationName 下载中心functionCode,示例:pc_dlc_bbs_post_list
     * @return
     */
    public static DownInfo getDownInfo(String funcationName) {
        DownInfo downInfo = new DownInfo();
        downInfo.setAppCode(AppConstants.DOWNLOAD_APP_CODE);
        downInfo.setModuleCode(AppConstants.DOWNLOAD_MODULE_CODE);
        downInfo.setFunctionName(funcationName);
        downInfo.setUseOriginFileName(true);
        return downInfo;
    }
}
