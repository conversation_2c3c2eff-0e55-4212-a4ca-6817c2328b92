package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.talent.rv.infrastructure.common.utilities.util.jwt.JwtUserInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.experimental.UtilityClass;
import org.joda.time.DateTime;

import java.nio.charset.StandardCharsets;
import java.util.Date;

@UtilityClass
public class JwtUtil {

    private static final String ORG_ID = "orgId";
    private static final String USER_ID = "userId";
    private static final String USER_NAME = "userName";
    private static final String FULL_NAME = "fullName";

    /**
     * 密钥加密token
     *
     * @param claim  用户信息
     * @param secret 密钥
     * @param expire 过期时间，单位秒
     */
    public static String generateToken(JwtUserInfo claim, String secret, int expire) {
        Date exp = expire > 0 ? DateTime.now().plusSeconds(expire).toDate() : new Date(expire);
        return Jwts.builder()
            .claim(ORG_ID, claim.getOrgId())
            .claim(USER_ID, claim.getId())
            .claim(USER_NAME, claim.getName())
            .claim(FULL_NAME, claim.getFullName())
            .setExpiration(exp)
            .signWith(SignatureAlgorithm.HS512, secret.getBytes(StandardCharsets.UTF_8))
            .compact();
    }

    /**
     * 获取token中的用户信息
     */
    public static JwtUserInfo getInfoFromToken(String token, String secret) {
        Jws<Claims> claimsJws = parserToken(token, secret);
        Claims body = claimsJws.getBody();
        return new JwtUserInfo(getObjectValue(body.get(ORG_ID)), getObjectValue(body.get(USER_ID)),
            getObjectValue(body.get(USER_NAME)), getObjectValue(body.get(FULL_NAME)));
    }

    private static Jws<Claims> parserToken(String token, String secret) {
        return Jwts.parser()
            .setSigningKey(secret.getBytes(StandardCharsets.UTF_8))
            .parseClaimsJws(token);
    }

    private static String getObjectValue(Object obj) {
        return obj == null ? "" : obj.toString();
    }

}
