package com.yxt.talent.rv.infrastructure.common.utilities.util;

import cn.hutool.core.util.ArrayUtil;
import jakarta.annotation.Nullable;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;

@UtilityClass
public class SpelUtil {

    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    /**
     * 支持 #p0 参数索引的表达式解析
     *
     * @param rootObject 根对象,method 所在的对象
     * @param spel       表达式
     * @param method     目标方法
     * @param args       方法入参
     * @return 解析后的字符串
     */
    @Nullable
    public static <T> T parse(
            Object rootObject, String spel, Method method, Object[] args, Class<T> clazz) {
        if (StringUtils.isBlank(spel)) {
            return null;
        }
        //获取被拦截方法参数名列表(使用Spring支持类库)
        String[] paraNameArr = discoverer.getParameterNames(method);
        if (ArrayUtil.isEmpty(paraNameArr)) {
            return null;
        }
        StandardEvaluationContext context =
                new MethodBasedEvaluationContext(rootObject, method, args, discoverer);
        //把方法参数放入SPEL上下文中
        for (int i = 0; i < paraNameArr.length; i++) {
            context.setVariable(paraNameArr[i], args[i]);
        }
        return parser.parseExpression(spel).getValue(context, clazz);
    }

}
