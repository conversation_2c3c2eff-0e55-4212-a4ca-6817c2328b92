package com.yxt.talent.rv.infrastructure.common.utilities.util;

import cn.hutool.core.lang.Validator;
import com.yxt.common.util.Validate;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
public class ValidateUtil {

    /**
     * 校验年份，必须四位整数
     */
    public static void isYear(String str, String errKey) {
        if (StringUtils.isBlank(str)) {
            return;
        }
        Validate.isTrue(str.matches("^\\d{4}$"), errKey);
    }

    /**
     * 验证uuid
     */
    public static void isUuid(String str, String errKey) {
        if (!StringUtils.isNotBlank(str)) {
            return;
        }
        Validate.isTrue(Validator.isUUID(str), errKey);
    }

    public static void isSeriesId(String seriesId, String errKey) {
        // UUID 或者 类似 XL+5位数字
        if (StringUtils.isBlank(seriesId)) {
            return;
        }
        Validate.isTrue(Validator.isUUID(seriesId) || seriesId.matches("^XL\\d{5}$"), errKey);
    }

    /**
     * main方法，用于测试此类中的工具方法
     * @param args a
     */
    public static void main(String[] args) {
        // --- 测试 isYear ---
        System.out.println("--- 开始测试 isYear 方法 ---");

        // 案例1: 验证有效年份
        try {
            isYear("2023", "年份格式错误");
            System.out.println("isYear(\"2023\") - PASSED");
        } catch (Exception e) {
            System.out.println("isYear(\"2023\") - FAILED: " + e.getMessage());
        }

        // 案例2: 验证无效年份
        try {
            isYear("abcd", "年份格式错误");
            System.out.println("isYear(\"abcd\") - FAILED: 未按预期抛出异常");
        } catch (Exception e) {
            System.out.println("isYear(\"abcd\") - PASSED: 成功捕获异常 -> " + e.getMessage());
        }

        // 案例3: 验证空字符串
        try {
            isYear("", "年份格式错误");
            System.out.println("isYear(\"\") - PASSED");
        } catch (Exception e) {
            System.out.println("isYear(\"\") - FAILED: " + e.getMessage());
        }

        // --- 测试 isUuid ---
        System.out.println("\n--- 开始测试 isUuid 方法 ---");

        // 案例1: 验证有效UUID
        String validUuid = java.util.UUID.randomUUID().toString();
        try {
            isUuid(validUuid, "UUID格式错误");
            System.out.println("isUuid(\"" + validUuid + "\") - PASSED");
        } catch (Exception e) {
            System.out.println("isUuid(\"" + validUuid + "\") - FAILED: " + e.getMessage());
        }

        // 案例2: 验证无效UUID
        try {
            isUuid("not-a-uuid", "UUID格式错误");
            System.out.println("isUuid(\"not-a-uuid\") - FAILED: 未按预期抛出异常");
        } catch (Exception e) {
            System.out.println("isUuid(\"not-a-uuid\") - PASSED: 成功捕获异常 -> " + e.getMessage());
        }

        // 案例3: 验证空字符串
        try {
            isUuid(null, "UUID格式错误");
            System.out.println("isUuid(null) - PASSED");
        } catch (Exception e) {
            System.out.println("isUuid(null) - FAILED: " + e.getMessage());
        }

        // --- 测试 isSeriesId ---
        System.out.println("\n--- 开始测试 isSeriesId 方法 ---");

        // 案例1: 有效UUID
        String uuidSeriesId = java.util.UUID.randomUUID().toString();
        try {
            isSeriesId(uuidSeriesId, "SeriesId格式错误");
            System.out.println("isSeriesId(\"" + uuidSeriesId + "\") - PASSED");
        } catch (Exception e) {
            System.out.println("isSeriesId(\"" + uuidSeriesId + "\") - FAILED: " + e.getMessage());
        }

        // 案例2: 有效的 'XL+5位数字'
        try {
            isSeriesId("XL12345", "SeriesId格式错误");
            System.out.println("isSeriesId(\"XL12345\") - PASSED");
        } catch (Exception e) {
            System.out.println("isSeriesId(\"XL12345\") - FAILED: " + e.getMessage());
        }

        // 案例3: 无效格式
        try {
            isSeriesId("YL12345", "SeriesId格式错误");
            System.out.println("isSeriesId(\"YL12345\") - FAILED: 未按预期抛出异常");
        } catch (Exception e) {
            System.out.println("isSeriesId(\"YL12345\") - PASSED: 成功捕获异常 -> " + e.getMessage());
        }

        // 案例4: 空字符串
        try {
            isSeriesId("", "SeriesId格式错误");
            System.out.println("isSeriesId(\"\") - PASSED");
        } catch (Exception e) {
            System.out.println("isSeriesId(\"\") - FAILED: " + e.getMessage());
        }

    }
}
