package com.yxt.talent.rv.infrastructure.common.utilities.util.jwt;

import com.yxt.common.util.jwt.IJwtInfo;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class JwtUserInfo implements IJwtInfo {

    /**
     * 机构/租户id
     */
    private String orgId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户账号，等同于userName
     */
    private String userName;


    /**
     * 用户名
     */
    private String fullName;

    @Override
    public String getId() {
        return this.userId;
    }

    @Override
    public String getName() {
        return this.userName;
    }
}
