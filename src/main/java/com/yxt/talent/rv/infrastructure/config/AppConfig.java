package com.yxt.talent.rv.infrastructure.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.BasicPolymorphicTypeValidator;
import com.fasterxml.jackson.databind.jsontype.PolymorphicTypeValidator;
import com.yxt.common.config.WafRedisClientConfig;
import com.yxt.common.repo.RedisRepository;
import com.yxt.spsdk.audit.AuditLogConfig;
import com.yxt.spsdk.logsave.LogSaveConfig;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.RvAuditLogPointEnum;
import com.yxt.ubiz.export.component.ExportMDUtil;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static com.yxt.common.config.WafRedisClientConfig.REDIS_CONNECTION_FACTORY_BEAN;

@Slf4j
@EnableCaching
@RequiredArgsConstructor
@SpringBootConfiguration
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableFeignClients(basePackages = "com.yxt.talent.rv.infrastructure.service.auth.tinahe")
public class AppConfig {

    private final ObjectMapper objectMapper;

    @Bean(WafRedisClientConfig.REDIS_PROPERTIES_BEAN)
    @ConfigurationProperties("sptalentapi.redis")
    public RedisProperties talentRedisProperties() {
        return new RedisProperties();
    }

    @Bean
    @Primary
    public StringRedisTemplate sprvStringRedisTemplate(
            @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate(redisConnectionFactory);
        RedisSerializer<Object> jsonSerializer =
                new GenericJackson2JsonRedisSerializer(objectMapper.copy());
        // 功能增强,增加压缩机制(gzip压缩+jackson序列化)
        //        template.setValueSerializer(new GzipSerializer(jsonSerializer));
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        template.setKeySerializer(RedisSerializer.string());
        template.setHashKeySerializer(RedisSerializer.string());
        return template;
    }

    @Bean
    @Primary
    public RedisTemplate<String, Object> sprvRedisTemplate(
            @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        RedisSerializer<Object> jsonSerializer =
                new GenericJackson2JsonRedisSerializer(objectMapper.copy());
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        template.setKeySerializer(RedisSerializer.string());
        template.setHashKeySerializer(RedisSerializer.string());
        return template;
    }

    @Bean
    public CacheManager redisCacheManager(
            @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory) {
        RedisCacheWriter redisCacheWriter =
                RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
        RedisCacheConfiguration defaultCacheConfig = getDefaultCacheConfiguration();
        return new RedisCacheManager(redisCacheWriter,
                defaultCacheConfig.entryTtl(Duration.ofHours(8L)), getRedisCacheConfigurationMap());
    }

    private RedisCacheConfiguration getDefaultCacheConfiguration() {
        ObjectMapper copyObjectMapper = objectMapper.copy();

        // 保存缓存类型信息
        PolymorphicTypeValidator ptv = BasicPolymorphicTypeValidator.builder()
                .allowIfBaseType(Object.class)
                .build();
        copyObjectMapper.activateDefaultTyping(ptv, ObjectMapper.DefaultTyping.NON_FINAL);

        // 不启用缓存类型，即不在保存的缓存中保存类型信息，但是会导致反序列化时报错，因为缓存中没有类型信息
        //        objectMapper.deactivateDefaultTyping();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        RedisSerializer<Object> jsonSerializer =
                new GenericJackson2JsonRedisSerializer(copyObjectMapper);
        RedisSerializationContext.SerializationPair<Object> pair =
                RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer);
        return RedisCacheConfiguration.defaultCacheConfig()
                .serializeValuesWith(pair)
                .disableCachingNullValues();
    }

    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> map = new HashMap<>(8);
        RedisCacheConfiguration config = getDefaultCacheConfiguration();
        map.put(RedisKeys.CK_ORG, config.entryTtl(Duration.ofHours(8L)));
        map.put(RedisKeys.CK_ORG_ROOT_DEPT, config.entryTtl(Duration.ofHours(8L)));
        map.put(RedisKeys.CK_ORG_PERF_GRADE, config.entryTtl(Duration.ofMinutes(10L)));
        map.put(RedisKeys.CK_ACTIVITY_CREATE_TIME, config.entryTtl(Duration.ofMinutes(10L)));
        return map;
    }

    @Bean
    public RedisRepository talentRedisRepository(
            @Qualifier(REDIS_CONNECTION_FACTORY_BEAN) RedisConnectionFactory redisConnectionFactory,
            StringRedisTemplate sprvStringRedisTemplate) {
        return new RedisRepository(
                new StringRedisTemplate(redisConnectionFactory), sprvStringRedisTemplate);
    }

    @Bean
    @ConditionalOnMissingClass("com.yxt.aom.base.config.AomRedisConfig")
    public ExportMDUtil initExportBean(
            RocketMQTemplate rocketMQTemplate, RedisTemplate<String, Object> sprvRedisTemplate) {
        //noinspection InstantiationOfUtilityClass
        return new ExportMDUtil(sprvRedisTemplate, rocketMQTemplate); // NOSONAR
    }

    @Primary
    @Bean(name = AbstractApplicationContext.APPLICATION_EVENT_MULTICASTER_BEAN_NAME)
    public ApplicationEventMulticaster simpleApplicationEventMulticaster() {
        //        eventMulticaster.setErrorHandler(t -> log.error("LOG67020:", t));
        return new SimpleApplicationEventMulticaster();
    }

    /**
     * 人才发展内部日志告警系统
     *
     * @return
     */
    @Bean
    public LogSaveConfig logSaveConfig(AppProperties appProperties) {
        LogSaveConfig logSave = new LogSaveConfig();
        logSave.setLogTable("sprv_sys_operate_log");
        logSave.setBizBasePkg(appProperties.getLogConfig().getBizLogPkg());
        AppProperties.SysLogConfig logConfig = appProperties.getLogConfig();
        logSave.setDingURL(logConfig.getDingRobotUrl());
        logSave.setEnabled(logConfig::isEnabled);
        logSave.setSaveStyle(logConfig.getSaveStyle());
        logSave.setObserverFunc((bizLine) -> appProperties.getLogConfig().getObserver());
        log.debug(
            "LOG10372:enabled={}, onlyException={}, saveStyle={}, dingURL={}, bizLogPkg={}",
            logSave.getEnabled().get(), logSave.isOnlyException(), logSave.getSaveStyle(),
            logSave.getDingURL(), logSave.getBizBasePkg());
        return logSave;
    }

    @Bean
    public AuditLogConfig auditLogConfig() {
        AuditLogConfig ret = new AuditLogConfig();
        ret.setEnabledGetter(() -> true);
        //业务定义的操作日志枚举AuditLogPointEnum
        ret.setLogPointEnums(RvAuditLogPointEnum.values());
        return ret;
    }

}
