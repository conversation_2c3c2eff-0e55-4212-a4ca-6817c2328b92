package com.yxt.talent.rv.infrastructure.config.swagger;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * swagger扩展注解，解决分页参数每个方法都要写一遍的问题
 * 升级springboot3之后不能使用了
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SwaggerPageQuery {
    String value() default "";
}
