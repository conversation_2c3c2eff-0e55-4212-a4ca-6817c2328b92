package com.yxt.talent.rv.infrastructure.persistence.cache;

import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.BeanHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * @since 2021/6/26 17:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonCacheRepository {

    private final RedisRepository talentRedisRepository;

    private static <T> boolean isNotEmpty(T t) {
        if (t == null) {
            return false;
        } else if (t instanceof Collection) {
            return CollectionUtils.isNotEmpty((Collection<?>) t);
        } else if (t instanceof Map<?, ?> map) {
            return !(map).isEmpty();
        } else if (t instanceof Optional) {
            return ((Optional<?>) t).isPresent();
        } else if (t instanceof Object[] arr) {
            return arr.length != 0;
        } else if (t instanceof String str) {
            return StringUtils.isNotBlank(str);
        }
        return true;
    }

    /**
     * 查询缓存包装方法,【注意】最大支持缓存30天，超过30天会被重置为30天
     *
     * @param key
     * @param ttl  缓存时间, 传0不做缓存处理，最大支持30天
     * @param unit 时间单位
     * @param cb   如果缓存未命中，执行查询数据库的方法
     * @param clz  从缓存中拉出的数据，需要反序列化的目标class对象
     * @return
     * <AUTHOR>
     * @date 2018年7月18日 下午5:26:11
     */
    public <T> Optional<T> cache(
            String key, long ttl, TimeUnit unit, Cacheable<Optional<T>> cb, Class<? extends T> clz,
            Class<?>... parameterizedTypeClass) {
        if (key == null) {
            throw new IllegalArgumentException("Key cannot be null");
        }
        if (cb == null) {
            throw new IllegalArgumentException("Callback cannot be null");
        }

        String json = talentRedisRepository.opsForValue().get(key);
        if (StringUtils.isNotEmpty(json)) {
            return Optional.ofNullable(BeanHelper.json2Bean(json, clz, parameterizedTypeClass));
        }

        Optional<T> opt;
        try {
            opt = cb.process();
        } catch (Exception e) {
            log.error("LOG12305:Error retrieving data from database", e);
            return Optional.empty();
        }

        return opt.map(t -> {
            if (!isNotEmpty(t)) {
                log.debug("LOG12315:Data is empty, no cache");
                return t;
            }

            String cacheVal = BeanHelper.bean2Json(t, ALWAYS);
            if (!StringUtils.isNotBlank(cacheVal)) {
                log.debug("LOG12325:Data is empty, no cache");
                return t;
            }

            // 禁止缓存超过30天
            long maxTtlDays = TimeUnit.DAYS.toSeconds(30);
            if (ttl > 0 && ttl <= maxTtlDays) {
                log.debug("LOG12335:Cache data, key={}, ttl={}, unit={}", key, ttl, unit);
                talentRedisRepository.opsForValue().set(key, cacheVal, ttl, unit);
            } else if (ttl > maxTtlDays || ttl == -1) {
                log.warn("LOG12345:Cache data, key={}, ttl:30 days", key);
                talentRedisRepository.opsForValue()
                        .set(key, cacheVal, maxTtlDays, TimeUnit.SECONDS);
            } else {
                log.warn("LOG12355:缓存过期时间设置的不合法,不做缓存处理。key:{}; ttl:{}", key, ttl);
            }
            return t;
        });
    }

    public void removeKey(String key) {
        if (StringUtils.isNotEmpty(key)) {
            talentRedisRepository.delete(key);
        }
    }

    /**
     * 缓存双删
     *
     * @param key
     * @param evictable
     */
    public void evict(String key, CacheEvictable evictable) {
        if (key == null) {
            throw new IllegalArgumentException("Key cannot be null");
        }
        if (evictable == null) {
            throw new IllegalArgumentException("Database delete callback cannot be null");
        }

        // Step 1: 先删除缓存
        talentRedisRepository.delete(key);

        // Step 2: 再删库
        try {
            evictable.process();
        } catch (Exception e) {
            log.error("LOG12805:Error deleting data from database", e);
            throw e; // rethrow or handle accordingly
        }

        // Step 3: 等待100ms之后，再删除一次缓存
        try {
            TimeUnit.MILLISECONDS.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("LOG12365:", e);
        }
        talentRedisRepository.delete(key);
    }

}
