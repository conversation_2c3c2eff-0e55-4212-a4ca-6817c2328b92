package com.yxt.talent.rv.infrastructure.persistence.cache;

import cn.hutool.core.lang.Pair;
import com.yxt.common.repo.RedisRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RedisRepo {
    private final RedisRepository talentRedisRepository;

    public RedisRepository getRedisRepository() {
        return talentRedisRepository;
    }

    public void expire(String key, Pair<Integer, TimeUnit> expire) {
        getRedisRepository().expire(key, expire.getKey(), expire.getValue());
    }

    public String getHmValue(String key, String subKey) {
        return getRedisRepository().getHashValue(key, subKey);
    }

    public void setHmValue(String key, String subKey, String value, long ttl, TimeUnit timeUnit) {
        getRedisRepository().setHashValue(key, subKey, value);
        getRedisRepository().expire(key, ttl, timeUnit);
    }

    public void setValue(String key, String value, Pair<Long, TimeUnit> expire) {
        getRedisRepository().setValue(key, value, expire.getKey(), expire.getValue());
    }
    public void setValue(String key, String value, long ttl, TimeUnit timeUnit) {
        getRedisRepository().setValue(key, value, ttl, timeUnit);
    }

    public String getValue(String key) {
        return getRedisRepository().getValueByKey(key);
    }

    public void setHmValue(String key, String subKey, String value, Pair<Long, TimeUnit> expire) {
        getRedisRepository().setHashValue(key, subKey, value);
        getRedisRepository().expire(key, expire.getKey(), expire.getValue());
    }

    public void removeKey(String key) {
        getRedisRepository().delete(key);
    }


}
