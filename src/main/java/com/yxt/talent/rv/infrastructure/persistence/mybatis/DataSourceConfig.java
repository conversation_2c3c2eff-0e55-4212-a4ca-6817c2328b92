package com.yxt.talent.rv.infrastructure.persistence.mybatis;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
public class DataSourceConfig {

    @Primary
    @Bean("rvDataSource")
    @ConfigurationProperties("spring.datasource.druid.rv")
    public DataSource dataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Primary
    @Bean(AppConstants.DB_0_TRANSACTION_MANAGER)
    public PlatformTransactionManager db0TransactionManager(
            @Qualifier("rvDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
