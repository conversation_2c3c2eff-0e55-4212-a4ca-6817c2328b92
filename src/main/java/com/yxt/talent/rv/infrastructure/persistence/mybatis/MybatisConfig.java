package com.yxt.talent.rv.infrastructure.persistence.mybatis;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.extend.CustomizedSqlInjector;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.io.IOException;
import java.util.Optional;
import java.util.stream.Stream;

abstract class MybatisConfig {

    private static final ResourcePatternResolver RESOURCE_RESOLVER =
            new PathMatchingResourcePatternResolver();

    protected void applyConfiguration(MybatisSqlSessionFactoryBean factory) {
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setLogImpl(Slf4jImpl.class);
        factory.setConfiguration(configuration);
        GlobalConfig globalConfig = GlobalConfigUtils.defaults().setSqlInjector(new CustomizedSqlInjector());
        factory.setGlobalConfig(globalConfig);
    }

    protected Resource[] resolveMapperLocations(String... mapperLocations) {
        return Stream.of(Optional.ofNullable(mapperLocations).orElse(new String[0]))
                .flatMap(location -> Stream.of(getResources(location))).toArray(Resource[]::new);
    }

    private Resource[] getResources(String location) {
        try {
            return RESOURCE_RESOLVER.getResources(location);
        } catch (IOException e) {
            return new Resource[0];
        }
    }
}
