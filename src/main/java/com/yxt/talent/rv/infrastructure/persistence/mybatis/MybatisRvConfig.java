package com.yxt.talent.rv.infrastructure.persistence.mybatis;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yxt.common.interceptor.DbHintInterceptor;
import com.yxt.common.interceptor.DbhintPaginationInnerInterceptor;
import com.yxt.spmodel.facade.service.SpmodelSqlService;
import com.yxt.spsdk.common.interceptor.MybatisLogInterceptor;
import com.yxt.spsdk.spmodel.bean.SpmodelQueryConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.extend.CustomizedSqlInjector;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Objects;

@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(
        value = {"com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper", "com.yxt.spsdk.**.mapper"},
        sqlSessionFactoryRef = MybatisRvConfig.RV_SQL_SESSION_FACTORY)
public class MybatisRvConfig extends MybatisConfig {

    public static final String RV_SQL_SESSION_FACTORY = "rvSqlSessionFactory";

    @Primary
    @Bean(RV_SQL_SESSION_FACTORY)
    public SqlSessionFactory sqlSessionFactory(
            @Qualifier("rvDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        factory.setVfs(SpringBootVFS.class);
        factory.setTypeAliasesPackage(
                "com.yxt.talent.rv.infrastructure.persistence.mybatis.po");
        factory.setMapperLocations(resolveMapperLocations("classpath*:mapper/**/*Mapper*.xml"));
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(dbhintPaginationInnerInterceptor());
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        factory.setPlugins(new DbHintInterceptor(), mybatisPlusInterceptor, new MybatisLogInterceptor());
        applyConfiguration(factory);

        return Objects.requireNonNull(factory.getObject());
    }

    @Bean
    public DbhintPaginationInnerInterceptor dbhintPaginationInnerInterceptor() {
        DbhintPaginationInnerInterceptor dbhintPaginationInnerInterceptor =
            new DbhintPaginationInnerInterceptor();
        dbhintPaginationInnerInterceptor.setDbType(DbType.MYSQL);
        // 关闭MP left join的自动优化
        dbhintPaginationInnerInterceptor.setOptimizeJoin(false);
        return dbhintPaginationInnerInterceptor;
    }

    @Primary
    @Bean("rvSqlSessionTemplate")
    public SqlSessionTemplate rvSqlSessionTemplate(
            @Qualifier(RV_SQL_SESSION_FACTORY) SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean("rvBatchSqlSession")
    public SqlSessionTemplate rvBatchSqlSession(
            @Qualifier(RV_SQL_SESSION_FACTORY) SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory, ExecutorType.BATCH);
    }

    @Bean
    public SpmodelQueryConfig spmodelQueryConfig(SpmodelSqlService spmodelSqlService) {
        return new SpmodelQueryConfig("com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.spmodel", spmodelSqlService);
    }
}
