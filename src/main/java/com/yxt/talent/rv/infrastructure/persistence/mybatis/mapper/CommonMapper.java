package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.Mapper;
import com.yxt.common.util.BatchOperationUtil;
import jakarta.annotation.Nullable;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.function.Consumer;

public interface CommonMapper<T> extends Mapper<T> {

    Logger log = LoggerFactory.getLogger(CommonMapper.class);

    /**
     * 查出来的记录去重, 要求T实现自定义的equals和hashCode方法
     */
    @jakarta.annotation.Nonnull
    default Collection<T> deduplicate(Collection<T> records) {
        return new ArrayList<>(new HashSet<>(records));
    }

    /**
     * 分割集合,批量执行
     *
     * @param coll
     * @param callBack
     */
    default void batchExecute(
            @Nullable Collection<T> coll, BatchOperationUtil.BatchExecuteCallBack<T> callBack) {
        if (CollectionUtils.isEmpty(coll)) {
            return;
        }
        List<T> list = coll instanceof List ? (List<T>) coll : new ArrayList<>(coll);
        BatchOperationUtil.batchExecute(list, 200, callBack);
    }

    /**
     * 分割集合,批量执行, 支持自定义批次大小
     *
     * @param coll
     * @param batchSize
     * @param callBack
     */
    default void batchExecute(
            @Nullable Collection<T> coll, int batchSize,
            BatchOperationUtil.BatchExecuteCallBack<T> callBack) {
        if (CollectionUtils.isEmpty(coll)) {
            return;
        }
        List<T> list = coll instanceof List ? (List<T>) coll : new ArrayList<>(coll);
        BatchOperationUtil.batchExecute(list, 200, callBack);
    }

    /**
     * 遍历集合,一个个执行
     *
     * @param coll
     * @param tConsumer
     */
    default void forEachExecute(@Nullable Collection<T> coll, Consumer<T> tConsumer) {
        if (CollectionUtils.isEmpty(coll)) {
            return;
        }
        coll.forEach(tConsumer);
    }

    void insertBatchSomeColumn(Collection<T> entityList);

}
