package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityPerfMapper extends CommonMapper<ActivityPerfPO> {
    int insert(ActivityPerfPO record);

    int insertOrUpdate(ActivityPerfPO record);

    ActivityPerfPO selectByPrimaryKey(String id);

    ActivityPerfPO selectById(@Param("orgId") String orgId, @Param("id") String id);

    List<ActivityPerfPO> selectByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);

    void deleteById(@Param("orgId") String orgId, @Param("opUserId") String opUserId, @Param("id") String id);

    List<ActivityPerfPO> findByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    ActivityPerfPO selectByAomActId(@Param("orgId") String orgId, @Param("aomActId") String aomActId);

    List<ActivityPerfPO> findAllValidActs();

    List<String> findAllPeriodIds(@Param("orgId") String orgId);

    /**
     * 根据机构ID查询所有绩效活动
     *
     * @param orgId 机构ID
     * @return 绩效活动列表
     */
    List<ActivityPerfPO> selectByOrgId(@Param("orgId") String orgId);
}