package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityPerfResultMapper extends CommonMapper<ActivityPerfResultPO> {

    List<ActivityPerfResultPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(ActivityPerfResultPO record);

    int insertOrUpdate(ActivityPerfResultPO record);

    ActivityPerfResultPO selectByPrimaryKey(String id);

    int insertList(@Param("list") List<ActivityPerfResultPO> list);

    int updateBatch(@Param("list") List<ActivityPerfResultPO> list);

    List<ActivityPerfResultPO> selectByUserIdAndActId(@Param("orgId") String orgId, @Param("actId") String actId,
            @Param("userIds") List<String> userIds);

    Long selectFinishCount(@Param("orgId") String orgId, @Param("actId") String id);

    Long selectFinishCountRange(@Param("orgId") String orgId, @Param("actId") String id,
            @Param("userIds") List<String> userIds);

    List<ActivityPerfResultPO> findByProfIdsAndUserIds(@Param("orgId") String orgId,
            @Param("actIds") List<String> xpdProfIds, @Param("userIds") List<String> userIds);
}