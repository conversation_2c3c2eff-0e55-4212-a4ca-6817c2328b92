package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityProfileIndicatorMapper extends CommonMapper<ActivityProfileIndicatorPO> {

    List<ActivityProfileIndicatorPO> selectByOrgId(@Param("orgId")String orgId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ActivityProfileIndicatorPO record);

    void batchInsert(@Param("list") List<ActivityProfileIndicatorPO> list);

    int insertOrUpdate(ActivityProfileIndicatorPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ActivityProfileIndicatorPO selectByPrimaryKey(String id);

    void deleteByActvId(@Param("orgId") String orgId, @Param("actProfileId") String actProfileId,
            @Param("optUserId") String optUserId);

    List<ActivityProfileIndicatorPO> findByActProfileId(@Param("orgId") String orgId,
            @Param("actProfileId") String actProfileId);
}