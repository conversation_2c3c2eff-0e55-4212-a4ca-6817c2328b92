package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityProfileMapper extends CommonMapper<ActivityProfilePO> {

    List<ActivityProfilePO> selectByOrgId(@Param("orgId")String orgId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ActivityProfilePO record);

    int insertOrUpdate(ActivityProfilePO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ActivityProfilePO selectByPrimaryKey(String id);

    ActivityProfilePO findByIdAndOrgId(@Param("id") String id, @Param("orgId") String orgId);

    void updateByPrimaryKey(ActivityProfilePO profilePO);

    void deleteByPrimaryKey(@Param("orgId") String orgId, @Param("id") String actProfileId,
            @Param("optUserId") String optUserId);

    List<ActivityProfilePO> findAllValidActProfiles();
}