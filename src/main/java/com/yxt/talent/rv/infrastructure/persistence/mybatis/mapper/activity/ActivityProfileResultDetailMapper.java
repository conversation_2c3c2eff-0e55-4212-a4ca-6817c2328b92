package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityProfileResultDetailMapper extends CommonMapper<ActivityProfileResultDetailPO> {

    List<ActivityProfileResultDetailPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(ActivityProfileResultDetailPO record);

    int insertOrUpdate(ActivityProfileResultDetailPO record);

    ActivityProfileResultDetailPO selectByPrimaryKey(String id);

    List<ActivityProfileResultDetailPO> findByActProfileIdAndResultIds(@Param("orgId") String orgId,
            @Param("userResultIds") List<String> userResultIds, @Param("actProfileId") String actProfileId);

    void insertBatch(@Param("list") List<ActivityProfileResultDetailPO> profileResultDetailPOList);

    void deleteByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);
}