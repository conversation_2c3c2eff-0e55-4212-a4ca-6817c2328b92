package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityProfileResultMapper extends CommonMapper<ActivityProfileResultPO> {

    List<ActivityProfileResultPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(ActivityProfileResultPO record);

    int insertOrUpdate(ActivityProfileResultPO record);

    ActivityProfileResultPO selectByPrimaryKey(String id);

    long findFinishedUserCount(@Param("orgId") String orgId, @Param("actProfileId") String actProfileId);

    long findFinishedUserCountRangeUser(@Param("orgId") String orgId, @Param("actProfileId") String actProfileId,
            @Param("userIds") List<String> userIds);

    List<ActivityProfileResultPO> findByActProfileIdAndUserIds(@Param("orgId") String orgId,
            @Param("actProfileId") String actProfileId, @Param("userIds") List<String> userIds);

    void batchInsert(@Param("list") List<ActivityProfileResultPO> profileResultPOList);

    void deleteByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);
}