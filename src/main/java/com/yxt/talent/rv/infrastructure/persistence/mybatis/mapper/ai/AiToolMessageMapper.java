package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO;
import lombok.NonNull;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AiToolMessageMapper extends CommonMapper<AiToolMessagePO> {

    int insert(AiToolMessagePO data);

    int insertOrUpdate(AiToolMessagePO data);

    AiToolMessagePO selectByPrimaryKey(Long id);

    AiToolMessagePO selectByOrgIdAndId(
        @NonNull @Param("orgId") String orgId, @NonNull @Param("id") Long id);

    void deleteByOrgIdAndId(@Param("orgId") String orgId, @Param("id") Long id);

    default void insertOrUpdateBatch(Collection<AiToolMessagePO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    void batchInsertOrUpdate(List<AiToolMessagePO> entities);

    Collection<AiToolMessagePO> selectByOrgIdAndSessionId(
        @Param("orgId") String orgId, @Param("sessionId") String sessionId);

    int updateById(@Param("updated") AiToolMessagePO updated);
}