package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 【注意】来自aom定义的表，只应用于查询，禁止修改
 */
@Mapper
public interface RvActivityMapper {

    @Select("""
    select 1 from rv_activity where org_id = #{orgId} and deleted = 0
    and category_id in (
            select n.descnt_id from rv_tree_node_relation n where n.org_id=#{orgId} and n.tree_id=#{treeId} and
            n.ancr_id = #{catalogId} and n.deleted = 0
            ) limit 1
    """)
    Integer existActvByCategoryId(
        @Param("orgId") String orgId,
        @Param("treeId") String treeId,
        @Param("catalogId") String catalogId);

    @Select("""
        select actv_name
                     from rv_activity where  org_id = #{orgId} and id = #{id}
    """)
    String selectAcNameById(@Param("orgId") String orgId,
        @Param("id") String id);

}
