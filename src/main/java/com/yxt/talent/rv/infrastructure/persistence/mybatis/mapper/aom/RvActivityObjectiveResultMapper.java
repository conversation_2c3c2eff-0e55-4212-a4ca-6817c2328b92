package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import com.yxt.talent.rv.application.aom.dto.ActivityObjectiveResultWithActivityInfoDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityObjectiveResultPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 【注意】来自aom定义的表，只应用于查询，禁止修改
 */
public interface RvActivityObjectiveResultMapper extends CommonMapper<ActivityObjectiveResultPO> {

    int insert(ActivityObjectiveResultPO record);

    ActivityObjectiveResultPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ActivityObjectiveResultPO record);

    int updateBatch(@Param("list") List<ActivityObjectiveResultPO> list);

    int batchInsert(@Param("list") List<ActivityObjectiveResultPO> list);

    List<XpdIndicatorResultDto> queryByUserIds(
        @Param("orgId") String orgId,
        @Param("actvIds") Collection<String> actvIds,
        @Param("userIds") Collection<String> userIds,
        @Param("objectiveIds") Collection<String> objectiveIds);

    /**
     * 查询指定活动和用户的所有指标结果
     */
    List<ActivityObjectiveResultPO> selectByActvIdAndUserId(
        @Param("orgId") String orgId,
        @Param("actvId") String actvId,
        @Param("userId") String userId);

    /**
     * 根据AOM项目ID查询用户的最新活动指标结果，在SQL中完成去重逻辑
     * 对于相同指标，自动选择活动创建时间最新的结果，无需代码层面再次处理
     */
    List<ActivityObjectiveResultWithActivityInfoDTO> selectLatestIndicatorResultsByAomProjectId(
        @Param("orgId") String orgId,
        @Param("aomProjectId") String aomProjectId,
        @Param("userId") String userId);
}