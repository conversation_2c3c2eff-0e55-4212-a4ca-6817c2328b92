package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.bean.part.PartMember4List;
import com.yxt.talent.rv.application.activity.dto.ActMemberUser;
import com.yxt.talent.rv.application.activity.dto.ActMemberUserCriteria;
import com.yxt.talent.rv.application.activity.dto.ActMemberUserSearchParam;
import com.yxt.talent.rv.application.activity.dto.UserProjectDTO;
import com.yxt.talent.rv.controller.client.authprj.query.PrjListQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 【注意】来自aom定义的表，只应用于查询，禁止修改
 */
public interface RvActivityParticipationMemberMapper extends CommonMapper<RvActivityParticipationMemberPO> {
    long findTotalUserCount(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("participationId") Long participationId);

    List<String> findAllUserIdByActId(@Param("orgId") String orgId, @Param("actvId") String actvId);

    IPage<RvActivityParticipationMemberPO> pageUser(@Param("pageable") Page<RvActivityParticipationMemberPO> pageable,
            @Param("orgId") String orgId, @Param("actvId") String actvId,
            @Param("searchParam") ActMemberUserSearchParam searchParam);

    IPage<RvActivityParticipationMemberPO> pageUserByPartId(@Param("pageable") Page<RvActivityParticipationMemberPO> pageable,
        @Param("orgId") String orgId, @Param("actvId") String actvId,
        @Param("participationId") Long participationId);

    IPage<ActMemberUser> listPage(Page<ActMemberUser> page, @Param("orgId") String orgId, @Param("criteria") ActMemberUserCriteria criteria);

    List<String> findUserIdByActIdAndUserIds(@Param("orgId") String orgId, @Param("actvId") String actvId,
        @Param("participationId") Long participationId,
        @Param("userIds") List<String> userIds);

    IPage<ActMemberUser> listPrjUserPage(Page<ActMemberUser> page,
        @Param("orgId") String orgId,
        @Param("criteria") ActMemberUserCriteria criteria);

    List<RvActivityParticipationMemberPO> listByActvId(@Param("orgId") String orgId, @Param("actvId") String actvId,
        @Param("userIds") Collection<String> userIds);


    long countByActvId(@Param("orgId") String orgId, @Param("actvId") String actvId, @Param("actId") String actId);

    IPage<UserProjectDTO> getUserProjectPage(IPage<UserProjectDTO> page, @Param("orgId") String orgId, @Param("userId") String userId,
        @Param("criteria") PrjListQuery criteria);
}