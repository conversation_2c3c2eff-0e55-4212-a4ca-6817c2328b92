package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvAssessmentActivityResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RvAssessmentActivityResultMapper
    extends CommonMapper<RvAssessmentActivityResultPO>, BaseMapper<RvAssessmentActivityResultPO> {
    int batchInsertOrUpdate(@Param("list") List<RvAssessmentActivityResultPO> list);

    /**
     * 根据aom子活动id和用户id查询aom子活动结果
     *
     * @param orgId
     * @param actvId    aom主项目id
     * @param actvRefId aom子活动id
     * @param userId
     * @return
     */
    RvAssessmentActivityResultPO selectByActvRefIdAndUserId(
        @Param("orgId") String orgId, @Param("actvId") String actvId, @Param("actvRefId") String actvRefId,
        @Param("userId") String userId);
}