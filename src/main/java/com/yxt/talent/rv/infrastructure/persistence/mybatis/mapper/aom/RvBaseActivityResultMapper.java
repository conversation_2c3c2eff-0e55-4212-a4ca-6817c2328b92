package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import com.yxt.talent.rv.application.xpd.common.dto.AomUserRefIdDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.BaseActivityResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 【注意】来自aom定义的表，只应用于查询，禁止修改
 */
public interface RvBaseActivityResultMapper extends CommonMapper<BaseActivityResultPO> {

    int insert(BaseActivityResultPO record);

    BaseActivityResultPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(BaseActivityResultPO record);

    int updateBatch(@Param("list") List<BaseActivityResultPO> list);

    int batchInsert(@Param("list") List<BaseActivityResultPO> list);

    /**
     *
     * @param orgId
     * @param actvId
     * @param itemId
     * @param userIds
     * @return
     */
    List<String> doneUserIdsByItemId(
        @Param("orgId") String orgId,
        @Param("actvId")String actvId,
        @Param("itemId")Long itemId,
        @Param("userIds")List<String> userIds);

    List<AomUserRefIdDto> doneRefIdsByUserId(
        @Param("orgId") String orgId,
        @Param("actvId")String actvId,
        @Param("userIds") Collection<String> userIds,
        @Param("refIds") Collection<String> refIds);

    /**
     *
     * @param orgId
     * @param actvType ActivityTypeEnum
     * @param actvId
     * @param userIds
     * @return
     */
    List<String> doneUserIds(
        @Param("orgId") String orgId,
        @Param("actvType")int actvType,
        @Param("actvId")String actvId,
        @Param("userIds")List<String> userIds);
}