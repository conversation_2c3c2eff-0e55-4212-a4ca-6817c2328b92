package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.yxt.talent.rv.application.authprj.dto.AomUserStatisDataDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 认证项目员工概览数据查询Mapper
 */
public interface AuthPrjUserOverviewMapper {

    /**
     * 计算员工总得分
     * 员工所有认证指标的得分之和，如果一个指标有多个活动都认证了，则取创建时间最新的活动的得分
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 总得分
     */
    BigDecimal calculateUserTotalScore(@Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userId") String userId);

    /**
     * 计算员工认证进度
     * 员工已完成的认证活动/所有认证活动
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 进度百分比
     */
    BigDecimal calculateUserProgress(@Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userId") String userId);

    /**
     * 获取员工认证分层结果名称
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 分层结果名称
     */
    String getUserLevelName(@Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userId") String userId);

    /**
     * 统计员工获得证书数量
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 证书数量
     */
    Integer countUserCertificates(@Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userId") String userId);


    /**
     * 获取员工项目统计数据
     * @param orgId
     * @param aomId
     * @param userId
     * @return
     */
    AomUserStatisDataDTO getUserStatisData(@Param("orgId") String orgId, @Param("aomId") String aomId, @Param("userId") String userId);
}
