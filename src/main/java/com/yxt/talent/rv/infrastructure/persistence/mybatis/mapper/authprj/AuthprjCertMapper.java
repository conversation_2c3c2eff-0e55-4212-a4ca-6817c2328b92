package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.controller.manage.authprj.query.AuthPrjCertIssue4Query;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertIssueVO;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjCertMapper extends CommonMapper<AuthprjCertPO>, BaseMapper<AuthprjCertPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjCertPO> list);

    /**
     * 根据认证项目ID查询证书列表（包含证书模板信息）
     */
    List<AuthPrjCertVO> selectCertListByAuthprjId(@Param("orgId") String orgId, @Param("authprjId") String authprjId);
}