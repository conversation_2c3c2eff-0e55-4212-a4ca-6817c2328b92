package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertRemindVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertRemindPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjCertRemindMapper extends CommonMapper<AuthprjCertRemindPO>, BaseMapper<AuthprjCertRemindPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjCertRemindPO> list);

    /**
     * 根据证书ID删除提醒配置
     */
    int deleteByAuthprjCertId(@Param("orgId") String orgId, @Param("authprjCertId") String authprjCertId);

    List<AuthPrjCertRemindVO> selectRemindListByAuthprjId(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 查询所有活跃的证书提醒配置
     */
    List<AuthPrjCertRemindVO> selectAllActiveRemindConfigs();
}