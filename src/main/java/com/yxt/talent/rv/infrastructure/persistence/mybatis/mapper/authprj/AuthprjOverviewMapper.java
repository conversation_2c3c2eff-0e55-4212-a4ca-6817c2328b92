package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjIndicatorStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 认证项目概览数据查询Mapper
 */
public interface AuthprjOverviewMapper {

    /**
     * 统计参与人数
     * 当前认证项目中加入项目的人数总计（包含启用、禁用、删除状态的人员，但不包含从项目中移除的人员）
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @return 参与人数
     */
    Integer countParticipants(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 统计完成人数
     * 参与人员中已完成所有认证任务的人数
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @return 完成人数
     */
    Integer countCompleted(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 统计通过人数
     * 完成人员中认证结果为"通过"及"通过"以上等级的人数
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @return 通过人数
     */
    Integer countPassed(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 获取指标统计数据
     * 包含指标层级信息、数据来源、总分、平均分
     *
     * @param orgId        机构ID
     * @param authprjId    认证项目ID
     * @param indicatorIds 指标ID列表
     * @return 指标统计数据列表
     */
    List<AuthPrjIndicatorStatisticsVO> getIndicatorStatistics(
            @Param("orgId") String orgId,
            @Param("authprjId") String authprjId,
            @Param("indicatorIds") List<String> indicatorIds);

}
