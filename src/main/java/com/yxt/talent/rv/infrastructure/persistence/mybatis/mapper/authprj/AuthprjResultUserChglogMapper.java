package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserChglogPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjResultUserChglogMapper extends CommonMapper<AuthprjResultUserChglogPO>, BaseMapper<AuthprjResultUserChglogPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjResultUserChglogPO> list);

    List<AuthprjResultUserChglogPO> selectByAuthprjIdAndUserId(@Param("orgId") String orgId, @Param("authPrjId") String authPrjId,@Param("userId") String userId);
}