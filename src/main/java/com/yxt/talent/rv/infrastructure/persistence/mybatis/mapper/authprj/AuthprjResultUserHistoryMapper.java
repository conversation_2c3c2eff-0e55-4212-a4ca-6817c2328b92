package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserHistoryPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjResultUserHistoryMapper extends CommonMapper<AuthprjResultUserHistoryPO>, BaseMapper<AuthprjResultUserHistoryPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjResultUserHistoryPO> list);

    List<AuthprjResultUserHistoryPO> selectByAuthprjIdAndUserId(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId,
        @Param("userId") String userId);
}