package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjUserIndicatorDetailVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AuthprjResultUserMapper extends BaseMapper<AuthprjResultUserPO> {

    int updateBatch(@Param("list") List<AuthprjResultUserPO> list);

    int batchInsert(@Param("list") List<AuthprjResultUserPO> list);

    int batchInsertOrUpdate(@Param("list") List<AuthprjResultUserPO> list);

    int insertOnDuplicateUpdate(AuthprjResultUserPO record);

    int insertOnDuplicateUpdateSelective(AuthprjResultUserPO record);

    AuthprjResultUserPO selectByAuthprjIdAndUserId(
            @Param("orgId") String orgId, @Param("authprjId") String authprjId,
            @Param("userId") String userId);

    /**
     * 获取员工指标详细统计数据
     * 包含指标层级信息、数据来源、总分、得分
     *
     * @param orgId        机构ID
     * @param authprjId    认证项目ID
     * @param userId       用户ID
     * @param indicatorIds 指标ID列表
     * @return 指标详细统计数据列表
     */
    List<AuthPrjUserIndicatorDetailVO> getUserIndicatorDetails(
            @Param("orgId") String orgId,
            @Param("authprjId") String authprjId,
            @Param("userId") String userId,
            @Param("indicatorIds") List<String> indicatorIds);

    List<AuthprjResultUserPO> selectByAuthprjIdAndUserIds(
            @Param("orgId") String orgId, @Param("authprjId") String authprjId,
            @Param("userIds") List<String> userIds);

    List<AuthprjResultUserPO> selectByAuthprjId(@Param("orgId") String orgId, @Param("authprjId") String authprjId);
    /**
     * 根据认证项目ID和用户ID删除分层结果（仅删除自动计算的）
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @param userId    用户ID
     */
    void deleteByAuthPrjAndUser(@Param("orgId") String orgId, @Param("authPrjId") String authPrjId, @Param("userId") String userId);
}