package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.CertLogPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CertLogMapper extends CommonMapper<CertLogPO>, BaseMapper<CertLogPO> {
    int batchInsertOrUpdate(@Param("list") List<CertLogPO> list);

    List<CertLogPO> selectByCertTempIdAndUserId(
        @Param("orgId") String orgId, @Param("sourceId") String sourceId, @Param("certTempId") String certTempId,
        @Param("userId") String userId);

    void updateCertStatus(
        @Param("orgId") String orgId, @Param("sourceId") String sourceId, @Param("certTempId") String certTempId,
        @Param("userIds") List<String> userIds, @Param("certStatus") Integer certStatus);
}