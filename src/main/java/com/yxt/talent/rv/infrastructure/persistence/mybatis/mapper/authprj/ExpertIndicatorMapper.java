package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertIndicatorPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpertIndicatorMapper extends CommonMapper<ExpertIndicatorPO>, BaseMapper<ExpertIndicatorPO> {
    int batchInsertOrUpdate(@Param("list") List<ExpertIndicatorPO> list);

    List<ExpertIndicatorPO> findByOrgIdAndExpertIdIn(@Param("orgId") String orgId, @Param("list") List<String> expertIds);

    void deleteByExpertId(@Param("orgId") String orgId, @Param("expertId") String expertId);
}