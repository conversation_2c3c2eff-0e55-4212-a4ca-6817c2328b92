package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.calimeet.dto.CaliRecordUserDTO;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliRecord4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetRecordVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordItemPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CalimeetRecordItemMapper extends CommonMapper<CalimeetRecordItemPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetRecordItemPO record);

    CalimeetRecordItemPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetRecordItemPO record);

    int updateBatch(@Param("list") List<CalimeetRecordItemPO> list);

    int batchInsert(@Param("list") List<CalimeetRecordItemPO> list);

    IPage<CaliMeetRecordVO> pageQuery(
        IPage<CaliMeetRecordVO> requestPage, @Param("orgId") String orgId,
        @Param("query") CaliRecord4Query query);

    List<CaliRecordUserDTO> listAll(@Param("orgId") String orgId,
        @Param("query") CaliRecord4Query query);

    void deleteByUserIds(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds,
        @Param("operatorId") String operatorId);

    void deleteRecordByCaliMeetId(@Param("orgId")String orgId,@Param("caliMeetId") String caliMeetId);
}