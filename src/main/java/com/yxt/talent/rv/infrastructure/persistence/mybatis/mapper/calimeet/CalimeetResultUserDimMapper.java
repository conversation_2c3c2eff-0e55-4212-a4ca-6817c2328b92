package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CalimeetResultUserDimMapper extends CommonMapper<CalimeetResultUserDimPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetResultUserDimPO record);

    int insertOrUpdate(CalimeetResultUserDimPO record);

    int insertOrUpdateSelective(CalimeetResultUserDimPO record);

    CalimeetResultUserDimPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetResultUserDimPO record);

    int updateBatch(@Param("list") List<CalimeetResultUserDimPO> list);

    int batchInsert(@Param("list") List<CalimeetResultUserDimPO> list);

    int batchInsertOrUpdate(@Param("list") List<CalimeetResultUserDimPO> list);

    void deleteUserDimResults(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds);

    List<CalimeetResultUserDimPO> getByUserIdDimIds(
        @Param("orgId") String orgId,
        @Param("caliMeetId") String caliMeetId, @Param("userIds") Collection<String> userIds,
        @Param("dimIds") Collection<String> dimIds);

    List<CalimeetResultUserDimPO> selectByCaliMeetIdAndUserIds(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userId);

    List<CalimeetResultUserDimPO> listByOrgIdAndCalimeetId(
        @Param("orgId") String orgId, @Param("calimeetId") String calimeetId);


}