package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CalimeetResultUserDimcombMapper extends CommonMapper<CalimeetResultUserDimcombPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetResultUserDimcombPO record);

    int insertOrUpdate(CalimeetResultUserDimcombPO record);

    int insertOrUpdateSelective(CalimeetResultUserDimcombPO record);

    CalimeetResultUserDimcombPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetResultUserDimcombPO record);

    int updateBatch(@Param("list") List<CalimeetResultUserDimcombPO> list);

    int batchInsert(@Param("list") List<CalimeetResultUserDimcombPO> list);

    int batchInsertOrUpdate(@Param("list") List<CalimeetResultUserDimcombPO> list);

    void deleteUserDimcombResults(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds);
}