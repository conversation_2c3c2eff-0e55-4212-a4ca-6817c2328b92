package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CalimeetResultUserIndicatorMapper extends CommonMapper<CalimeetResultUserIndicatorPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetResultUserIndicatorPO record);

    int insertOrUpdate(CalimeetResultUserIndicatorPO record);

    int insertOrUpdateSelective(CalimeetResultUserIndicatorPO record);

    CalimeetResultUserIndicatorPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetResultUserIndicatorPO record);

    int updateBatch(@Param("list") List<CalimeetResultUserIndicatorPO> list);

    int batchInsert(@Param("list") List<CalimeetResultUserIndicatorPO> list);

    int batchInsertOrUpdate(@Param("list") List<CalimeetResultUserIndicatorPO> list);

    void deleteUserIndicatorResults(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds);

    List<CalimeetResultUserIndicatorPO> getByUserIdIndicatorIds(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
        @Param("userIds") Collection<String> userIds, @Param("indicatorIds") Collection<String> indicatorIds);

    List<CalimeetResultUserIndicatorPO> selectByCaliMeetIdAndUserIds(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userId);

    List<CalimeetResultUserIndicatorPO> selectByOrgIdAndCalimeetId(
        @Param("orgId") String orgId, @Param("calimeetId") String calimeetId);
}