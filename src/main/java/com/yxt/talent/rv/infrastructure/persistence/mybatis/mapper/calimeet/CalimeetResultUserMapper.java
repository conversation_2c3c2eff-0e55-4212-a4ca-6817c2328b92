package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CalimeetResultUserMapper extends CommonMapper<CalimeetResultUserPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetResultUserPO record);

    int insertOrUpdate(CalimeetResultUserPO record);

    int insertOrUpdateSelective(CalimeetResultUserPO record);

    CalimeetResultUserPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetResultUserPO record);

    int updateBatch(@Param("list") List<CalimeetResultUserPO> list);

    int batchInsert(@Param("list") List<CalimeetResultUserPO> list);

    int batchInsertOrUpdate(@Param("list") List<CalimeetResultUserPO> list);

    void deleteUserResults(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds);

    @Select("""
    select xpd_level_id from rv_calimeet_result_user where org_id = #{orgId}
    and calimeet_id = #{caliMeetId} and user_id = #{userId} and deleted = 0 limit 1
    """)
    String userLevelId(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userId") String userId);
}