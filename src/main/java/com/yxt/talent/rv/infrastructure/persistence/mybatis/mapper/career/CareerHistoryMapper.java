package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.career;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.CareerHistoryPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CareerHistoryMapper extends CommonMapper<CareerHistoryPO> {

    int batchInsertOrUpdate(@Param("list") Collection<CareerHistoryPO> list);

    default void insertOrUpdateBatch(@Param("list") Collection<CareerHistoryPO> list) {
        batchExecute(list, this::batchInsertOrUpdate);
    }

    Collection<String> selectByThirdUserIds(
            @Param("orgId") String orgId, @Param("thirdUserIds") List<String> thirdUserIds);

    List<CareerHistoryPO> selectByThirdUserId(
        @Param("orgId") String orgId, @Param("thirdUserId") String thirdUserId);

    int deleteByThirdUserIdAndThirdCareerHistoryIds(
        @Param("orgId") String orgId,
        @Param("thirdCareerHistoryIds") List<String> thirdCareerHistoryIds);

    List<CareerHistoryPO> selectByUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    int deleteByUserId(@Param("orgId") String orgId, @Param("userIds") List<String> userIds, String operator);

    List<CareerHistoryPO> selectByOrgId(@Param("orgId") String orgId);
}