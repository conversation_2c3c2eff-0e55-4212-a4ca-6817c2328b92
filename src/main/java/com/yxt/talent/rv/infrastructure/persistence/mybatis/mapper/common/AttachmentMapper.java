package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Mapper
@Repository
public interface AttachmentMapper extends CommonMapper<AttachmentPO> {

    void deleteByAppSourceId(
            @Nonnull @Param("orgId") String orgId, @Param("appSourceId") String appSourceId);

    int batchInsertOrUpdate(@Nonnull @Param("list") List<AttachmentPO> list);

    @Nonnull
    List<AttachmentPO> listByAppSourceId(
            @Nonnull @Param("orgId") String orgId, @Param("appSourceId") String appSourceId);

    @Nullable
    AttachmentPO selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    int deleteById(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    long insertOrUpdate(AttachmentPO attachmentPO);

    default void insertOrUpdateBatch(Collection<AttachmentPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    List<AttachmentPO> listByOrgId(@Nonnull @Param("orgId") String orgId);
}
