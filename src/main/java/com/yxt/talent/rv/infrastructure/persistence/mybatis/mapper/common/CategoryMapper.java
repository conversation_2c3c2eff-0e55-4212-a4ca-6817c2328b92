package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface CategoryMapper {

    @Nullable
    CategoryPO selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    int updateById(@Nonnull @Param("updated") CategoryPO updated);

    void deleteById(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    void insertOrUpdate(CategoryPO category);
}
