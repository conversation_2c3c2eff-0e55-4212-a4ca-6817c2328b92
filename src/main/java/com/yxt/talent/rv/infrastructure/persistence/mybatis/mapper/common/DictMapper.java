package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DictMapper {

    int deleteById(String id);

    long insertOrUpdate(@Nonnull DictPO entity);

    @Nullable
    DictPO selectByType(
            @Nonnull @Param("type") Integer type, @Nonnull @Param("value") Integer value);

    @Nonnull
    List<DictPO> listByType(@Nonnull @Param("type") Integer type);

    @Nullable
    DictPO selectById(@Param("id") String id);
}