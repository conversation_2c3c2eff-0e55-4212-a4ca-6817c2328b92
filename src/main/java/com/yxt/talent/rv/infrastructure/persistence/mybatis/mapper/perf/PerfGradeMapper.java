package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface PerfGradeMapper extends CommonMapper<PerfGradePO> {

    default void insertOrUpdate(PerfGradePO perfGradePO) {
        batchInsertOrUpdate(List.of(perfGradePO));
    }

    default void insertOrUpdateBatch(Collection<PerfGradePO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    long batchInsertOrUpdate(@Param("list") Collection<PerfGradePO> list);

    int currentMaxSort(@Nonnull @Param("orgId") String orgId);

    int selectMaxOrderIndex(@Nonnull @Param("orgId") String orgId);

    List<PerfGradePO> selectByOrgIdIncludeDeleted(@Param("orgId") String orgId);

    long countByOrgId(@Param("orgId") String orgId);

    Collection<PerfGradePO> selectByOrgId(@Param("orgId") String orgId);

    Collection<PerfGradePO> selectByOrgIdAll(@Param("orgId") String orgId);

    Collection<PerfGradePO> selectByOrgIdInState(@Param("orgId") String orgId);

    long countByGradeName(@Param("orgId") String orgId, @Param("gradeName") String gradeName, @Param("id") String id);

    PerfGradePO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    List<PerfGradePO> selectByOrgIdAndOrderIndexBetween(@Param("orgId") String orgId,
            @Param("minOrderIndex") int minOrderIndex, @Param("maxOrderIndex") int maxOrderIndex);

    void updateStateById(@Param("orgId") String orgId, @Param("id") String id, @Param("state") Integer state,
            @Param("userId") String userId);

    List<String> findGradeOrgId();

    int findGradeValueNum(@Param("orgId") String orgId, @Param("gradeValue") int gradeValue);

    void deleteByOrgId(String orgId);

    List<PerfGradePO> findGradeByOrgIdAndLevel(@Param("orgId") String orgId,
            @Param("gradeValues") List<Integer> perfLevels);
}