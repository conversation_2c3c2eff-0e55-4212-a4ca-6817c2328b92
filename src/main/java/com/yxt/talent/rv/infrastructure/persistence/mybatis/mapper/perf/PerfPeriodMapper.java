package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface PerfPeriodMapper extends CommonMapper<PerfPeriodPO> {

    int insert(PerfPeriodPO record);

    int insertOrUpdate(PerfPeriodPO record);

    PerfPeriodPO selectByPrimaryKey(String id);

    long batchInsertOrUpdate(@Param("list") List<PerfPeriodPO> list);

    default void insertOrUpdateBatch(Collection<PerfPeriodPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    int currentMaxSort(@Nonnull @Param("orgId") String orgId);

    @Nullable
    PerfPeriodPO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    List<PerfPeriodPO> selectByOrgId(@Param("orgId") String orgId);

    long countByOrgId(@Param("orgId") String orgId);

    long selectByOrgIdAndPeriodNameAndIdNeIfHas(
        @Param("orgId") String orgId, @Param("periodName") String periodName,
        @Param("id") String id);

    List<PerfPeriodPO> selectByOrgIdAndOrderIndexBetween(
        @Param("orgId") String orgId, @Param("start") int start, @Param("end") int end);

    List<PerfPeriodPO> selectByOrgIdAndIdsIncludeDeleted(
        @Param("orgId") String orgId, @Param("ids") List<String> ids);

    List<PerfPeriodPO> selectByOrgIdAndIds(
        @Param("orgId") String orgId, @Param("ids") List<String> ids);

    List<PerfPeriodPO> selectByOrgIdAndNames(
        @Param("orgId") String orgId, @Param("names") List<String> names);

    List<PerfPeriodPO> selectByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);
}