package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.reward;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.RewardPunishmentHistoryPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface RewardPunishmentHistoryMapper extends CommonMapper<RewardPunishmentHistoryPO> {

    int batchInsertOrUpdate(@Param("list") Collection<RewardPunishmentHistoryPO> list);

    default void insertOrUpdateBatch(Collection<RewardPunishmentHistoryPO> list) {
        batchExecute(list, this::batchInsertOrUpdate);
    }

    Collection<String> selectByThirdUserIds(
            @Param("orgId") String orgId, @Param("thirdUserIds") List<String> thirdUserIds);

    List<RewardPunishmentHistoryPO> selectByOrgIdAndThirdUserId(
        @Param("orgId") String orgId, @Param("thirdUserId") String thirdUserId);

    List<RewardPunishmentHistoryPO> selectByOrgIdAndUserId(
        @Param("orgId") String orgId, @Param("userId") String userId);

    int deleteByIds(@Param("orgId") String orgId,  @Param("ids") List<String> ids);

    void deleteByUserId(@Param("orgId") String orgId, @Param("userIds") List<String> userIds, String operator);

    List<RewardPunishmentHistoryPO> selectByOrgId(@Param("orgId") String orgId);
}