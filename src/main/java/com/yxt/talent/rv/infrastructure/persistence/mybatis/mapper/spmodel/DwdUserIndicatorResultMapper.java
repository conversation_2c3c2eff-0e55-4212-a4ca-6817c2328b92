package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.spmodel;


import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface DwdUserIndicatorResultMapper {
    List<XpdIndicatorResultDto> listUserIndicator(
        @Param("orgId") String orgId,
        @Param("thirdUserIds") Collection<String> thirdUserIds,
        @Param("indicatorIds") Collection<String> indicatorIds);
}
