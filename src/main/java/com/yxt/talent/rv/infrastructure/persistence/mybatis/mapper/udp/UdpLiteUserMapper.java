package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.user.dto.UdpUserSimpleDTO;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjUserResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamPrjUserResultClientVO;
import com.yxt.talent.rv.controller.client.general.xpd.viewobj.XpdClientVO;
import com.yxt.talent.rv.controller.manage.udp.query.PosCreateCmd;
import com.yxt.talent.rv.controller.manage.udp.query.UdpUserFrontQuery;
import com.yxt.talent.rv.controller.manage.udp.viewobj.UdpUserFrontVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UdpLiteUserMapper {

    @Nullable
    UdpLiteUserPO selectByUserId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId);

    UdpLiteUserPO selectByUserIdWithDeleted(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId);

    /**
     * 根据用户账号数组查询用户信息
     *
     * @param orgId
     * @param usernames
     */
    @Nonnull
    List<UdpLiteUserPO> selectByUserNames(
            @Nonnull @Param("orgId") String orgId,
            @Param("usernames") Collection<String> usernames);

    /**
     * 根据用户账号数组查询用户信息
     *
     * @param orgId
     * @param usernames
     */
    @Nonnull
    List<UdpLiteUserPO> selectByUserNamesIncludeDeleted(
        @Nonnull @Param("orgId") String orgId,
        @Param("usernames") Collection<String> usernames);

    /**
     * 根据用户ID岗位查询
     *
     * @param orgId
     * @param addUserIdList
     */
    @Nonnull
    List<UdpLiteUserPO> selectByPosIdAndUserIds(
            @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("list") List<String> addUserIdList);

    @Nonnull
    List<UdpLiteUserPO> selectByUserIds(
            @Nonnull @Param("orgId") String orgId,
            @Param("userIds") Collection<String> userIds);

    @Nonnull
    List<UdpLiteUserPO> selectByUserIdsIncludeDelete(
            @Nonnull @Param("orgId") String orgId,
            @Param("userIds") List<String> userIds);

    long countByPosIdAndGradeId(
            @Nonnull @Param("orgId") String orgId,
            @Param("positionId") String positionId,
            @Param("gradeIdList") List<String> gradeIdList);

    @Nonnull
    List<UdpLiteUserPO> selectByThirdUserIds(
            @Nonnull @Param("orgId") String orgId,
            @Param("thirdUserIds") Collection<String> thirdUserIds);

    @Nonnull
    List<UdpLiteUserPO> selectByThirdUserIdsAll(
        @Nonnull @Param("orgId") String orgId,
        @Param("thirdUserIds") Collection<String> thirdUserIds);

    @Nonnull
    List<UdpUserSimpleDTO> listByOrgId(@Nonnull @Param("orgId") String orgId);

    /**
     * 用户端盘点结果列表【人员维度】【专用查询】
     *
     * @param page
     * @param orgId
     * @param currentUserId
     * @param search
     */
    @Nonnull
    IPage<TeamPrjUserResultClientVO> selectTeamPrjUserResults(
            @Nonnull IPage<TeamPrjUserResultClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("currentUserId") String currentUserId,
            @Nonnull @Param("search") TeamPrjUserResultScopeAuthClientQuery search,
            @Param("qwUserIds") List<String> qwUserIds);

    /**
     * [新盘点]我的团队-人才盘点-个人维度
     *
     * @param page
     * @param orgId
     * @param currentUserId
     * @param search
     */
    @Nonnull
    IPage<TeamPrjUserResultClientVO> selectTeamXpdUserResults(
            @Nonnull IPage<TeamPrjUserResultClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("currentUserId") String currentUserId,
            @Nonnull @Param("search") TeamPrjUserResultScopeAuthClientQuery search,
            @Param("qwUserIds") List<String> qwUserIds);

    /**
     * [新盘点]我的团队-人才盘点-项目维度
     *
     * @param pageable
     * @param orgId
     * @param prjStatusList
     * @param search
     */
    @Nonnull
    IPage<XpdClientVO> selectPrjClientPage(
        Page<XpdClientVO> pageable,
        @Param("orgId") String orgId,
        @Param("prjStatusList") List<Integer> prjStatusList,
        @Param("search") TeamPrjResultScopeAuthClientQuery search);

    @Nullable
    UdpLiteUserPO selectUserSimple(@Nonnull @Param("id") String id);

    /**
     * 用户分页查询
     *
     * @param page
     * @param searchParam
     * @param orgId
     */
    @Nonnull
    IPage<UdpUserFrontVO> pageQuery(
            Page<UdpUserFrontVO> page,
            @Param("searchParam") UdpUserFrontQuery searchParam,
            @Nonnull @Param("orgId") String orgId,
            @Param("posInfos") List<PosCreateCmd> posInfos);

    int countUserByPosIdsAndGradeIds(
            @Nonnull @Param("orgId") String orgId,
            @Param("positionIds") List<String> positionIds,
            @Param("gradeIdList") List<String> gradeIdList);

    @Nullable
    UdpLiteUserPO selectByThirdUserId(
            @Nonnull @Param("orgId") String orgId,
            @Param("thirdUserId") String thirdUserId);

    @Nonnull
    List<UdpLiteUserPO> selectActiveUsers(
            @Nonnull @Param("orgId") String orgId,
            @Param("ids") Collection<String> ids);

    @Nonnull
    List<UdpLiteUserPO> selectActiveUsersByUserNames(
            @Nonnull @Param("orgId") String orgId,
            @Param("userNames") Collection<String> userNames);

    long countByOrgId(@Nonnull @Param("orgId") String orgId);

    UdpLiteUserPO selectById(@Param("id") String id);
}
