package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpPosCatPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UdpPosCatMapper {

    @Nullable
    UdpPosCatPO selectByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

}
