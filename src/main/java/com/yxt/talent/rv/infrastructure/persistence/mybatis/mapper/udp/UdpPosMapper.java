package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpPosPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UdpPosMapper {


    @Nullable
    UdpPosPO selectByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    List<UdpPosPO> selectByOrgIdAndIds(@Param("orgId") String orgId,  @Param("ids") List<String> id);
}
