package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserExtPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface UserExtMapper extends CommonMapper<UserExtPO> {

    int batchInsertOrUpdate(@Param("list") Collection<UserExtPO> list);

    default void insertOrUpdateBatch(Collection<UserExtPO> list) {
        batchExecute(list, this::batchInsertOrUpdate);
    }

    Collection<String> selectByThirdUserIds(
            @Param("orgId") String orgId, @Param("thirdUserIds") List<String> thirdUserIds);

    List<UserExtPO> selectByThirdUserId(@Param("orgId") String orgId, @Param("thirdUserId") String thirdUserId);

    List<UserExtPO> selectByOrgIdAndUserId(@Param("orgId") String orgId, @Param("userId") String userId);

    List<UserExtPO> selectByAndUserIds(@Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    List<UserExtPO> selectByOrgId(@Param("orgId") String orgId);
}