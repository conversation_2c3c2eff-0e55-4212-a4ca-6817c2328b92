package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.bizmgr;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface UserFocusMapper {

    int insert(@Nonnull UserFocusPO entity);

    long insertOrUpdate(@Nonnull UserFocusPO entity);

    int updateById(@Nonnull UserFocusPO entity);

    @Nullable
    UserFocusPO selectByOrgIdAndUserIdAndTargetId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
            @Param("targetId") String targetId);

    @Nonnull
    List<UserFocusPO> selectByOrgIdAndUserIdAndTargetIds(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
            @Param("prjIds") List<String> prjIds);

    @Nonnull
    List<UserFocusPO> selectByOrgIdAndUserId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId);

    Optional<UserFocusPO> selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);
}
