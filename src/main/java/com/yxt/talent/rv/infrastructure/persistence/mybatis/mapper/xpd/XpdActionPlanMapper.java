package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XpdActionPlanMapper extends CommonMapper<XpdActionPlanPO> {

    // 根据组织ID查询行动计划列表
    List<XpdActionPlanPO> selectByOrgId(@Param("orgId") String orgId);

    int insert(XpdActionPlanPO record);

    XpdActionPlanPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdActionPlanPO record);

    int updateBatch(@Param("list") List<XpdActionPlanPO> list);

    int batchInsert(@Param("list") List<XpdActionPlanPO> list);

    int insertOrUpdate(XpdActionPlanPO record);

    List<XpdActionPlanPO> selectByOrgIdAndXpdIdAndType(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("xpdId") String xpdId, @Nonnull @Param("targetType") Integer targetType);

    IPage<XpdActionPlanPO> pagingByOrgIdAndXpdIdAndType(
            Page<XpdActionPlanPO> page, @Nonnull @Param("orgId") String orgId, @Nonnull @Param("xpdId") String xpdId,
            @Nonnull @Param("targetType") Integer targetType);

    int countByOrgIdAndXpdIdAndType(
        @Param("orgId") String orgId, @Param("trainingId") String trainingId, @Param("targetType") Integer targetType);

    List<XpdActionPlanPO> selectByTargetIdsAndType(
        @Param("orgId") String orgId, @Param("targetIds") List<String> targetIds,
        @Param("targetType") Integer targetType);
}