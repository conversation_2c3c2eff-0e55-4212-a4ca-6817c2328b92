package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.controller.client.general.xpd.viewobj.XpdClientVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface XpdActivityMemberStatisticsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ActivityMemberStatistics record);

    int insertOrUpdate(ActivityMemberStatistics record);

    int insertOrUpdateSelective(ActivityMemberStatistics record);

    int insertSelective(ActivityMemberStatistics record);

    ActivityMemberStatistics selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivityMemberStatistics record);

    int updateByPrimaryKey(ActivityMemberStatistics record);

    int updateBatch(@Param("list") List<ActivityMemberStatistics> list);

    int batchInsert(@Param("list") List<ActivityMemberStatistics> list);

    ActivityMemberStatistics selectByActvIdAndUserId(
            @Param("orgId") String orgId, @Param("actvId") String actvId, @Param("userId") String userId);

    List<String> getInvolvedDeptIdFullPaths(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdClientVO> avgProgressByActvIds(@Param("orgId") String orgId, @Param("actvIds") List<String> actvIds);

    List<ActivityMemberStatistics> listCompRate(@Param("orgId") String orgId, @Param("actvIds") List<String> actvIds, @Param("userId") String userId);
}