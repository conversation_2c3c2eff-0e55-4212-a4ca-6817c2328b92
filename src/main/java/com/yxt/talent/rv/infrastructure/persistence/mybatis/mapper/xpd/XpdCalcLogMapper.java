package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdCalcLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Author: geyan
 * @Date: 10/12/24 10:13
 * @Description:
 **/
@Mapper
public interface XpdCalcLogMapper extends BaseMapper<XpdCalcLogPO> {

    @Select("select batch_no from rv_xpd_calc_log where calc_type = #{calcType} and ref_id = #{refId} order by batch_no desc limit 1")
    Integer lastBatchNo(@Param("calcType") int calcType, @Param("refId") String refId);

    @Select("select batch_no,start_time,end_time,calc_status from rv_xpd_calc_log where org_id = #{orgId} and calc_type = #{calcType} and ref_id = #{refId} order by batch_no desc limit 1")
    XpdCalcLogPO queryRefIdLatestCal(@Param("orgId")String orgId, @Param("calcType") int calcType, @Param("refId") String refId);
}
