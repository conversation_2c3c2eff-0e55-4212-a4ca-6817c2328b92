package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.xpd.common.dto.DimCombBriefDto;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombListVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface XpdDimCombMapper extends CommonMapper<XpdDimCombPO> {
    int insert(XpdDimCombPO record);

    int insertOrUpdate(XpdDimCombPO record);

    XpdDimCombPO selectByPrimaryKey(String id);

    /**
     * 维度组是引入关系，所有xpd共享一份维度组
     *
     * @param orgId
     * @return
     */
    List<XpdDimCombPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    @Select("""
    select dc.id,dc.comb_name,dc.x_sd_dim_id,dc.y_sd_dim_id from rv_xpd_grid g
    join rv_xpd_grid_dim_comb gdc on gdc.org_id = g.org_id and gdc.grid_id = g.id and gdc.deleted = 0
    join rv_xpd_dim_comb dc on dc.id = gdc.dim_comb_id and dc.deleted = 0
    where g.org_id = #{orgId} and g.xpd_id = #{xpdId} and g.deleted = 0
    order by gdc.order_index
    """)
    List<DimCombBriefDto> listBriefByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    IPage<XpdDimCombPO> listByXpdIdPage(
        IPage<XpdDimCombPO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("dimIds") List<String> dimIds,
        @Param("keyword") String keyword);

    /**
     * 根据宫格获取维度组
     *
     * @param orgId
     * @param gridId
     * @return
     */
    List<XpdDimCombPO> listByXpdIdAndGridId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("gridId") String gridId);

    int batchInsert(@Param("list") List<XpdDimCombPO> list);

    int countByName(String orgId, String combName, String xpdId);

    @Nonnull
    IPage<XpdDimCombListVO> pagingByOrgId(
        Page<XpdDimCombListVO> page, @Nonnull @Param("orgId") String orgId,
        @Nonnull @Param("xDimId") String xDimId, @Nonnull @Param("yDimId") String yDimId,
        @Nonnull @Param("combName") String combName, @Param("scopeSdDimIds") Collection<String> scopeSdDimIds);


    List<XpdDimCombPO> selectByIds(@Param("ids") Collection<String> ids);

    List<XpdDimCombPO> selectByOrgId(@Param("orgId") String orgId);

    List<XpdDimCombPO> listInner(@Param("orgId") String orgId);

    List<XpdDimCombPO> selectByDimIds(@Param("orgId") String orgId, @Param("sdDimIds") Collection<String> sdDimIds);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

    void deleteByOrgId(@Param("orgId") String orgId);
}