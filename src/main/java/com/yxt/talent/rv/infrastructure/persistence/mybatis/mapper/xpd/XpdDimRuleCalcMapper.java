package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdDimRuleCalcMapper extends CommonMapper<XpdDimRuleCalcPO> {

    List<XpdDimRuleCalcPO> selectByOrgId(@Param("orgId") String orgId);
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(XpdDimRuleCalcPO record);

    int insertOrUpdate(XpdDimRuleCalcPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    XpdDimRuleCalcPO selectByPrimaryKey(String id);

    List<XpdDimRuleCalcPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    void deleteBySdDimIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                          @Param("sdDimIds") Collection<String> sdDimIds, @Param("userId") String userId);

    void deleteByDimRuleId(@Param("orgId") String orgId, @Param("dimRuleId") String dimRuleId, @Param("userId") String userId);

    void deleteByDimRuleIds(@Param("orgId") String orgId, @Param("userId") String userId, @Param("dimRuleIds") Collection<String> dimRuleId);

    List<XpdDimRuleCalcPO> listBySdDimId(@Param("orgId") String orgId, @Param("sdDimId") String sdDimId);

    List<XpdDimRuleCalcPO> listByDimRuleId(@Param("orgId") String orgId, @Param("dimRuleId") String dimRuleId);

    void batchInsert(List<XpdDimRuleCalcPO> newDimRuleCalcList);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

    List<XpdDimRuleCalcPO> selectByOrgIdAndXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 逻辑删
     *
     * @param userId 操作人ID
     * @param ids    ids
     */
    void deleteByIds(@Param("userId") String userId, @Param("ids") Collection<String> ids);

    List<XpdDimRuleCalcPO> listByXpdIdAndSdDimId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimId") String sdDimId);
}