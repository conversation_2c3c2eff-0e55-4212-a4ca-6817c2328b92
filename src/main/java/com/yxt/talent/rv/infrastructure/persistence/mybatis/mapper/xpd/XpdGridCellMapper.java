package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridCellPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdGridCellMapper extends CommonMapper<XpdGridCellPO> {

    List<XpdGridCellPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(XpdGridCellPO record);

    int insertOrUpdate(XpdGridCellPO record);

    XpdGridCellPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdGridCellPO record);

    int updateBatch(@Param("list") List<XpdGridCellPO> list);

    int batchInsert(@Param("list") List<XpdGridCellPO> list);

    List<XpdGridCellPO> listByGridIds(@Param("orgId") String orgId, @Param("gridIds") Collection<String> gridIds);

    List<XpdGridCellPO> selectByXpdIdAndGridIdAndDimCombId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("gridId") String gridId, @Param("dimCombId") String dimCombId);

    /**
     * 查询宫格内的格子
     *
     * @param orgId  机构ID
     * @param gridId 宫格ID
     * @return list
     * <AUTHOR>
     */
    List<XpdGridCellPO> listByGridId(@Param("orgId") String orgId, @Param("gridId") String gridId);

    int insertBatch(@Param("coll") Collection<XpdGridCellPO> coll);

    void deleteGrid(
        @Param("orgId") String orgId, @Param("gridId") String gridId,
        @Param("userId") String userId);

    List<XpdGridCellPO> listByGridIdAndDimCombId(
        @Param("orgId") String orgId, @Param("gridId") String gridId,
        @Param("dimCombId") String dimCombId);

    List<XpdGridCellPO> listByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

    IPage<XpdGridCellPO> listPageByXpdId(
        @Param("page") IPage<XpdGridCellPO> page,
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("dimCombId") String dimCombId);

    XpdGridCellPO selectByDimCombIdAndIndex(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("dimCombId") String dimCombId,
        @Param("xIndex") Integer xIndex,
        @Param("yIndex") Integer yIndex);

    void deleteByGridId(@Param("orgId") String orgId,
        @Param("gridId") String gridId, @Param("userId") String userId);

    void deleteByGridIdAndDimCombIds(@Param("orgId") String orgId,
        @Param("gridId") String gridId, @Param("dimCombIds") List<String> dimCombIds, @Param("userId") String userId);

    void deleteByOrgId(@Param("orgId") String orgId);
}