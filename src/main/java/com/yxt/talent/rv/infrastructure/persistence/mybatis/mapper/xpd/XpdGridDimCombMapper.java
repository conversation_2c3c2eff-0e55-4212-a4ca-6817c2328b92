package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;
import com.yxt.talent.rv.controller.manage.xpd.grid.dto.XpdGridDimCombDTO;
import org.apache.ibatis.annotations.Param;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridDimCombPO;

import java.util.List;

import java.util.Collection;

public interface XpdGridDimCombMapper extends CommonMapper<XpdGridDimCombPO> {

    List<XpdGridDimCombPO> selectByOrgId(@Param("orgId") String orgId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(XpdGridDimCombPO record);

    int insertOrUpdate(XpdGridDimCombPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    XpdGridDimCombPO selectByPrimaryKey(String id);

    List<XpdGridDimCombPO> listByGridIds(@Param("orgId") String orgId, @Param("gridIds") Collection<String> gridIds);

    void deleteByGridId(@Param("orgId") String orgId, @Param("gridId") String gridId, @Param("userId") String userId);
    int insertBatch(
        @Param("xpdGridDimCombPOCollection") Collection<XpdGridDimCombPO> xpdGridDimCombPOCollection);

    List<XpdGridDimCombPO> listByXpdIdAndGridId(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                                                @Param("gridId") String gridId);

    List<XpdGridDimCombPO> findTempByGridId(@Param("orgId") String orgId,
        @Param("gridId") String gridId);

    List<XpdGridDimCombPO> batchInsertOrUpdate(@Param("list") Collection<XpdGridDimCombPO> list);

    void deleteByIds(@Param("ids") Collection<String> ids, @Param("userId") String userId);

    List<XpdGridDimCombDTO> findGridCombList(@Param("orgId") String orgId, @Param("gridId") String gridId);

    void batchInsert(List<XpdGridDimCombPO> newGridDimCombList);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

    int deleteByGridIdAndDimCombId(@Param("orgId") String orgId, @Param("gridId") String gridId, @Param("dimCombIds") List<String> dimCombId,
        String userId);

    int countByDimCombId(@Param("orgId") String orgId, @Param("dimCombId") String dimCombId);

    void batchUpdate(List<XpdGridDimCombPO> list);

    void deleteByOrgId(@Param("orgId") String orgId);
}