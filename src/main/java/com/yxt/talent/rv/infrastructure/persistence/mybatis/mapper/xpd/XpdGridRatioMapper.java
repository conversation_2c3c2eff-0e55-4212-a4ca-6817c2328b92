package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridRatioPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdGridRatioMapper extends CommonMapper<XpdGridRatioPO> {

    List<XpdGridRatioPO> selectByOrgId(@Param("orgId")String orgId);


    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(XpdGridRatioPO record);

    int insertOrUpdate(XpdGridRatioPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    XpdGridRatioPO selectByPrimaryKey(String id);

    List<XpdGridRatioPO> listByGridIds(@Param("orgId") String orgId, @Param("gridIds") Collection<String> gridIds);

    List<XpdGridRatioPO> selectByXpdIdAndGridIdAndDimCombId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("gridId") String gridId,
        @Param("dimCombId") String dimCombId);

    List<XpdGridRatioPO> listByGridId(@Param("orgId") String orgId, @Param("gridId") String gridId);

    void deleteGridRatio(@Param("orgId") String orgId, @Param("gridId") String gridId, @Param("userId") String userId);

    int batchInsert(
        @Param("xpdGridRatioPOCollection") Collection<XpdGridRatioPO> xpdGridRatioPOCollection);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

    void deleteByOrgId(@Param("orgId") String orgId);
}