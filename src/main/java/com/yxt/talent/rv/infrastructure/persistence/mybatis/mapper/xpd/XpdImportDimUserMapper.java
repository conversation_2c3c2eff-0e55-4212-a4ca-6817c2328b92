package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportDimNumDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportGridLevelDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.XpdImportUserDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportDimUserPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdImportDimUserMapper extends CommonMapper<XpdImportDimUserPO> {

    List<XpdImportDimUserPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(XpdImportDimUserPO record);

    int insertOrUpdate(XpdImportDimUserPO record);

    int insertOrUpdateSelective(XpdImportDimUserPO record);

    XpdImportDimUserPO selectByPrimaryKey(String id);

    List<XpdImportDimUserPO> selectByXpdIdAndUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userId") String userId);

    List<XpdImportDimUserPO> getBySdDimIdAndUserIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId,
        @Param("userIds") List<String> userIds);

    List<XpdImportUserDTO> selectUserCount(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("importIds") List<String> importIds);

    List<XpdImportDimUserPO> getByUserIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId,
        @Param("importId") String importId,
        @Param("userIds") Collection<String> userIds);

    int insertBatch(@Param("xpdImportDimUserPOCollection") Collection<XpdImportDimUserPO> xpdImportDimUserPOCollection);


    int batchUpdate(@Param("list") Collection<XpdImportDimUserPO> list);

    @Nonnull
    List<ImportGridLevelDTO> findUserGridLevel(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId,
        @Param("importId") String importId,
        @Param("userIds") Collection<String> userIds);

    int deleteByImportIdAndUserId(
        @Param("orgId") String orgId,
        @Param("importId") String importId,
        @Param("dimId") String dimId,
        @Param("userId") String userId,
        @Param("operator") String operator);

    int deleteByXpdId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("operator") String operator);

    int deleteByXpdIdAndDimIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("dimIds") Collection<String> dimIds,
        @Param("operator") String operator);

    List<ImportDimNumDTO> findDimNum(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("dimIds") Collection<String> dimIds,
        @Param("userId") String userId);

}