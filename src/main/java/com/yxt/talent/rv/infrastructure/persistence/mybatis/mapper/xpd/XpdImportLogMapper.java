package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportLogPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdImportLogMapper extends CommonMapper<XpdImportLogPO> {

    List<XpdImportLogPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(XpdImportLogPO record);

    int insertOrUpdate(XpdImportLogPO record);

    int insertOrUpdateSelective(XpdImportLogPO record);

    XpdImportLogPO selectByPrimaryKey(String id);

    List<XpdImportLogPO> selectByImportId(@Param("orgId") String orgId, @Param("importIds") List<String> importIds);

    void deleteByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("operator") String operator);

    void deleteByXpdIdAndImportIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("operator") String operator,
        @Param("importIds") Collection<String> importIds);
}