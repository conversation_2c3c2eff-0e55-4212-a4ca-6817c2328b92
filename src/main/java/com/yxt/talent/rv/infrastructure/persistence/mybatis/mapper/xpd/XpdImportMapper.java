package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface XpdImportMapper extends CommonMapper<XpdImportPO> {

    List<XpdImportPO> selectByOrgId(@Param("orgId") String orgId);

    int insert(XpdImportPO record);

    int insertOrUpdate(XpdImportPO record);

    XpdImportPO selectByPrimaryKey(String id);

    List<XpdImportPO> selectByXpdIdAndOrgId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdImportPO> selectByXpdId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("importType") Integer importType);
    IPage<XpdImportPO> selectPage(@Param("page") IPage<XpdImportPO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    int findCountBySdDimId(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimId") String sdDimId);

    List<XpdImportPO> listBySdDimIdsAndImportType(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                                                  @Param("sdDimIds") Collection<String> sdDimIds, @Param("importType") Integer importType);

    @Select("""
    select id from rv_xpd_import where org_id = #{orgId} and xpd_id = #{xpdId} and import_type = #{importType} and deleted = 0
    """)
    List<String> listXpdImportIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("importType") Integer importType);

    List<String> selectDimIdByXpdIdAndOrgIdAndImportType(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("importType") int importType);

    /**
     * 查询导入的维度
     *
     * @param orgId
     * @param xpdId
     * @param importType 导入的类型，导入维度指标明细 or 导入维度分层结果
     * @param mustXpdDim 是否必须为盘点的维度
     * @return
     */
    List<XpdImportPO> findByXpdIdAndImportType(@Param("orgId") String orgId,
                                               @Param("xpdId") String xpdId,
                                               @Param("importType") Integer importType,
                                               @Param("mustXpdDim") boolean mustXpdDim);

    @Nonnull
    List<XpdImportPO> selectByIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);

    int deleteByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("operator") String operator);

    int deleteByXpdIdAndSdDimId(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("dimIds") Collection<String> dimIds
        , @Param("operator") String operator);
}