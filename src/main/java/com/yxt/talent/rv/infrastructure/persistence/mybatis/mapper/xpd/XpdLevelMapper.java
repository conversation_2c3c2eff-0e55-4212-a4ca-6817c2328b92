package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdLevelMapper extends CommonMapper<XpdLevelPO> {

    int deleteByPrimaryKey(String id);

    int batchInsert(@Param("list") List<XpdLevelPO> list);

    int insert(XpdLevelPO record);

    int insertOrUpdate(XpdLevelPO record);

    XpdLevelPO selectByPrimaryKey(String id);

    List<XpdLevelPO> listByGridIds(@Param("orgId") String orgId, @Param("gridIds") Collection<String> gridIds);

    List<XpdLevelPO> queryByRuleId(
            @Param("orgId") String orgId,
            @Param("xpdId") String xpdId,
            @Param("xpdRuleId") String xpdRuleId);

    List<XpdLevelPO> selectByOrgId(@Param("orgId") String orgId);

    int countByName(String orgId, String levelName, String xpdId, String gridId, @Nullable String id);

    int queryMaxOrderIndexByOrgId(String orgId, String xpdId, String gridId);

    List<XpdLevelPO> queryAllByOrgId(String orgId, String xpdId, String gridId);

    List<XpdLevelPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdLevelPO> findByActId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdLevelPO> listByXpdRuleId(@Param("orgId") String orgId, @Param("xpdRuleId") String xpdRuleId);

    List<XpdLevelPO> listByXpdRuleIdReverse(@Param("orgId") String orgId, @Param("xpdRuleId") String xpdRuleId);

    /**
     * 批量逻辑删
     *
     * @param orgId     机构ID
     * @param xpdRuleId 项目规则ID
     * @param userId    操作人ID
     */
    void deleteByXpdRuleId(@Param("orgId") String orgId,
                           @Param("xpdRuleId") String xpdRuleId,
                           @Param("userId") String userId);

    void deleteByXpdId(@Param("orgId") String orgId,
                       @Param("userId") String userId,
                       @Param("xpdId") String xpdId);

    /**
     * 根据宫格ID获取分层
     *
     * @param orgId  机构ID
     * @param gridId 宫格ID
     * @return list
     */
    List<XpdLevelPO> listByGridId(@Param("orgId") String orgId, @Param("gridId") String gridId);

    /**
     * 根据项目获取人才分层
     * @param orgId
     * @param xpdId
     * @return
     */
    List<XpdLevelPO> selectByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    void batchUpdateFormulaDisplay(@Param("list")List<XpdLevelPO> list);

    /**
     * 根据项目获取人才分层(分页）
     * @param page
     * @param orgId
     * @param xpdId
     * @return
     */
    IPage<XpdLevelPO> selectByXpdId(IPage<XpdLevelPO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdLevelPO> listByIds(@Param("ids") Collection<String> ids);

    void deleteByOrgId(@Param("orgId") String orgId);
}