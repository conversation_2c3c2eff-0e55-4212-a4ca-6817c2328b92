package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.talent.rv.application.xpd.common.dto.XpdInfoDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdUserCountDto;
import com.yxt.talent.rv.application.xpd.xpd.dto.XpdCompletedUserDTO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptXpdClientVO;
import com.yxt.talent.rv.controller.manage.xpd.xpd.query.XpdQuery;
import com.yxt.talent.rv.controller.manage.xpd.xpd.viewobj.XpdVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdIdDto;
import com.yxt.talentrvfacade.bean.ActivityFacadeDTO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdMapper extends CommonMapper<XpdPO> {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(XpdPO record);

    int insertOrUpdate(XpdPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    XpdPO selectByPrimaryKey(String id);

    XpdPO selectByAomPrjId(@Param("orgId") String orgId, @Param("aomPrjId") String aomPrjId);

    XpdPO selectById(String id);

    XpdPO selectByIdAndOrg(@Param("orgId") String orgId, @Param("id") String id);

    void deleteByAomPrjIds(@Param("orgId") String orgId, @Param("aomPrjIds") List<String> aomPrjIds);

    Collection<XpdPO> selectByModelIds(@Param("orgId") String orgId, @Param("modelIds") Collection<String> modelIds);

    List<XpdPO> selectByAomIds(@Param("orgId") String orgId, @Param("aomIds") List<String> aomIds);

    List<XpdUserCountDto> countByUserIds(@Param("orgId") String orgId, @Param("userIds") Collection<String> userIds);

    List<XpdInfoDto> listByUserIdAndSceneId(@Param("orgId") String orgId, @Param("userId") String userId, @Param("sceneId") String sceneId);

    XpdInfoDto findXpdMsg(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    @Nonnull
    IPage<DeptXpdClientVO> selectMyDeptProjects(
        @Param("page") IPage<DeptXpdClientVO> page, @Nonnull @Param("orgId") String orgId,
        @Nonnull @Param("userId") String userId,
        @Param("criteria") DeptProjectClientQuery criteria);

    @Nullable
    DeptXpdClientVO selectMyDeptXpdStatistics(
        @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
        @Param("criteria") DeptProjectClientQuery criteria);

    /**
     * 统计部门经理所管辖下的人员的已完成盘点人数
     *
     * @param orgId
     * @param projectIds
     * @param criteria
     */
    @Nonnull
    List<XpdCompletedUserDTO> selectProjectCompletedUsers(
        @Nonnull @Param("orgId") String orgId, @Param("projectIds") List<String> projectIds,
        @Param("criteria") DeptProjectClientQuery criteria);

    /**
     * 根据refId查询对应的项目id
     * @param orgId
     * @param refId
     * @return
     */
    ActivityFacadeDTO findActivityByRefId(@Param("orgId") String orgId, @Param("refId") String refId);


    List<XpdVO> search(@Param("orgId") String orgId, @Param("criteria") XpdQuery criteria);

    List<String> queryXpdNameByModelId(@Param("orgId") String orgId, @Param("modelIds") List<String> modelIds);

    List<String> listXpdIdByEvalId(@Param("orgId") String orgId, @Param("evalId") String evalId);

    /**
     * 根据 aom 项目id 找到 aom活动id
     * @param orgId
     * @param aomPrjId
     * @return
     */
    List<Activity> findActvByAomPrjId(@Param("orgId") String orgId, @Param("aomPrjId") String aomPrjId);

    /**
     * 根据xpdId查询活动安排
     * @param orgId
     * @param xpdId
     * @return
     */
    List<ActivityArrangeItem> selectActvArrangeItemByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 根据活动ID列表查询未结束的盘点项目
     *
     * @param orgId 机构ID
     * @param activityIds 活动ID列表
     * @return 盘点项目列表
     */
    List<XpdPO> findActiveProjectsByActivityIds(@Param("orgId") String orgId, @Param("activityIds") List<String> activityIds);

    List<String> select4RefreshUserDimCombResult();

    List<XpdPO> selectByOrgId(@Param("orgId") String orgId);

    int batchInsert(@Param("list")List<XpdPO> list);

    List<XpdIdDto> allEndXpdIds(@Param("orgId") String orgId);

    void clearOrgTreeNodes(String orgId);

    void clearOrgTreeNodeRelations(String orgId);

    void clearOrgTreeActionPermissions(String orgId);
}