package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultIndicatorPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface XpdResultIndicatorMapper extends CommonMapper<XpdResultIndicatorPO> {

    List<XpdResultIndicatorPO> selectByOrgId(@Param("orgId")String orgId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(XpdResultIndicatorPO record);

    int insertOrUpdate(XpdResultIndicatorPO record);

    @Select("""
            delete from rv_xpd_result_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
            """)
    void removeByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    int deleteByXpdSdIndicatorIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("indicatorIds") Collection<String> sdIndicatorIds);

    List<XpdResultIndicatorPO> findAllByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdResultIndicatorPO> findByIndicatorIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
            @Param("indicatorIds") List<String> dimIndicatorIds);
}