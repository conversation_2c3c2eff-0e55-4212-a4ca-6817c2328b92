package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.result.dto.CellUserCountDTO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdLevelAggVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimLevelVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.ViewCell4UserCmd;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.UserCellIndexDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.ViewUserDimDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.XpdUserLevelNumDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.DecimalPtgBean;
import com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdUserDimResultDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface XpdResultUserDimMapper extends CommonMapper<XpdResultUserDimPO> {

    List<XpdResultUserDimPO> selectByOrgId(@Param("orgId")String orgId);

    int deleteByPrimaryKey(String id);

    int insert(XpdResultUserDimPO record);

    int insertOrUpdate(XpdResultUserDimPO record);

    int insertOrUpdateSelective(XpdResultUserDimPO record);

    XpdResultUserDimPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdResultUserDimPO record);

    int updateBatch(@Param("list") List<XpdResultUserDimPO> list);

    int batchInsert(@Param("list") List<XpdResultUserDimPO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdResultUserDimPO> list);

    List<UserResultIdDTO> queryIgnoreDelByDimUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId, @Param("userIds") List<String> userIds);

    List<XpdUserDimResultDTO> queryResultByDimUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdDimIds") Collection<String> sdDimIds, @Param("userIds") Collection<String> userIds);

    void batchUpdateResult(List<XpdResultUserDimPO> list);

    @Update("""
        update rv_xpd_result_user_dim set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 and calc_batch_no <> #{calcBatchNo} limit 1000
        """)
    int removeNotCalcByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("calcBatchNo") int calcBatchNo);

    @Update("""
        update rv_xpd_result_user_dim set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 limit 1000
        """)
    int removeCalcByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<DecimalPtgBean> listSortValue(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId, @Param("getScore") boolean getScore);

    void batchUpdateLevelId(List<DecimalPtgBean> list);

    List<XpdUserLevelCountDto> queryUserLevelQty(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("gridLevelIds") List<String> gridLevelIds, @Param("userIds") List<String> userIds);

    List<XpdUserDimLevelDto> queryUserDimLevel(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdDimIds") List<String> sdDimIds, @Param("userIds") List<String> userIds);

    /**
     * 查询维度层级每层的人数
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdLevelAggVO> selectDimLevelAgg(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    /**
     * 获取用户在指定x维度下的排序坐标和y维度的排序坐标
     *
     * @param orgId
     * @param xpdId
     * @param xSdDimId
     * @param ySdDimId
     * @return
     */
    List<UserGridOrderIndexDTO> selectUserGridOrderIndex(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("xSdDimId") String xSdDimId, @Param("ySdDimId") String ySdDimId,
        @Param("query") XpdResultQuery query, @Param("allUserIds") List<String> allUserIds);

    /**
     * 用户维度结果（分页）
     *
     * @param page
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    IPage<XpdTableResultVO> selectUserDimResult(
        Page<XpdTableResultVO> page, @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("query") XpdResultQuery query);

    /**
     * 用户维度结果
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdTableResultVO> selectUserDimResult(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("query") XpdResultQuery query);

    /**
     * 部门视图-部门树(按单维度)
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdDeptUserDimResultDTO> selectDeptUserDimLevel(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    List<XpdResultUserDimPO> findByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 表格视图(落位结果)（分页）
     *
     * @param orgId
     * @param xpdId
     * @param xSdDimId
     * @param ySdDimId
     * @param query
     * @return
     */
    IPage<XpdDimCombTableResultVO> getDimCombTableResult(
        Page<XpdTableResultVO> page, @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId,
        @Param("query") XpdResultQuery query);

    List<XpdDimCombTableResultVO> getDimCombTableResultAll(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("xSdDimId") String xSdDimId, @Param("ySdDimId") String ySdDimId,
        @Param("query") XpdResultQuery query);

    /**
     * 表格视图(落位结果)
     *
     * @param orgId
     * @param xpdId
     * @param xSdDimId
     * @param ySdDimId
     * @param query
     * @return
     */
    List<XpdDimCombTableResultVO> getDimCombTableResult(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId,
        @Param("query") XpdResultQuery query);

    /**
     * 获取宫格视图数据
     *
     * @param orgId
     * @param xpdId
     * @param xSdDimId
     * @param ySdDimId
     * @param query
     * @return
     */
    List<XpdDimCombGridResult> getDimCombGridResult(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId, @Param("query") XpdResultQuery query);

    List<XpdResultUserDimPO> findByXpdIdAndUserId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("userId") String userId);

    /**
     * 获取部门视图数据
     *
     * @param orgId
     * @param xpdId
     * @param xSdDimId
     * @param ySdDimId
     * @param query
     * @return
     */
    List<XpdDimCombDeptResultDTO> getDimCombDeptResult(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId, @Param("query") XpdResultQuery query);

    XpdResultUserDimPO findByXpdIdAndUserIdAndDimId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("userId") String userId, @Param("dimId") String dimId);

    /**
     * 人员各维度所属等级详情
     *
     * @param orgId
     * @param xpdId
     * @param userId
     * @return
     */
    List<XpdResultUserDimLevelVO> getUserDimDetail(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userId") String userId);

    List<XpdUserLevelNumDto> findLevelNum(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    UserCellIndexDTO selectIndex4User(
        @Param("orgId") String orgId, ViewCell4UserCmd cmd
        , @Param("xSdDimId") String xSdDimId, @Param("ySdDimId") String ySdDimId);

    List<XpdResultUserDimPO> findByXpdIdAndUserIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("userIds") List<String> userIds);

    /**
     * 获取部门用户数量
     */
    List<DeptUserCountDTO> getDeptUserCount(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    /**
     * 获取用户XY轴落位信息
     */
    List<UserXYIndexDTO> getUserIndexResult(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId, @Param("query") XpdResultQuery query);

    /**
     * 获取部门格子用户数量
     */
    List<DeptCellUserCountDTO> getDeptCellInfo(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("dimCombId") String dimCombId,
        @Param("query") XpdResultQuery query);

    /**
     * 获取每个宫格的用户数量统计
     */
    List<CellUserCountDTO> getCellUserCount(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("xSdDimId") String xSdDimId,
        @Param("ySdDimId") String ySdDimId, @Param("query") XpdResultQuery query);

    List<ViewUserDimDTO> findUser(@Param("orgId") String orgId, @Param("xpdId") String xpdId);
}