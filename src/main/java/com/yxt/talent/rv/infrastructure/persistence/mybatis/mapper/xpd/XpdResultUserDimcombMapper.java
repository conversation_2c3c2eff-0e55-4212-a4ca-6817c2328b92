package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimcombPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XpdResultUserDimcombMapper extends CommonMapper<XpdResultUserDimcombPO> {

    List<XpdResultUserDimcombPO> selectByOrgId(@Param("orgId")String orgId);

    int deleteByPrimaryKey(String id);

    int insert(XpdResultUserDimcombPO record);

    int insertOrUpdate(XpdResultUserDimcombPO record);

    int insertOrUpdateSelective(XpdResultUserDimcombPO record);

    XpdResultUserDimcombPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdResultUserDimcombPO record);

    int updateBatch(@Param("list") List<XpdResultUserDimcombPO> list);

    int batchInsert(@Param("list") List<XpdResultUserDimcombPO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdResultUserDimcombPO> list);

    void removeByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userIds") List<String> userIds);

    List<XpdResultUserDimcombPO> findByXpdIdAndUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userIds") List<String> userIds);

    void deleteByUserIdAndXpdId(String orgId, String xpdId, String operator);

    void deleteBatch(@Param("ids") List<String> ids, @Param("operator") String operator);
}