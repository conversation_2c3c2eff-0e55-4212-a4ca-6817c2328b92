package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdIndicatorStaDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface XpdResultUserIndicatorMapper extends CommonMapper<XpdResultUserIndicatorPO> {

    List<XpdResultUserIndicatorPO> selectByOrgId(@Param("orgId")String orgId);

    int deleteByPrimaryKey(String id);

    int insert(XpdResultUserIndicatorPO record);

    int insertOrUpdate(XpdResultUserIndicatorPO record);

    int insertOrUpdateSelective(XpdResultUserIndicatorPO record);

    XpdResultUserIndicatorPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdResultUserIndicatorPO record);

    int updateBatch(@Param("list") List<XpdResultUserIndicatorPO> list);

    int batchInsert(@Param("list") List<XpdResultUserIndicatorPO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdResultUserIndicatorPO> list);

    List<UserResultIdDTO> queryIgnoreDelByIndUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdIndicatorId") String sdIndicatorId, @Param("userIds") List<String> userIds);

    void batchUpdateResult(List<XpdResultUserIndicatorPO> list);

    @Update("""
        update rv_xpd_result_user_indicator set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 and calc_batch_no <> #{calcBatchNo} limit 1000
        """)
    int removeNotCalcByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("calcBatchNo") int calcBatchNo);

    @Update("""
        update rv_xpd_result_user_indicator set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 limit 1000
        """)
    int removeCalcByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    @Select("""
        select sd_indicator_id,sum(score_value) as score_value,sum(if(qualified = 1,1,0)) as qualified_qty,count(id) as total_qty from rv_xpd_result_user_indicator
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
        group by sd_indicator_id
        """)
    List<XpdIndicatorStaDTO> queryIndicatorSta(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    XpdResultUserIndicatorPO selectByXpdIdAndResultDimIdAndUserId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("resultDimId") String resultDimId,
        @Param("userId") String userId);

    List<XpdResultUserIndicatorPO> findByIndicatorIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("indicatorIds") List<String> dimIndicatorIds);

    List<XpdResultUserIndicatorPO> findByIndicatorIdsAndUserId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("indicatorIds") Collection<String> dimIndicatorIds,
        @Param("userId") String userId);

    List<XpdResultUserIndicatorPO> findByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdResultUserIndicatorPO> findByUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("userId") String userId);

    List<XpdResultUserIndicatorPO> findByXpdIdAndUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userIds") List<String> userIds);
}