package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDeptUserDimResultDTO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdResultUserLevelDTO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdUserCntDTO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptXpdUserClientVO;
import com.yxt.talent.rv.controller.client.general.xpd.viewobj.XpdResultSubUserVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdLevelAggVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.user.viewobj.XpdOverviewResultDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.DecimalPtgBean;
import com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface XpdResultUserMapper extends CommonMapper<XpdResultUserPO> {

    List<XpdResultUserPO> selectByOrgId(@Param("orgId")String orgId);

    int deleteByPrimaryKey(String id);

    int insert(XpdResultUserPO record);

    int insertOrUpdate(XpdResultUserPO record);

    int insertOrUpdateSelective(XpdResultUserPO record);

    XpdResultUserPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdResultUserPO record);

    int updateBatch(@Param("list") List<XpdResultUserPO> list);

    int batchInsert(@Param("list") List<XpdResultUserPO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdResultUserPO> list);

    List<UserResultIdDTO> queryIgnoreDelByXpdId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("userIds") List<String> userIds);

    void batchUpdateResult(List<XpdResultUserPO> list);

    List<DecimalPtgBean> listSortValue(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("getScore") boolean getScore);

    void batchUpdateLevel(List<DecimalPtgBean> list);

    @Update("""
        update rv_xpd_result_user set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 and calc_batch_no <> #{calcBatchNo} limit 1000
        """)
    int removeNotCalcByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("calcBatchNo") int calcBatchNo);

    @Update("""
        update rv_xpd_result_user set deleted = 1
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 limit 1000
        """)
    int removeCalcByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 查询人才层级每层的人数
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdLevelAggVO> selectLevelAgg(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    /**
     * 用户盘点结果(分页)
     *
     * @param page
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    IPage<XpdTableResultVO> selectUserTableResult(
        Page<XpdTableResultVO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    /**
     * 用户盘点结果
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdTableResultVO> selectUserTableResult(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    /**
     * 部门人才分层结果
     *
     * @param orgId
     * @param xpdId
     * @param query
     * @return
     */
    List<XpdDeptUserDimResultDTO> selectDeptResult(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("query") XpdResultQuery query);

    List<XpdResultUserPO> findUserResultByActId(
        @Param("orgId") String orgId, @Param("xpdId") String actId);

    XpdResultUserPO findByUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("userId") String userId);

    List<XpdResultUserLevelDTO> findLevelByUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("userIds") List<String> userIds);

    List<XpdResultUserPO> findResultsByUserIds(
        @Param("orgId") String orgId, @Param("userIds") Collection<String> userIds);

    XpdResultUserLevelDTO findResultByXpdIdAndUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userId") String userId);

    List<XpdResultSubUserVO> findResultsByActvIdsAndUserId(
        @Param("orgId") String orgId,
        @Param("actvIds") Collection<String> actvIds,
        @Param("userId") String userId);

    /**
     * 统计xpd项目参与盘点人数和完成人数
     *
     * @param orgId
     * @param userId
     * @param criteria
     * @return
     */
    XpdUserCntDTO selectActvUserCnt(
        @Param("orgId") String orgId, @Param("userId") String userId,
        @Param("criteria") DeptProjectClientQuery criteria);

    List<XpdOverviewResultDTO> selectDeptResultGroupByLevel(
        @Param("orgId") String orgId, @Param("userId") String userId,
        @Param("criteria") DeptProjectClientQuery criteria);

    /**
     * 获取我任部门经理的部门下的盘点人员列表
     *
     * @param page
     * @param orgId
     * @param userId
     * @param criteria
     * @return
     */
    IPage<DeptXpdUserClientVO> selectMyDeptXpdtUser(
        @Param("page") IPage<DeptXpdUserClientVO> page, @Param("orgId") String orgId,
        @Param("userId") String userId,
        @Param("criteria") DeptProjectClientQuery criteria);

    List<XpdResultUserPO> findByXpdIdAndUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userIds") List<String> userIds);
}