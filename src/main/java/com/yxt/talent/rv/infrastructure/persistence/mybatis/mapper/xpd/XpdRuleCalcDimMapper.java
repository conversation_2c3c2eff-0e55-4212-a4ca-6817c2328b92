package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdRuleCalcDimMapper extends CommonMapper<XpdRuleCalcDimPO> {

    List<XpdRuleCalcDimPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(XpdRuleCalcDimPO record);

    int insertOrUpdate(XpdRuleCalcDimPO record);

    XpdRuleCalcDimPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdRuleCalcDimPO record);

    int updateBatch(@Param("list") List<XpdRuleCalcDimPO> list);

    int batchInsert(@Param("list") List<XpdRuleCalcDimPO> list);

    List<XpdRuleCalcDimPO> getByXpdId(
            @Param("orgId") String orgId,
            @Param("xpdId") String xpdId);

    /**
     * 当删除维度时要同步删除项目计算规则里的维度
     *
     * @param orgId       机构ID
     * @param userId      操作人ID
     * @param xpdId       项目ID
     * @param delSdDimIds 删除的维度IDs
     */
    void deleteByXpdIdAndSdDimIds(
            @Param("orgId") String orgId,
            @Param("xpdId") String xpdId,
            @Param("delSdDimIds") Collection<String> delSdDimIds,
            @Param("userId") String userId);

    List<XpdRuleCalcDimPO> listByXpdRuleId(
            @Param("orgId") String orgId,
            @Param("xpdRuleId") String xpdRuleId);

    /**
     * 批量逻辑删
     *
     * @param orgId     机构ID
     * @param xpdRuleId 项目规则ID
     * @param userId    操作人ID
     */
    void deleteByXpdRuleId(@Param("orgId") String orgId,
                           @Param("xpdRuleId") String xpdRuleId,
                           @Param("userId") String userId);

    /**
     * 批量逻辑删
     *
     * @param orgId  机构ID
     * @param xpdId  项目ID
     * @param userId 操作人ID
     */
    void deleteByXpdId(@Param("orgId") String orgId,
                       @Param("userId") String userId,
                       @Param("xpdId") String xpdId);
}