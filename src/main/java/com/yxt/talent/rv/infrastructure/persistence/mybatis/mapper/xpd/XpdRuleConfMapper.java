package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XpdRuleConfMapper extends CommonMapper<XpdRuleConfPO> {

    List<XpdRuleConfPO> selectByOrgId(@Param("orgId")String orgId);

    int insert(XpdRuleConfPO record);

    int insertOrUpdate(XpdRuleConfPO record);

    XpdRuleConfPO selectByPrimaryKey(String id);

    /**
     * 查找xpdId下的盘点项目规则配置
     *
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @return 盘点项目配置
     */
    XpdRuleConfPO selectByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    XpdRuleConfPO selectById(String id);

    void deleteByXpdId(@Param("orgId") String orgId,
                       @Param("userId") String userId,
                       @Param("xpdId") String xpdId);
}