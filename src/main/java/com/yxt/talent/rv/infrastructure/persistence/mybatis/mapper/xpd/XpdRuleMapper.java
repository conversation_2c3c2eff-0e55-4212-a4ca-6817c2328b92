package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface XpdRuleMapper extends CommonMapper<XpdRulePO> {

    int deleteByPrimaryKey(String id);

    int insert(XpdRulePO record);

    int insertOrUpdate(XpdRulePO record);

    int insertOrUpdateSelective(XpdRulePO record);

    XpdRulePO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdRulePO record);

    int updateBatch(@Param("list") List<XpdRulePO> list);

    int batchInsert(@Param("list") List<XpdRulePO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdRulePO> list);

    List<XpdRulePO> selectByOrgId(@Param("orgId") String orgId);

    /**
     * 过滤删除数据
     *
     * @param id 规则ID
     * @return one
     */
    XpdRulePO selectById(String id);

    XpdRulePO getByXpdId(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId);

    void deleteByXpdId(
        @Param("orgId") String orgId,
        @Param("userId") String userId,
        @Param("xpdId") String xpdId);

    @Update("update rv_xpd_rule set rule_threshold = #{ruleThreshold},threshold_invalid = #{thresholdInvalid} where id = #{id}")
    void updateRuleThreshold(
        @Param("id") String id, @Param("ruleThreshold") String ruleThreshold,
        @Param("thresholdInvalid") int thresholdInvalid);
}