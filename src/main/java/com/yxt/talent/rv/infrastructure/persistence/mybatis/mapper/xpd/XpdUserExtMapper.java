package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.xpd.user.dto.XpdUserStaticsDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.cmd.XpdImportUserQuery;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportActUserVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【rv_xpd_user_ext(新盘点项目人员表)】的数据库操作Mapper
* @createDate 2024-12-18 17:52:29
* @Entity generator.domain.XpdUserExtPO
*/
@Mapper
public interface XpdUserExtMapper extends CommonMapper<XpdUserExtPO> {

    List<XpdUserExtPO> selectByOrgId(@Param("orgId")String orgId);

    int deleteByPrimaryKey(Long id);

    int insert(XpdUserExtPO record);

    int insertSelective(XpdUserExtPO record);

    XpdUserExtPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(XpdUserExtPO record);

    int updateByPrimaryKey(XpdUserExtPO record);

    int countXpdUserNum(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdUserStaticsDTO> selectUserBaseInfo(String orgId, String xpdId);

    List<XpdUserExtPO> findByOrgIdAndXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    IPage<ImportActUserVO> selectPage(@Param("page") IPage<ImportActUserVO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("importId") String importId, @Param("query") XpdImportUserQuery query
        );

    XpdUserExtPO selectByXpdIdAndUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userId") String userId);

    List<XpdUserExtPO> selectByXpdIdsAndUserId(
            @Param("orgId") String orgId, @Param("xpdIds") Collection<String> xpdId, @Param("userId") String userId);

    List<XpdUserExtPO> selectByXpdIdAndUserIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userIds") Collection<String> userIds);

    void insertOrUpdate(XpdUserExtPO record);

    int insertBatch(@Param("xpdUserExtPOCollection") Collection<XpdUserExtPO> xpdUserExtPOCollection);

    void batchUpdateRvXpdUserExt(@Param("list") Collection<XpdUserExtPO> list);
}
