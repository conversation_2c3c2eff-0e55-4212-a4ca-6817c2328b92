package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动模型-绩效评估-绩效周期配置表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_perf_conf")
public class ActivityPerfConfPO implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 绩效活动ID, rv_activity_perf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID)
    private String actvPerfId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 绩效周期ID
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID)
    private String periodId;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}