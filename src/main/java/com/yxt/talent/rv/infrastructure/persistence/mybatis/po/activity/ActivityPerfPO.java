package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动模型-绩效评估活动表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_perf")
public class ActivityPerfPO implements Serializable {
    /**
     * 主键id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID)
    private String id;

    /**
     * 项目活动ID, 指向rv_activity.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID)
    private String aomActId;

    /**
     * 模型id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_MODEL_ID)
    private String modelId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 活动名称
     */
    private String actvName;

    /**
     * 绩效周期ID, ;隔开
     */
    private String periodIds;

    /**
     * 关联指标ID
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID)
    private String indicatorId;

    /**
     * 评估方式(1-绩效等级, 2-绩效得分(分值))
     */
    private Integer evalType;

    /**
     * 1:动态评估,2:定时评估
     */
    private Integer evalTimeType;

    /**
     * 定时评估时使用,精确到小时
     */
    private LocalDateTime evalTime;

    /**
     * 任务说明,最多500字
     */
    private String actvDesc;

    /**
     * 达标得分
     */
    private BigDecimal scoreQualified;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}