package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动模型-绩效评估配置表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_perf_result_conf")
public class ActivityPerfResultConfPO implements Serializable {
    /**
     * 主键id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID)
    private String id;

    /**
     * 活动ID,rv_activity_perf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID)
    private String actvPerfId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 结果等级
     */
    private String resultName;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 规则为绩效得分时，需要配置规则得分(废弃）
     */
    private BigDecimal ruleScore;

    /**
     * 是否达标(0-不达标, 1-达标)
     */
    private Integer qualified;

    /**
     * 规则JSON配置
     */
    private String ruleConf;

    /**
     * 规则描述
     */
    private String ruleDisplay;
    /**
     * 排序 (序号越小，代表越好)
     */
    private Integer orderNum;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}