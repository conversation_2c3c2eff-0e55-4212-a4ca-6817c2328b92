package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 活动模型-绩效评估结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_perf_result")
public class ActivityPerfResultPO implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 活动ID rv_activity_perf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID)
    private String actvPerfId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 用户id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    /**
     * 绩效结果, rv_activity_perf_result_conf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID)
    private String resultConfId;

    /**
     * 绩效得分
     */
    private BigDecimal resultScore;

    /**
     * 是否达标(0-不达标, 1-达标)
     */
    private Integer qualified;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}