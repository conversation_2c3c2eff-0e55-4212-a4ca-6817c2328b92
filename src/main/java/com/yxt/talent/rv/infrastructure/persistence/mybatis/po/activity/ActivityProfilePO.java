package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动模型-动态人才评估
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_profile")
public class ActivityProfilePO implements Serializable {
    /**
     * 主键id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_ID)
    private String id;

    /**
     * 活动ID, rv_activity.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_ID)
    private String aomActvId;

    /**
     * 模型id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_MODEL_ID)
    private String modelId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 任务名称
     */
    private String profileName;

    /**
     * 任务说明
     */
    private String actvDesc;

    /**
     * 1:动态评估,2:定时评估
     */
    private Integer evalTimeType;

    /**
     * 定时评估时使用,精确到小时
     */
    private LocalDateTime evalTime;

    /**
     * 达标得分
     */
    private BigDecimal scoreQualified;

    /**
     * 未达标得分
     */
    private BigDecimal scoreUnqualified;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}