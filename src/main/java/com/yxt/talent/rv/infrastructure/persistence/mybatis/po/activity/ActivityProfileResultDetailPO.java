package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 活动模型-动态人才评估-结果明细表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_activity_profile_result_detail")
public class ActivityProfileResultDetailPO implements Serializable {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 活动ID, rv_activity_profile.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_ID)
    private String actvProfileId;

    /**
    * 学员id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    /**
    * 人才档案，学员指标结果ID，rv_activity_profile_result.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_RESULT_ID)
    private String userResultId;

    /**
    * 维度条件id,指向规则json中的某一个条件主键
     * 不替换，机构隔离，及时这个id一样，也没啥问题
    */
    private String conditionId;

    /**
    * 标签类型:1-指标 2-标签
    */
    private Integer labelType;

    /**
    * 标签/指标id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.DYN_SPM_LABEL_ID)
    private String labelId;

    /**
    * 标签/指标值id,多个值之间使用半角逗号分隔
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPM_LABEL_VALUE_ID)
    private String labelValueId;

    /**
    * 标签/指标值,多个值之间使用半角逗号分隔
    */
    private String labelValue;

    /**
    * 是否达标:0-不达标 1-达标 2-异常不达标（异常,可能因维度在任职资格中被删除找不到了）
    */
    private Integer qualified;

    /**
    * 创建人主键
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人主键
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}