package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 自用AI问答工具-会话记录
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AiToolMessagePO {
    /**
     * 雪花id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户姓名
     */
    private String fullname;

    /**
     * 问题类型
     */
    private Integer qaType;

    /**
     * 原始问题
     */
    private String origQaText;

    /**
     * 修改后问题
     */
    private String qaText;

    /**
     * 回答内容
     */
    private String answerText;

    /**
     * 用户会话Id
     */
    private String sessionId;

    /**
     * 是否确认过，默认：0
     */
    private Integer checked;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除.0：未删除，1：已删除
     */
    private Integer deleted;

    /**
     * ai给的答案是否有用
     */
    private Integer useful;

    /**
     * 生成本次对话的aibox节点
     */
    private String currNodeInstanceId;
}