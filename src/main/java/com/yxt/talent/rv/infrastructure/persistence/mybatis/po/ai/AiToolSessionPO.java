package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 自用AI问答工具-会话记录表
 */
@Slf4j
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AiToolSessionPO {
    /**
     * 雪花id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 会话标题
     */
    private String sessionTitle;

    /**
     * 会话开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会话结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会话活跃状态，1表示活跃，0表示结束
     */
    private Integer activeStatus;

    /**
     * 本地会话是否对用户有用
     */
    private Integer useful;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除.0：未删除，1：已删除
     */
    private Integer deleted;

}