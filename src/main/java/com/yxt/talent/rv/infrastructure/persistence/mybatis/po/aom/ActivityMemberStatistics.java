package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 学员统计表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActivityMemberStatistics implements Serializable {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 用户id
    */
    private String userId;

    /**
    * 活动id
    */
    private String actvId;

    /**
    * 活动完成状态:0-未完成,1-进行中,2-已完成
    */
    private Integer actvCompletedStatus;

    /**
    * 活动完成进度
    */
    private BigDecimal actvCompletedRate;

    /**
    * 活动完成时间
    */
    private LocalDateTime actvCompletedTime;

    /**
    * 目录完成数
    */
    private Integer periodCompletedCount;

    /**
    * 学员已获得积分
    */
    private Integer earnedPoint;

    /**
    * 学员已获得学分
    */
    private BigDecimal earnedCredit;

    /**
    * 第一次学习时间
    */
    private LocalDateTime firstStudyTime;

    /**
    * 最近学习时间
    */
    private LocalDateTime lastStudyTime;

    /**
    * 所有任务数量
    */
    private Integer allTaskCount;

    /**
    * 所有任务完成数量
    */
    private Integer allTaskCompletedCount;

    /**
    * 所有任务完成率
    */
    private BigDecimal allTaskCompletedRate;

    /**
    * 必修任务数
    */
    private Integer requiredTaskCount;

    /**
    * 必修任务完成数
    */
    private Integer requiredTaskCompletedCount;

    /**
    * 必修任务完成率
    */
    private BigDecimal requiredTaskCompletedRate;

    /**
    * 带教任务数
    */
    private Integer ojtTaskCount;

    /**
    * 带教任务完成数
    */
    private Integer ojtTaskCompletedCount;

    /**
    * 带教必修任务数
    */
    private Integer ojtRequiredTaskCount;

    /**
    * 带教必修任务完成数
    */
    private Integer ojtRequiredTaskCompletedCount;

    /**
    * 带教必修任务完成率
    */
    private BigDecimal ojtRequiredTaskCompletedRate;

    /**
    * 最近一个必修任务完成时间点
    */
    private LocalDateTime lastRequiredTaskCompletedTime;

    /**
    * 最近一个任务完成时间点
    */
    private LocalDateTime lastAllTaskCompletedTime;

    /**
    * 是否删除 0未删除 1已删除
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 0:未归档,1:已归档
    */
    private Integer dbArchived;

    @Serial
    private static final long serialVersionUID = 1L;
}