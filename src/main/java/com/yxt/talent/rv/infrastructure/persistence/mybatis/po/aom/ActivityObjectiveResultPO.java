package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动指标结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActivityObjectiveResultPO implements Serializable {
    /**
    * 记录id
    */
    private Long id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 活动/项目id
    */
    private String actvId;

    /**
    * 学员id
    */
    private String userId;

    /**
    * 基础记录id
    */
    private Long baseActvResultId;

    /**
    * 指标id
    */
    private String objectiveId;

    /**
    * 指标模型id
    */
    private String objectiveModeId;

    /**
    * 指标类型(1-知识点, 2-技能, 3-能力)
    */
    private Integer objectiveType;

    /**
    * 指标得分
    */
    private BigDecimal objectiveScore;

    /**
    * 指标总分
    */
    private BigDecimal objectiveTotalScore;

    /**
    * 指标等级
    */
    private Integer objectiveLevel;

    /**
    * 指标结果(1-优势, 2-待提升, 3-达标)
    */
    private Integer objectiveResult;

    /**
    * 是否删除 0未删 1已删
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更细时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 0:未归档,1:已归档
    */
    private Integer dbArchived;

    @Serial
    private static final long serialVersionUID = 1L;
}