package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 活动结果基础表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaseActivityResultPO implements Serializable {
    /**
    * 记录id
    */
    private Long id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 活动id
    */
    private String actvId;

    /**
    * 学员id
    */
    private String userId;

    /**
    * 叶节点id
    */
    private Long itemId;

    /**
    * 叶节点引用对象的具体类型(UACD注册表中定义)
    */
    private String refRegId;

    /**
    * 是否必修 0否 1是
    */
    private Integer required;

    /**
    * 0未开始,1进行中,2已完成
    */
    private Integer resultStatus;

    /**
    * 开始学习时间
    */
    private LocalDateTime startTime;

    /**
    * 完成时间
    */
    private LocalDateTime completedTime;

    /**
    * 最近学习时间
    */
    private LocalDateTime lastStudyTime;

    /**
    * 是否手动标记完成0 否 1是
    */
    private Integer handCompleted;

    /**
    * 是否删除 0未删 1已删
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更细时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 类型(1-活动, 2-项目)
    */
    private Integer actvType;

    /**
    * 子类型(1-Assessment, 2-Content, 3-Practice)
    */
    private Integer subType;

    @Serial
    private static final long serialVersionUID = 1L;
}