package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 活动/项目参与人员关系表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RvActivityParticipationMemberPO implements Serializable {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 项目/活动id
    */
    private String actvId;

    /**
    * 参与id
    */
    private Long participationId;

    /**
    * 学员id
    */
    private String userId;

    /**
    * 0-旁听学员 1-正式学员
    */
    private Integer formal;

    /**
    * 加入方式（1-手动加入 2-自动加入 3-通过报名加入）
    */
    private Integer joinMethod;

    /**
    * 小组id
    */
    private Long groupId;

    /**
    * 删除标志
    */
    private Integer deleted;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 修改人id
    */
    private String updateUserId;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否归档 0-未归档 1-已归档
    */
    private Integer dbArchived;

    /**
    * 加入时间
    */
    private LocalDateTime joinTime;

    /**
    * 生效时间
    */
    private LocalDateTime effectTime;

    /**
    * 学员活动开始时间
    */
    private LocalDateTime startTime;

    /**
    * 学员活动结束时间
    */
    private LocalDateTime endTime;

    /**
    * 0:未被延期 1:被延期
    */
    private Integer delayFlag;

    @Serial
    private static final long serialVersionUID = 1L;
}