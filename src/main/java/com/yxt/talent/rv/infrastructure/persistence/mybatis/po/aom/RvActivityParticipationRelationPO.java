package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 活动/项目参与人员关系表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RvActivityParticipationRelationPO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 项目/活动id
     */
    private String actvId;

    /**
     * 参与id
     */
    private Long participationId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 类型 0-负责人 1-跟踪人 2-导师范围 3-培训计划负责人 4-预算负责人 5-辅导员
     */
    private Integer relationType;

    /**
     * 排序号
     */
    private Integer orderIndex;

    /**
     * 辅导员所属小组id(rv_group主键id)
     */
    private Long targetId;

    /**
     * 删除标志
     */
    private Integer deleted;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 用户名称（非数据库字段）
     */
    private String fullname;

    @Serial
    private static final long serialVersionUID = 1L;
}
