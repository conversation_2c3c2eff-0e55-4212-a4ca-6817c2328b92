package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评鉴活动结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_assessment_activity_result")
public class RvAssessmentActivityResultPO {
    /**
     * 记录id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 活动/项目id
     */
    @TableField(value = "actv_id")
    private String actvId;

    /**
     * 学员id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 基础记录id
     */
    @TableField(value = "base_actv_result_id")
    private Long baseActvResultId;

    /**
     * 任务重复类型 0:默认 1:多班次任务 2:指派重复学习
     */
    @TableField(value = "result_repeat_flag")
    private Integer resultRepeatFlag;

    /**
     * 指派重新学习次数
     */
    @TableField(value = "repeat_count")
    private Integer repeatCount;

    /**
     * 生效的子任务结果Id
     */
    @TableField(value = "sub_task_result_id")
    private Long subTaskResultId;

    /**
     * 活动是否通过 0否 1是
     */
    @TableField(value = "passed")
    private Integer passed;

    /**
     * 活动批阅状态 0:未开始；1:考试中；2:已提交；3:批阅中；4:已完成
     */
    @TableField(value = "target_status")
    private Integer targetStatus;

    /**
     * 活动得分
     */
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 活动总分
     */
    @TableField(value = "total_score")
    private BigDecimal totalScore;

    /**
     * 是否删除 0未删 1已删
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 更细时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 0:未归档,1:已归档
     */
    @TableField(value = "db_archived")
    private Integer dbArchived;

    /**
     * 扩展信息
     */
    @TableField(value = "ext")
    private String ext;

    /**
     * 叶节点id
     */
    @TableField(value = "item_id")
    private Long itemId;
}