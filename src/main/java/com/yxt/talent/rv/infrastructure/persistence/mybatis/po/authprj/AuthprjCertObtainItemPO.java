package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 项目证书条件表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_cert_obtain_item")
public class AuthprjCertObtainItemPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 项目证书id, rv_authprj_cert.id
     */
    @TableField(value = "authprj_cert_id")
    private String authprjCertId;

    /**
     * 认证项目ID, rv_authprj.id
     */
    @TableField(value = "authprj_id")
    private String authprjId;

    /**
     * 证书模板id
     */
    @TableField(value = "cert_temp_id")
    private String certTempId;

    /**
     * 指定活动ID, 指向 rv_activity_arrange_item.ref_id
     */
    @TableField(value = "act_id")
    private String actId;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新日期
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}