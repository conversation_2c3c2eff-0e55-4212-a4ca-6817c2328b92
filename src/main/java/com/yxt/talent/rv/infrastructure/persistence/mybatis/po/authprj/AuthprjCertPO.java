package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 项目证书表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_cert")
public class AuthprjCertPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 认证项目ID, rv_authprj.id
     */
    @TableField(value = "authprj_id")
    private String authprjId;

    /**
     * 证书模板id
     */
    @TableField(value = "cert_temp_id")
    private String certTempId;

    /**
     * 证书获得方式(0-认证结果 1-总得分大于 2-通过率大于, 3-通过特定认证任务， 4-通过所有认证任务)
     */
    @TableField(value = "obtain_type")
    private Integer obtainType;

    /**
     * obtain_type为0时，分层结果，1：分数，2：通过率，3：rv_authprj_cert_obtain_item存多个活动ID，4：空
     */
    @TableField(value = "obtain_value")
    private String obtainValue;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新日期
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}