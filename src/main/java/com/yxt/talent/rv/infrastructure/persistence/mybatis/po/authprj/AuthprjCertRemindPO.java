package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 项目到期提醒表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_cert_remind")
public class AuthprjCertRemindPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 项目证书id, rv_authprj_cert.id
     */
    @TableField(value = "authprj_cert_id")
    private String authprjCertId;

    /**
     * 到期前提醒天数
     */
    @TableField(value = "remind_time")
    private Integer remindTime;

    /**
     * 提醒角色，支持多个角色，分号隔开，0：员工，1：部门经理，2：直属经理，3：项目负责人
     */
    @TableField(value = "remind_type")
    private String remindType;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新日期
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}