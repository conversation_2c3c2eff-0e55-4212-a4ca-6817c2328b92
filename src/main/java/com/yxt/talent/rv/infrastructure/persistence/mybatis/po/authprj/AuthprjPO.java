package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;

/**
 * 认证项目
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj")
public class AuthprjPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 项目ID, 指向rv_activity.id
     */
    @TableField(value = "aom_prj_id")
    private String aomPrjId;

    @Transient
    @TableField(exist = false)
    private String aomPrjName;

    /**
     * 状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)
     */
    @Transient
    @TableField(exist = false)
    private Integer actvStatus;

    /**
     * 模型id
     */
    @TableField(value = "model_id")
    private String modelId;

    /**
     * 0:未删除 1:已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
}