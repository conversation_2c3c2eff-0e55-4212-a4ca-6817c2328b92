package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 认证项目用户结果变更记录
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_result_user_chglog")
public class AuthprjResultUserChglogPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 认证项目ID, rv_authprj.id
     */
    @TableField(value = "authprj_id")
    private String authprjId;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 变更前分层结果
     */
    @TableField(value = "old_level_id")
    private String oldLevelId;

    /**
     * 分层结果
     */
    @TableField(value = "level_id")
    private String levelId;

    /**
     * 变更前项目结果-分值
     */
    @TableField(value = "old_score_value")
    private BigDecimal oldScoreValue;

    /**
     * 项目结果-分值
     */
    @TableField(value = "score_value")
    private BigDecimal scoreValue;

    /**
     * 原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 0:未删除 1:已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
}