package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 认证活动-用户指标结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_result_user_indicator")
public class AuthprjResultUserIndicatorPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 认证项目id
     */
    @TableField(value = "authprj_id")
    private String authprjId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 指标id
     */
    @TableField(value = "sd_indicator_id")
    private String sdIndicatorId;

    /**
     * 来源活动id(如果一个指标有多个活动都认证了，则取创建时间最新的那个活动的得分)， 指向rv_activity_arrange_item.ref_id
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 总分
     */
    @TableField(value = "score_total")
    private BigDecimal scoreTotal;

    /**
     * 得分
     */
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人主键
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}