package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 认证活动-分层规则表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_authprj_rule_level")
public class AuthprjRuleLevelPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 认证项目id
     */
    @TableField(value = "authprj_id")
    private String authprjId;

    /**
     * 分层名称
     */
    @TableField(value = "level_name")
    private String levelName;

    /**
     * 分层顺序
     */
    @TableField(value = "order_index")
    private Integer orderIndex;

    /**
     * 是否通过 0-未通过 1-通过
     */
    @TableField(value = "passed")
    private Integer passed;

    /**
     * 计算规则json, 不能为空
     */
    @TableField(value = "formula")
    private String formula;

    /**
     * 可视化的计算规则表达式
     */
    @TableField(value = "formula_display")
    private String formulaDisplay;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人主键
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}