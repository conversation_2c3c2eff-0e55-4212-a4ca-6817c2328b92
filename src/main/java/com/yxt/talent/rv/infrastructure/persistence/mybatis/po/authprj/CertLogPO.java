package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 证书领取记录表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_cert_log")
public class CertLogPO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 项目ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 证书模板id
     */
    @TableField(value = "cert_temp_id")
    private String certTempId;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 获取证书的状态，-1：发布失败, 0：待发布，1：已发布，2:被撤销 3:已删除 4:已过期
     */
    @TableField(value = "cert_status")
    private Integer certStatus;

    /**
     * 是否删除(0-未删除,1-已删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新日期
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}