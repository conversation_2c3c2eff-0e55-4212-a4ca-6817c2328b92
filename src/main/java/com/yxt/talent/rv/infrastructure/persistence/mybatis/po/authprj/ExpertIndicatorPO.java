package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 专家-指标关系表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "rv_expert_indicator")
public class ExpertIndicatorPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 专家ID
     */
    @TableField(value = "expert_id")
    private String expertId;

    /**
     * 指标ID
     */
    @TableField(value = "ind_id")
    private String indId;

    /**
     * 0:未删除 1:已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
}