package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校准会
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetPO {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 盘点项目id, 指向rv_xpd.id
    */
    private String xpdId;

    /**
    * 校准会状态(0-未开始，1-进行中，2-已结束)
    */
    private Integer calimeetStatus;

    /**
    * 校准任务名称
    */
    private String calimeetName;

    /**
    * 组织形式(0-在线校准，1-线下校准)
    */
    private Integer calimeetMode;

    /**
    * 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
    */
    private Integer calimeetType;

    /**
    * 开始时间
    */
    private LocalDateTime startTime;

    /**
    * 结束时间
    */
    private LocalDateTime endTime;

    /**
    * 是否开启比例控制(0-否，1-是)
    */
    private Integer showRatio;

    /**
    * 会议记录
    */
    private String record;

    /**
    * 是否删除(0-否,1-是)
    */
    private Integer deleted;

    /**
    * 创建人主键
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人主键
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}