package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校准会-干系人员表
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetParticipantsPO {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 会议id
    */
    private String calimeetId;

    /**
    * 干系人id
    */
    private String userId;

    /**
    * 干系人类型(1-组织者，2-校准人)
    */
    private Integer userType;

    /**
    * 校准人是否完成校准任务(0-未完成，1-已完成)
    */
    private Integer caliStatus;

    /**
    * 是否删除(0-否,1-是)
    */
    private Integer deleted;

    /**
    * 创建人主键
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人主键
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}