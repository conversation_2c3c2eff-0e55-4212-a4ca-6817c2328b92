package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import java.time.LocalDateTime;

import com.yxt.talent.rv.domain.RvBaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校准会-校准记录-维度组变更记录表
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetRecordItemPO extends RvBaseEntity {
    /**
    * id
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 校准会id
    */
    private String calimeetId;

    /**
    * 被校准人id
    */
    private String userId;

    /**
    * 指向校准记录表，rv_calimeet_record.id
    */
    private String calimeetRecordId;

    /**
    * 维度组合id
    */
    private String dimCombId;

    /**
    * 原落位宫格编号
    */
    private Integer originalCellIndex;

    /**
    * 现落位宫格编号
    */
    private Integer cellIndex;

    /**
    * 校准幅度
    */
    private Integer caliShift;

    /**
    * 是否删除
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人id
    */
    private String updateUserId;
}