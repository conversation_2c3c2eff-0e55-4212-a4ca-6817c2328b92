package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import java.time.LocalDateTime;

import com.yxt.talent.rv.domain.RvBaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校准会-校准记录表
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetRecordPO extends RvBaseEntity {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 校准会id
    */
    private String calimeetId;

    /**
    * 人员id
    */
    private String userId;

    /**
    * 发展建议
    */
    private String suggestion;

    /**
    * 校准原因
    */
    private String reason;

    /**
    * 校准详情
     * @see com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultWrapDto
    */
    private String caliDetails;

    /**
    * 维度结果
     * @see com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultWrapDto
    */
    private String resultDetails;

    /**
    * 是否删除
    */
    private Integer deleted;

    /**
    * 创建人主键
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人主键
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}