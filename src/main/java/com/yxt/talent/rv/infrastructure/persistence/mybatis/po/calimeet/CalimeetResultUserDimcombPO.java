package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 盘点用户维度组结果(校准会创建时的盘点结果快照)
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetResultUserDimcombPO {
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    private String xpdId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 维度组id
     */
    private String dimCombId;

    /**
     * 现落位宫格编号,
     */
    private Integer cellIndex;

    /**
     * 现落位宫格id
     */
    private String cellId;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的原始的计算出来的数据快照
     */
    private String originalSnap;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 校准会id
     */
    private String calimeetId;
}