package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校准会-被校准人员表
 */
@Getter
@Setter
@NoArgsConstructor
public class CalimeetUserPO {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 校准会id
    */
    private String calimeetId;

    /**
    * 人员id
    */
    private String userId;

    /**
    * 校准状态(0-未校准，1-已校准，2-无需缴准)
    */
    private Integer caliStatus;

    /**
    * 指向最新一条校准记录，rv_calimeet_record.id
    */
    private String latestRecordId;

    /**
    * 是否删除
    */
    private Integer deleted;

    /**
    * 创建人主键
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人主键
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}