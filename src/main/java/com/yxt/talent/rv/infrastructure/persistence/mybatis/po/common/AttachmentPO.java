package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.yxt.spsdk.democopy.DemoCopyConstants.NULL_STRATEGY_EMPTY;

/**
 * 附件表,目前主要应用于校准会附件上传
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_appendix")
public class AttachmentPO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    // 附件名称
    @TableField("app_name")
    private String appName = "";

    // 附件链接
    @TableField("app_url")
    private String appUrl = "";

    // 附件来源（1-校准会）
    @TableField("app_source")
    private Integer appSource = 0;

    // 附件来源Id
    @TableField("app_source_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_CALIMEET_ID, mappedNullStrategy = NULL_STRATEGY_EMPTY)
    private String appSourceId;
}
