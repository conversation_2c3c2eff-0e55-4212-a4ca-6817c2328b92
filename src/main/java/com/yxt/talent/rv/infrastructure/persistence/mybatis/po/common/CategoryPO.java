package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 盘点项目所属类别
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_category")
public class CategoryPO {
    // 主键
    @TableId
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_CATEGORY_ID)
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 分类名称
    @TableField("category_name")
    private String categoryName;

    // 分类（0-盘点项目分类）
    @TableField("category_type")
    private Integer categoryType = 0;

    // 是否启用（0-禁用，1-启用）
    @TableField("category_enable")
    private Integer categoryEnable = 0;

    // 说明
    @TableField("remark")
    private String remark = "";

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
