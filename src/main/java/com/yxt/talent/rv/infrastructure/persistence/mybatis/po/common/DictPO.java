package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 人才盘点字典表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_dict")
public class DictPO {
    // 字典表id
    @TableId
    private String id;

    // 字典类型(1-绩效枚举)
    @TableField("dict_type")
    private Integer dictType;

    // 字典名称
    @TableField("dict_name")
    private String dictName;

    // 字典枚举
    @TableField("dict_value")
    private Integer dictValue;

    // 排序
    @TableField("order_index")
    private Integer orderIndex;

    // 状态(0-禁用，1-启用)
    @TableField("enabled")
    private Integer enabled = 1;

    // 创建人id
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 修改人id
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
