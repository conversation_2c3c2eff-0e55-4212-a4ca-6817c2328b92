package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf;

import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 绩效表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PerfGradePO {

    // 主键
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_PERF_GRADE_ID)
    private String id;

    // 机构id
    private String orgId;

    // 绩效等级名称
    private String gradeName = "";

    // 绩效等级排序，从100开始，最高130，由于高绩效排序在前,100代表最高绩效等级
    private Integer gradeValue = 100;

    // 绩效等级排序
    private Integer orderIndex = 0;

    // 0-未删除，1-已删除
    private Integer deleted = 0;

    // 创建人主键
    private String createUserId;

    // 创建时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    private String updateUserId;

    // 更新时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    // 0-禁用/1-启用
    private Integer state = 1;
}
