package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 绩效表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_performance")
public class PerfPO {
    // 主键
    private String id;

    // 机构id
    private String orgId;

    // 绩效周期id
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String periodId;

    // 绩效值（A,B,C,D,S）
    private Integer periodLevel;

    // 用户id
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String userId;

    // 创建人主键
    private String createUserId;

    // 创建时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    private String updateUserId;

    // 更新时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    // 绩效得分
    @Nullable
    private BigDecimal perfPoint;

    // 绩效总分
    @Nullable
    private BigDecimal perfScore;

    // 绩效活动
    private String perfActivity;

    // 绩效活动 id
    private String perfThirdActivityId;

}
