package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf;

import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 绩效周期表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PerfPeriodPO {
    // 主键
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID)
    private String id;

    // 机构id
    private String orgId;

    // 周期名称
    private String periodName;

    // 排序
    private Integer orderIndex;

    // 创建人主键
    private String createUserId;

    // 创建时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    private String updateUserId;

    // 更新时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    // 绩效周期类型（老数据该字段可能为空）：0-月度绩效, 1-季度绩效, 2-半年度，3-年度
    private Integer cycle;

    // 月度:1-12, 季度:1-4, 半年度:1-2,年份:20xx
    private Integer period;

    // 绩效年份，格式：YYYY
    private Integer yearly;

    // 是否删除(0-否,1-是)
    private Integer deleted;

    // 绩效总分
    private BigDecimal scoreTotal;
}
