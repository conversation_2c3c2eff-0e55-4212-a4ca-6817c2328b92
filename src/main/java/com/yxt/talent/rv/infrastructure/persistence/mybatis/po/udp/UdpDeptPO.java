package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class UdpDeptPO {
    /**
     * UDP部门表主键
     */
    private String id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 父部门id
     */
    private String parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编号(内部给每个部门的编号)-不超过20位
     */
    private String code;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门id全路径
     */
    private String idFullPath;

    /**
     * 部门路径,如000001.000002.000003或000004.000005)
     */
    private String routingPath;

    /**
     * 第三方系统ID
     */
    private String thirdId;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 是否包含虚拟部门(0-否,1-是)
     */
    private Integer hasVirtualDept;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-否,1-是)
     */
    private Integer deleted;

    /**
     * 第三方系统同步时间
     */
    private LocalDateTime thirdSyncTime;

    /**
     * 1.0系统迁移更新时间
     */
    private LocalDateTime mgtSyncTime;
}
