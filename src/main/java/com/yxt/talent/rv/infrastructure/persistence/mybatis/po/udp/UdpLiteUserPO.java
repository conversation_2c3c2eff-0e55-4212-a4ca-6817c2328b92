package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NKeyPath;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.annotation.L10NValuePath;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 微服务小UDP表sample表, 不支持集团版，如果没有特殊情况，建议使用该表
 */
@Setter
@Getter
@ToString
@TableName(value = "udp_lite_user_sp")
public class UdpLiteUserPO implements L10NContent {

    /**
     * 微服务小UDP表sample表主键
     */
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    /**
     * 用户头像
     */
    private String imgUrl;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 工号
     */
    private String userNo;

    /**
     * 第三方用户ID
     */
    private String thirdUserId;

    /**
     * 0-表示不确定 1-表示男 2-表示女
     */
    private Integer sex;

    /**
     * 用户状态：用于标识当前用户的状态(0-禁用,1-启用)
     */
    private Integer status;

    /**
     * 主部门ID
     */
    private String deptId;

    /**
     * 主部门全路径名称，例：研发中心->园区
     */
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    @L10NValuePath
    private String deptName;

    /**
     * 直属经理ID
     */
    private String managerId;

    /**
     * 直属经理姓名
     */
    private String managerFullname;

    /**
     * 职级ID
     */
    private String gradeId;

    /**
     * 主岗位ID
     */
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String positionId;

    /**
     * 主岗位名称
     */
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;

    /**
     * 职级名称
     */
    private String gradeName;

    /**
     * 部门经理
     */
    private String deptManagerId;

    /**
     * 部门经理姓名
     */
    private String deptManagerFullname;

    /**
     * 手机是否验证:0-否，1-是
     */
    private Integer mobileValidated;

    /**
     * 邮箱是否验证:0-否，1-是
     */
    private Integer emailValidated;

    /**
     * 语言
     */
    private String locale;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记(0-否,1-是)
     */
    private Integer deleted;

    /**
     * 时区(存储timezoneId)
     */
    private String timezone;

    /**
     * 区域
     */
    private String region;

    /**
     * 入职时间
     */
    private LocalDateTime hireDate;

    /**
     * 国家/地区(区号)
     */
    private String areaCode;

    @TableField(exist = false)
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.KEY)
    @L10NKeyPath(ignoreFirstKey = true)
    private String deptIdFullPath;


}
