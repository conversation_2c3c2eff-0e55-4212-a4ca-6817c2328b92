package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "udp_org")
public class UdpOrgPO {
    /**
     * 机构ID
     */
    @TableField(value = "id")
    private String id;

    /**
     * 机构名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 机构代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 平台类型: 0-客户试用, 1-签约开通, 2-员工自用, 3-集中交付
     */
    @TableField(value = "org_type")
    private Integer orgType;

    /**
     * 机构logo
     */
    @TableField(value = "logo_url")
    private String logoUrl;

    /**
     * 机构简介
     */
    @TableField(value = "description")
    private String description;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * 站点名称
     */
    @TableField(value = "site_name")
    private String siteName;

    /**
     * 开始日期
     */
    @TableField(value = "start_date")
    private LocalDateTime startDate;

    /**
     * 结束日期
     */
    @TableField(value = "end_date")
    private LocalDateTime endDate;

    /**
     * 是否正式机构
     */
    @TableField(value = "official_org")
    private Integer officialOrg;

    /**
     * 是否模板机构
     */
    @TableField(value = "templated_org")
    private Integer templatedOrg;

    /**
     * 是否预留机构
     */
    @TableField(value = "reserved_org")
    private Integer reservedOrg;

    /**
     * 是否是销售机构
     */
    @TableField(value = "sales_org")
    private Integer salesOrg;

    /**
     * 是否是销售机构模板
     */
    @TableField(value = "sales_org_template")
    private Integer salesOrgTemplate;

    /**
     * 模板机构id
     */
    @TableField(value = "sales_org_template_id")
    private String salesOrgTemplateId;

    /**
     * 版本号: 10-工具版,100-基础版,101-渠道基础版,200-标准版,250-过渡版,300-中欧标准版,350-渠道标准版,400-专业版
     */
    @TableField(value = "edition_code")
    private String editionCode;

    /**
     * 渠道来源: 99-关联开通,100-微信isv,101-中欧渠道,102-内容BU,103-线下渠道,104-钉钉isv,105-领带金融,106-飞书isv
     */
    @TableField(value = "source_code")
    private String sourceCode;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 机构状态: 0-禁用;1-正常;2-停用维护中
     */
    @TableField(value = "org_status")
    private Integer orgStatus;

    /**
     * 机构站点ICON
     */
    @TableField(value = "icon_url")
    private String iconUrl;

    /**
     * 机构名称国际化Code
     */
    @TableField(value = "name_i18n")
    private String nameI18n;

    /**
     * 机构站点名称国际化Code
     */
    @TableField(value = "site_name_i18n")
    private String siteNameI18n;

    /**
     * 机构名称(CSM)
     */
    @TableField(value = "org_name")
    private String orgName;
}
