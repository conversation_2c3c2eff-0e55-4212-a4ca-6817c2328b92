package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "udp_position_catalog")
public class UdpPosCatPO {
    /**
     * 岗位分类表主键
     */
    @TableField(value = "id")
    private String id;

    /**
     * 所属企业id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 岗位分类名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 上级岗位分类id
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 分类路径,如000001.000002.000003或000004.000005)
     */
    @TableField(value = "routing_path")
    private String routingPath;

    /**
     * 第三方系统ID
     */
    @TableField(value = "third_id")
    private String thirdId;

    /**
     * 预留排序
     */
    @TableField(value = "order_index")
    private Integer orderIndex;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-否，1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 强模式集团关联主平台id
     */
    @TableField(value = "corp_refer_id")
    private String corpReferId;

    /**
     * 第三方系统同步时间
     */
    @TableField(value = "third_sync_time")
    private LocalDateTime thirdSyncTime;

    /**
     * 1.0系统迁移更新时间
     */
    @TableField(value = "mgt_sync_time")
    private LocalDateTime mgtSyncTime;

    /**
     * 岗位分类编号
     */
    @TableField(value = "code")
    private String code;
}
