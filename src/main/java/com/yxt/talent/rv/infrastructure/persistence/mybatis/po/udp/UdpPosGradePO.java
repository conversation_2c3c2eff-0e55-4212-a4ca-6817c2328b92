package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "udp_position_grade")
public class UdpPosGradePO {
    /**
     * 企业岗位职级表主键
     */
    @TableField(value = "id")
    private String id;

    /**
     * 所属企业id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 职级名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 第三方系统ID
     */
    @TableField(value = "third_id")
    private String thirdId;

    /**
     * 排序
     */
    @TableField(value = "order_index")
    private Integer orderIndex;

    /**
     * 创建者uuid
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者uuid
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 删除标记(0-否,1-是)
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 强模式集团关联主平台id
     */
    @TableField(value = "corp_refer_id")
    private String corpReferId;

    /**
     * 第三方系统同步时间
     */
    @TableField(value = "third_sync_time")
    private LocalDateTime thirdSyncTime;

    /**
     * 1.0系统迁移更新时间
     */
    @TableField(value = "mgt_sync_time")
    private LocalDateTime mgtSyncTime;
}
