package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "udp_position")
public class UdpPosPO implements L10NContent {

    /**
     * UDP岗位表主键
     */
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.KEY)
    private String id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 岗位分类id
     */
    private String catalogId;

    /**
     * 岗位职级id
     */
    @Nullable
    private String gradeId;

    /**
     * 岗位名称
     */
    @Nullable
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String name;

    /**
     * 岗位编号
     */
    private String code;

    /**
     * 岗位描述
     */
    private String description;

    /**
     * 第三方系统ID
     */
    @Nullable
    private String thirdId;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 关键岗位(0-否,1-是)
     */
    private Integer keyPosition;

    /**
     * 岗位职责
     */
    private String responsibility;

    /**
     * 岗位要求
     */
    private String requirement;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-否,1-是)
     */
    private Integer deleted;

    /**
     * 备用1
     */
    private String spare1;

    /**
     * 备用2
     */
    private String spare2;

    /**
     * 备用3
     */
    private String spare3;

    /**
     * 强模式集团关联主平台id
     */
    private String corpReferId;

    /**
     * 第三方系统同步时间
     */
    private LocalDateTime thirdSyncTime;

    /**
     * 1.0系统迁移更新时间
     */
    private LocalDateTime mgtSyncTime;
}
