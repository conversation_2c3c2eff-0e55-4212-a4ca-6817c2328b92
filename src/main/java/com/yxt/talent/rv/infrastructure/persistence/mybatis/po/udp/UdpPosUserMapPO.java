package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName(value = "udp_position_user_map")
public class UdpPosUserMapPO {
    /**
     * UDP岗位用户关系表主键
     */
    @TableField(value = "id")
    private String id;

    /**
     * 机构ID
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 岗位id
     */
    @TableField(value = "position_id")
    private String positionId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 是否兼职(0-否,1-是)
     */
    @TableField(value = "parttime")
    private Integer parttime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 任职状态 0-未启用 1-启用
     */
    @TableField(value = "um_status")
    private Integer umStatus;
}
