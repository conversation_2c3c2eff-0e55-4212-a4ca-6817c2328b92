package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户的任职履历表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CareerHistoryPO implements Serializable {
    /**
     * 主键Id
     */
    private String id;

    /**
     * 三方用户id
     */
    private String thirdUserId;

    /**
     * 机构Id
     */
    private String orgId;

    /**
     * 部门名称
     */
    private String thirdDeptName;

    /**
     * 岗位名称
     */
    private String thirdPositionName;

    /**
     * 职级名称
     */
    private String thirdJobgradeName;

    /**
     * 任职动作名称（入职、转正、晋升、转岗等）
     */
    private String actionName;

    /**
     * 任职动作发生时间
     */
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private LocalDateTime occurrenceTime;

    /**
     * 是否删除(0-否,1-是)
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 三方履历id
     */
    private String thirdCareerHistoryId;

    private String userId;

}