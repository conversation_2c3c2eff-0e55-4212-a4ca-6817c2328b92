package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 用户的奖惩信息历史
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RewardPunishmentHistoryPO implements Serializable {
    /**
    * 主键Id
    */
    private String id;

    /**
    * 用户id
    */
    private String thirdUserId;

    /**
    * 机构Id
    */
    private String orgId;

    /**
    * 奖惩类型（1-奖项 2-惩罚）
    */
    private Integer rpType;

    /**
    * 奖惩名称
    */
    private String rpName;

    /**
    * 获得时间
    */
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY, timezone = Constants.STR_GMT8)
    private LocalDate acqTime;

    /**
    * 发布方
    */
    private String pubFrom;

    /**
    * 是否删除(0-否,1-是)
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
     * udp用户id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    @Serial
    private static final long serialVersionUID = 1L;
}