package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user;

import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 用户扩展信息表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserExtPO implements Serializable {
    /**
     * 主键Id，无业务意义，不等同于udp_user.id
     */
    private String id;

    /**
     * 三方用户id
     */
    private String thirdUserId;

    /**
     * 机构Id
     */
    private String orgId;

    /**
     * 禁用/启用(0-禁用 1-启用)
     */
    private Integer enabled;

    /**
     * 绚星平台用户ID，等同于udp_user.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId = "";

    /**
     * 是否管理者(0-否 1-是)
     */
    private Integer manager;

    /**
     * 是否关键岗位(0-否 1-是)
     */
    private Integer keyPosition;

    /**
     * 职等
     */
    private String gradeLevel;

    /**
     * 常住地
     */
    private String residenceAddress;

    /**
     * 职业资格证书，多个证书使用半角分号分割
     */
    private String profCerts;

    /**
     * 是否删除(0-否,1-是)
     */
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 更新时间
     */
    private LocalDateTime updateTime = LocalDateTime.now();

    @Serial
    private static final long serialVersionUID = 1L;
}