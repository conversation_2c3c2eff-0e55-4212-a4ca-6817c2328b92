package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 业务管理者 : 我关注的项目盘点和人岗匹配项目
 */
@Getter
@Setter
public class UserFocusPO {
    /**
     * 主键
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 人员id
     */
    private String userId;

    /**
     * 盘点项目id或人岗匹配项目id
     */
    private String targetId;

    /**
     * 项目类型（0-盘点项目 1-人岗匹配项目）
     */
    private Integer targetType = 0;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    private Integer deleted = 0;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
