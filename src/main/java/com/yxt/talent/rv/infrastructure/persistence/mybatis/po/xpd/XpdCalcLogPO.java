package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.rv.domain.RvBaseEntity;
import lombok.*;

import java.time.LocalDateTime;

/**
 * @Author: geyan
 * @Date: 10/12/24 10:07
 * @Description:
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_xpd_calc_log")
public class XpdCalcLogPO extends RvBaseEntity {
    /**
     * 机构id
     */
    private String orgId;
    /**
     * 计算类型
     */
    private Integer calcType;
    /**
     * 计算关联id
     */
    private String refId;
    /**
     * 执行计算批次号
     */
    private Integer batchNo;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 0:开始，1：完成，2：失败
     */
    private Integer calcStatus;
}
