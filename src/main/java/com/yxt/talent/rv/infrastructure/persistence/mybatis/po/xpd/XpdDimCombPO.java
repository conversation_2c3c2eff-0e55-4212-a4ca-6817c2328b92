package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import lombok.*;
import org.springframework.data.annotation.Transient;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 维度组合
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("rv_xpd_dim_comb")
public class XpdDimCombPO implements Serializable {
    /**
     * id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_COMB_ID)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    private String orgId;

    /**
     * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
     * 维度组整个机构全局共享（值全都是00000000-0000-0000-0000-000000000000），所以demo复制时，这里不涉及需要替换xpdId情况
     */
    private String xpdId;

    /**
     * 组合名称
     */
    @I18nTranslate(codeField = "combNameI18n")
    private String combName;

    /**
     * 组合名称国际化
     */
    private String combNameI18n;

    /**
     * x轴维度id
     */
    @JsonProperty("xSdDimId")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_DIM_ID)
    private String xSdDimId;

    @Transient
    @JsonProperty("xSdDimName")
    @TableField(exist = false)
    private String xSdDimName;

    /**
     * y轴维度id
     */
    @JsonProperty("ySdDimId")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_DIM_ID)
    private String ySdDimId;

    @Transient
    @JsonProperty("ySdDimName")
    @TableField(exist = false)
    private String ySdDimName;

    /**
     * 类型:0-内置,1-自建
     */
    private Integer combType;

    /**
     * 描述
     */
    private String combDesc;

    /**
     * 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 是否默认显示,0-否,1-是
     */
    @Transient
    @TableField(exist = false)
    private Integer showType;

    @Serial
    private static final long serialVersionUID = 1L;

}