package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.application.xpd.common.dto.RatioLevelThresholdDto;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点维度规则表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_dim_rule")
public class XpdDimRulePO implements Serializable {
    /**
     * 主键
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_RULE_ID)
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 新盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 上级维度规则id, 指向rv_xpd_dim_rule.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_RULE_ID, idMapSkipValues = "0")
    private String parentId;

    /**
     * 人才标准的维度id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_DIM_ID)
    private String sdDimId;

    /**
     * 计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算
     */
    private Integer calcType;

    /**
     * 权重, 当计算方式为<按子维度结果计算>有效
     */
    private BigDecimal weight;

    /**
     * 计算规则:0-快捷配置 1-高级公式
     */
    private Integer calcRule;

    /**
     * 计算规则表达式,当计算规则为<高级公式>时有效
     */
    private String formula;

    /**
     * 可视化的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaDisplay;

    /**
     * JSON格式的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaExpression;

    /**
     * JSON格式的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaExpCode;

    /**
     * 结果类型:0-分值 1-达标率,非<绩效维度>下有效
     */
    private Integer resultType;

    /**
     * 活动模型-活动ID（=rv_activity_arrange_item.ref_id 也等于 rv_activity_perf.id）
     */
    private String aomActId;

    /**
     * 分层方式:0-按比例 1-按固定值
     */
    private Integer levelType;

    /**
     * 分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效
     */
    private Integer levelPriority;

    /**
     * @see DimGridLevelRuleDTO
     * 分层规则,json数组, 涉及到的维度分层规则id取自rv_xpd_grid_level.id
     * List<DimGridLevelRuleDTO>
     */
    private String levelRule;

    /**
     * 按比例计算的阈值
     * @see RatioLevelThresholdDto
     */
    private String ruleThreshold;
    private Integer thresholdInvalid;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * 是否启用:0-未启用,1-启用
     */
    private Integer enabled;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}