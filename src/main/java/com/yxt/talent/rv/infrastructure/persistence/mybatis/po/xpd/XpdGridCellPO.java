package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.domain.RvBaseEntity;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 盘点宫格-格子配置表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_grid_cell")
public class XpdGridCellPO extends RvBaseEntity implements Serializable {

    /**
     * id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_CELL_ID)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    private String orgId;

    /**
     * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID, idMapSkipValues = "00000000-0000-0000-0000-000000000000")
    private String xpdId;

    /**
     * 宫格ID, 指向rv_xpd_grid.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String gridId;

    /**
     * 维度组合id, 指向rv_xpd_dim_comb.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_COMB_ID)
    private String dimCombId;

    /**
     * 格子名称
     */
    private String cellName;

    /**
     * 格子名称国际化code
     */
    private String cellNameI18n;

    /**
     * 格子颜色
     */
    private String cellColor;

    /**
     * 格子编号
     */
    private Integer cellIndex;

    /**
     * 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * x坐标, 不同的宫格类型对应不同的x轴坐标，与rv_xpd_grid_level.order_index对应
     */
    private Integer xIndex;

    /**
     * y坐标, 不同的宫格类型对应不同的x轴坐标，与rv_xpd_grid_level.order_index对应
     */
    private Integer yIndex;

    // 宫格描述
    private String cellDesc;

    @Serial
    private static final long serialVersionUID = 1L;
}