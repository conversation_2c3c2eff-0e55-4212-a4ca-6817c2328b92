package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 盘点宫格设置表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_grid")
public class XpdGridPO implements Serializable {
    /**
     * id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    private String orgId;

    /**
     * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID, idMapSkipValues = "00000000-0000-0000-0000-000000000000")
    private String xpdId;

    /**
     * 来源:0-模板,1-项目级
     */
    private Integer template;

    /**
     * 源宫格ID，宫格为<项目级>时有效，表示此宫格从哪个宫格模板复制而来
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String sourceGridId;

    /**
     * 宫格名称
     */
    private String gridName;

    /**
     * 宫格名称国际化code
     */
    private String gridNameI18n;

    /**
     * 宫格类型,0-四宫格,1-九宫格,2-十六宫格
     */
    private Integer gridType;

    /**
     * 配置方式,0-统一配置,1-按维度组合配置
     */
    private Integer configType;

    /**
     * 来源,0-内置,1-自建
     */
    private Integer sourceType;

    /**
     * 状态,0:未发布 1:已发布
     */
    private Integer gridState;

    /**
     * 宫格描述
     */
    private String gridDesc;

    /**
     * 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    @Serial
    private static final long serialVersionUID = 1L;
}