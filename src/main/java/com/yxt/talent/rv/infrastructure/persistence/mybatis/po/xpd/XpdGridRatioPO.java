package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点宫格落位比例
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_grid_ratio")
public class XpdGridRatioPO {
    /**
    * id
    */
    private String id;

    /**
    * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
    */
    private String orgId;

    /**
    * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID, idMapSkipValues = "00000000-0000-0000-0000-000000000000")
    private String xpdId;

    /**
    * 宫格ID, 指向rv_xpd_grid.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String gridId;

    /**
    * 维度组合id,指向rv_xpd_dim_comb.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_COMB_ID)
    private String dimCombId;

    /**
    * 宫格中格子配置表的id集合,半角分号拼接,指向rv_xpd_grid_cell.id
    */
    private String gridCellIds;

    /**
    * 宫格中格子的编号集合,半角分号拼接,指向rv_xpd_grid_cell.cell_index
    */
    private String gridCellIndex;

    /**
    * 预期人员占比
    */
    private BigDecimal ratio;

    /**
    * 排序
    */
    private Integer orderIndex;

    /**
    * 0:未删除 1:已删除
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人ID
    */
    private String createUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人ID
    */
    private String updateUserId;
}