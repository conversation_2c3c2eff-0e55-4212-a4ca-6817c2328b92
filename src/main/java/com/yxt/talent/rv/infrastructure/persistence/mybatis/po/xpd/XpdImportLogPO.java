package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 导入维度结果记录表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_import_log")
public class XpdImportLogPO implements Serializable {
    /**
    * 主键id
    */
    private String id;

    /**
    * 盘点项目表id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 导入数据记录id，rv_xpd_import.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_IMPT_ID)
    private String importId;

    /**
    * 导入时间
    */
    private LocalDateTime importTime;

    /**
    * 导入文件url或者fileId
    */
    private String importFile;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否删除(0-未删除, 1-已删除)
    */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}