package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 导入维度结果表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_import")
public class XpdImportPO implements Serializable {
    /**
    * 主键id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_IMPT_ID)
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 盘点项目表id, rv_xpd.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
    * 人才标准侧的维度ID
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_DIM_ID)
    private String sdDimId;

    /**
    * 导入类型(0-导入维度指标明细, 1-导入维度分层结果)
    */
    private Integer importType;

    /**
     * 指标总分
     */
    private BigDecimal scoreTotal;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否删除(0-未删除, 1-已删除)
    */
    private Integer deleted;

    @Serial
    private static final long serialVersionUID = 1L;
}