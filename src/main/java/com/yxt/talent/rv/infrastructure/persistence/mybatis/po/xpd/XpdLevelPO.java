package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点人才层级
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_level")
public class XpdLevelPO implements Serializable {
    /**
     * id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_LEVEL_ID)
    private String id;

    /**
     * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
     */
    private String orgId;

    /**
     * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID, idMapSkipValues = "00000000-0000-0000-0000-000000000000")
    private String xpdId;

    /**
     * 项目规则ID,机构模板存00000000-0000-0000-0000-000000000000。当xpd_id有具体值时有效(指向rv_xpd_rule.id,指的是盘点项目中设置的分层规则)
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RULE_ID, idMapSkipValues = "00000000-0000-0000-0000-000000000000")
    private String xpdRuleId;

    /**
     * 宫格ID, 指向rv_xpd_grid.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String gridId;

    /**
     * 人才层级名称
     */
    private String levelName;

    /**
     * 人才层级名称国际化code
     */
    private String levelNameI18n;

    /**
     * 分层值:比例或固定值。xpd_id和xpd_rule_id都有具体值时有效。结果类型为<得分>时表示分数,结果类型为<达标率>时表示达标率
     */
    private BigDecimal levelValue;

    /**
     * 是否胜任:0-不胜任 1-胜任
     */
    private Integer competent;

    /**
     * 层级icon地址
     */
    private String icon;

    /**
     * 计算规则表达式
     */
    private String formula;

    /**
     * 可视化的计算规则表达式,用于页面渲染
     */
    private String formulaDisplay;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 0:未删除 1:已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    @Serial
    private static final long serialVersionUID = 1L;

}