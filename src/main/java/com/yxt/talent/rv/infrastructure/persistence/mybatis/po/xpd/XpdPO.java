package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;

/**
 * 盘点项目表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd")
public class XpdPO {
    /**
    * 主键id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 项目ID, 指向rv_activity.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.AOM_ACTV_ID)
    private String aomPrjId;

    @Nullable
    @Transient
    @TableField(exist = false)
    private String actvRegId;

    @Nullable
    @Transient
    @TableField(exist = false)
    private Integer actvType;

    @Nullable
    @Transient
    @TableField(exist = false)
    private String categoryId;

    /**
    * 盘点项目名称：【注意】 需要自行通过rv_activity级联查询
    */
    @Nullable
    @Transient
    @TableField(exist = false)
    private String xpdName;

    /**
    * 活动状态(0-未保存, 1-未发布, 2-进行中, 3-已结束)
    */
    @Nullable
    @Transient
    @TableField(exist = false)
    private Integer actvStatus;

    /**
    * 模型id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_MODEL_ID)
    private String modelId;

    /**
    * 创建人id
    */
    private String createUserId;

    /**
    * 更新人id
    */
    private String updateUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否删除(0-未删除, 1-已删除)
    */
    private Integer deleted;
}