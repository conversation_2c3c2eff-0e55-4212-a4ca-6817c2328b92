package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.domain.RvBaseEntity;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点指标结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_result_indicator")
public class XpdResultIndicatorPO extends RvBaseEntity {
    /**
    * 主键id
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 盘点项目id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
    * 指标id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID)
    private String sdIndicatorId;

    /**
    * 总分
    */
    private BigDecimal scoreTotal;

    /**
    * 标准分
    */
    private BigDecimal scoreStandard;

    /**
    * 均分
    */
    private BigDecimal scoreAvg;

    /**
    * 达标率
    */
    private BigDecimal qualifiedPtg;

    /**
    * 是否删除:0-未删除,1-已删除
    */
    private Integer deleted;

    /**
    * 创建人
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}