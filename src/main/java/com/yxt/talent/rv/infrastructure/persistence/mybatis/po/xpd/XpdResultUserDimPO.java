package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.domain.RvBaseEntity;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Transient;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 盘点用户维度结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_result_user_dim")
public class XpdResultUserDimPO extends RvBaseEntity implements Serializable {
    /**
     * 主键id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RESULT_USER_DIM_ID)
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 用户id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    /**
     * 冗余的人才标准的维度id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_DIM_ID)
    private String sdDimId;

    /**
     * 宫格分层id, rv_xpd_grid_level.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_LEVEL_ID)
    private String gridLevelId;

    @Transient
    @TableField(exist = false)
    private String gridLevelName;

    @Transient
    @TableField(exist = false)
    private String gridLevelNameI18n;

    @Transient
    @TableField(exist = false)
    private Integer gridLevelOrderIndex;

    @Transient
    @TableField(exist = false)
    private String thirdDimColor;

    /**
     * 分值, 包括绩效得分
     */
    private BigDecimal scoreValue;

    /**
     * 达标率
     */
    private BigDecimal qualifiedPtg;

    /**
     * 冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效。指向 rv_activity_perf_result_conf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID)
    private String perfResultId;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的原始的计算出来的数据快照
     */
    private String originalSnap;

    @Serial
    private static final long serialVersionUID = 1L;

    public void buildSnapshot() {
        if (StringUtils.isNotBlank(this.originalSnap)) {
            return;
        }
        this.originalSnap = BeanHelper.bean2Json(this, JsonInclude.Include.ALWAYS);
    }
}