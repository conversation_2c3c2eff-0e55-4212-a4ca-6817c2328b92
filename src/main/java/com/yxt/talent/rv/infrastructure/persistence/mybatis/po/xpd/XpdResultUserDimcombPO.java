package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 盘点用户维度组结果
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("rv_xpd_result_user_dimcomb")
public class XpdResultUserDimcombPO {
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 用户id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    /**
     * 维度组id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_DIM_COMB_ID)
    private String dimCombId;

    /**
     * 现落位宫格编号,
     */
    private Integer cellIndex;

    /**
     * 现落位宫格id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_CELL_ID)
    private String cellId;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的数据快照
     */
    private String originalSnap;

    public void buildSnapshot() {
        if (StringUtils.isNotBlank(this.originalSnap)) {
            return;
        }
        this.originalSnap = BeanHelper.bean2Json(this, JsonInclude.Include.ALWAYS);
    }
}