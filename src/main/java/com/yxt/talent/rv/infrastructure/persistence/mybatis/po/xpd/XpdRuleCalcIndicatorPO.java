package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.application.xpd.common.dto.IndicatorBase;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点项目计算规则(按指标结果计算时)
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_rule_calc_indicator")
public class XpdRuleCalcIndicatorPO extends IndicatorBase {
    /**
    * 主键
    */
    private String id;

    /**
    * 机构id
    */
    private String orgId;

    /**
    * 新盘点项目id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
    * 项目规则ID(指向rv_xpd_rule.id)
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RULE_ID)
    private String xpdRuleId;

    /**
    * 对应人才标准里的模型指标关系表id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID)
    private String sdIndicatorId;

    /**
    * 数据来源:评鉴活动ID/导入活动ID/个人档案(id=0),json对象 List<XpdDimRuleCalcRefDto>
    */
    private String refIds;

    /**
    * 计算逻辑:0-无 1-求平均 2-求和 3-全部来源中达标 4-任一来源中达标
    */
    private Integer calcMethod;

    /**
    * 权重
    */
    private BigDecimal weight;

    /**
    * 排序
    */
    private Integer orderIndex;

    /**
    * 是否删除:0-未删除,1-已删除
    */
    private Integer deleted;

    /**
    * 创建人
    */
    private String createUserId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新人
    */
    private String updateUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}