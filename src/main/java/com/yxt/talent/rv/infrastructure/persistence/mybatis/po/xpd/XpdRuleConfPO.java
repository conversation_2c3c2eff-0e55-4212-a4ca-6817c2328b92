package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 盘点项目规则表
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_rule_conf")
public class XpdRuleConfPO implements Serializable {
    /**
     * 主键
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RULE_CONF_ID)
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 新盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 宫格模板id, 最初选的宫格模板, 指向rv_xpd_grid.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String templateGridId;

    /**
     * 宫格id, 指向rv_xpd_grid.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_GRID_ID)
    private String gridId;

    /**
     * 盘点结果类型:0-分值 1-达标率
     */
    private Integer resultType;

    /**
     * 分制:0-原始分值 1-五分制 2-十分制
     */
    private Integer scoreSystem = 0;

    /**
     * 快速生成规则时选择的配置json快照
     */
    private String snap;

    /**
     * 规则配置的版本号，主要用于乐观锁校验，防止同时修改规则配置导致的维度规则数据一致性问题
     */
    private Integer version;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    public Integer getScoreSystem() {
        return Optional.ofNullable(scoreSystem).orElse(0);
    }

    public void setScoreSystem(Integer scoreSystem) {
        this.scoreSystem = Optional.ofNullable(scoreSystem).orElse(0);
    }

}