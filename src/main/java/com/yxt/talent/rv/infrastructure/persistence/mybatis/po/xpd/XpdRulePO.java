package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 盘点项目规则表
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@TableName("rv_xpd_rule")
public class XpdRulePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RULE_ID)
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 新盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 计算方式:0-按维度结果计算 1-按指标结果计算
     */
    private Integer calcType;

    /**
     * 结果类型:0-维度分层结果 1-(维度/指标)得分 2-(维度/指标)达标率
     */
    private Integer resultType;

    /**
     * 计算规则:0-快捷配置 1-高级公式,当计算方式为<按指标结果计算>且结果类型为<指标得分>时有效
     */
    private Integer calcRule;

    /**
     * 计算规则表达式,当计算规则为<高级公式>时有效
     */
    private String formula;

    /**
     * 可视化的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaDisplay;

    /**
     * 分层方式:0-按比例 1-按固定值
     */
    private Integer levelType;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效
     */
    private Integer levelPriority;

    /**
     * JSON格式的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaExpression;

    /**
     * JSON格式的计算规则表达式（参数格式）,仅用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaExpCode;

    /**
     * 规则按比例计算时的阈值
     */
    private String ruleThreshold;

    /**
     * 规则按比例计算时的阈值是否无效
     */
    private Integer thresholdInvalid = 0;

    public void setThresholdInvalid(Integer thresholdInvalid) {
        if (thresholdInvalid == null) {
            thresholdInvalid = 0;
        }
        this.thresholdInvalid = thresholdInvalid;
    }
}