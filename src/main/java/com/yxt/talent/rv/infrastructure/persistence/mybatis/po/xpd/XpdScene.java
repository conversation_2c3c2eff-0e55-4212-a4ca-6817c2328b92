package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 盘点项目场景
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_scene")
public class XpdScene implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    private static final long serialVersionUID = 1L;
}