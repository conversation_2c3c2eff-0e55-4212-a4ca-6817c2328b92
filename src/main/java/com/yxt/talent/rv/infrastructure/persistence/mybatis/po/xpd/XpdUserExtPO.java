package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 新盘点项目人员扩展表（项目完整人员表以rv_activity_participation_member为准)
 * @TableName rv_xpd_user_ext
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName(value = "rv_xpd_user_ext")
public class XpdUserExtPO implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 人员id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

//    /**
//     * （废弃）指向活动人员表，rv_activity_participation_member.id
//     */
//    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
//    private Long actvMemberId;

    /**
     * 发展建议
     */
    private String suggestion;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除(0-未删除, 1-已删除)
     */
    private Integer deleted;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}