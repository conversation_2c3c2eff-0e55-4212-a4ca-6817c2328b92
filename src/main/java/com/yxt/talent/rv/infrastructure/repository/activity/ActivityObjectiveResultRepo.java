package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityObjectiveResultMapper;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@RequiredArgsConstructor
@Repository
public class ActivityObjectiveResultRepo {
    private final RvActivityObjectiveResultMapper rvActivityObjectiveResultMapper;

    public List<XpdIndicatorResultDto> queryByUserIds(
        String orgId,
        String actvId,
        List<String> userIds,
        List<String> objectiveIds) {
        if (StringUtils.isAnyEmpty(orgId, actvId)
            || CollectionUtils.isEmpty(userIds)
            || CollectionUtils.isEmpty(objectiveIds)) {
            return Lists.newArrayList();
        }
        return rvActivityObjectiveResultMapper.queryByUserIds(orgId, Lists.newArrayList(actvId), userIds, objectiveIds);
    }

    public List<XpdIndicatorResultDto> queryByActvIds(
        String orgId,
        Collection<String> userIds,
        Collection<String> actvIds,
        Collection<String> objectiveIds) {
        if (StringUtils.isAnyEmpty(orgId)
            || CollectionUtils.isEmpty(userIds)
            || CollectionUtils.isEmpty(actvIds)
            || CollectionUtils.isEmpty(objectiveIds)) {
            return Lists.newArrayList();
        }
        return rvActivityObjectiveResultMapper.queryByUserIds(orgId, actvIds, userIds, objectiveIds);
    }
}
