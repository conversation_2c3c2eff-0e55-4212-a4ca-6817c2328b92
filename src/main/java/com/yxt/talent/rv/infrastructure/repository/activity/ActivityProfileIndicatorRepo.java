package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class ActivityProfileIndicatorRepo {

    private final ActivityProfileIndicatorMapper activityProfileIndicatorMapper;

    public void batchInsert(List<ActivityProfileIndicatorPO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        activityProfileIndicatorMapper.batchInsert(list);
    }

    public void deleteByActvId(String orgId, String actProfileId, String optUserId) {
        activityProfileIndicatorMapper.deleteByActvId(orgId, actProfileId, optUserId);
    }

    public List<ActivityProfileIndicatorPO> findByActProfileId(String orgId, String actProfileId) {
        if (StringUtils.isBlank(actProfileId) || StringUtils.isBlank(orgId)) {
            return Collections.emptyList();
        }
        return activityProfileIndicatorMapper.findByActProfileId(orgId, actProfileId);
    }
}
