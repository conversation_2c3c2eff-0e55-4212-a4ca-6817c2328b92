package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.CommonRepository;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/6
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ActivityProfileRepo implements CommonRepository {
    private final ActivityProfileMapper activityProfileMapper;

    public void insertEntity(ActivityProfilePO entity) {
        if (Objects.isNull(entity)) {
            return;
        }
        activityProfileMapper.insert(entity);
    }

    public ActivityProfilePO findById(String orgId, String id) {
        return activityProfileMapper.findByIdAndOrgId(id, orgId);
    }

    public void updateEntity(ActivityProfilePO profilePO) {
        activityProfileMapper.updateByPrimaryKey(profilePO);
    }

    public void deleteById(String orgId, String actProfileId, String optUserId) {
        activityProfileMapper.deleteByPrimaryKey(orgId, actProfileId, optUserId);
    }
}
