package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.CommonRepository;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileResultDetailMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Repository
@RequiredArgsConstructor
public class ActivityProfileResultDetailRepo implements CommonRepository {
    private final ActivityProfileResultDetailMapper activityProfileResultDetailMapper;


    public List<ActivityProfileResultDetailPO> findByActProfileIdAndResultIds(String orgId, List<String> userResultIds,
            String actProfileId) {
        if (CollectionUtils.isEmpty(userResultIds) || StringUtils.isBlank(actProfileId)) {
            return Collections.emptyList();
        }
        return activityProfileResultDetailMapper.findByActProfileIdAndResultIds(orgId, userResultIds, actProfileId);
    }

    public void batchInsert(List<ActivityProfileResultDetailPO> profileResultDetailPOList) {
        if (CollectionUtils.isEmpty(profileResultDetailPOList)) {
            return;
        }
        activityProfileResultDetailMapper.insertBatch(profileResultDetailPOList);
    }

    public void deleteByIds(String orgId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(orgId)) {
            return;
        }
        activityProfileResultDetailMapper.deleteByIds(orgId, ids);
    }
}
