package com.yxt.talent.rv.infrastructure.repository.ai;

import com.yxt.CommonRepository;
import com.yxt.talent.rv.domain.ai.AiToolMessage;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai.AiToolMessageMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO;
import com.yxt.talent.rv.infrastructure.repository.ai.assembler.AiToolMessageAssembler;
import jakarta.validation.Valid;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;

@Slf4j
@Valid
@Repository
@RequiredArgsConstructor
class AiToolMessageRepo implements CommonRepository {

    private final AiToolMessageAssembler aiToolMessageAssembler;
    private final AiToolMessageMapper aiToolMessageMapper;

    public Collection<AiToolMessage> loadBySessionId(String orgId, String sessionId) {
        return aiToolMessageAssembler.toAiToolMessages(
            aiToolMessageMapper.selectByOrgIdAndSessionId(orgId, sessionId));
    }

    public Optional<AiToolMessage> load(@NonNull String orgId, @NonNull Long entityId) {
        AiToolMessagePO aiToolMessagePO = aiToolMessageMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(aiToolMessagePO).map(aiToolMessageAssembler::toAiToolMessage);
    }

    public void save(@NonNull AiToolMessage entity) {
        convertUpdate(entity, aiToolMessageAssembler::toAiToolMessagePO, aiToolMessageMapper::insertOrUpdate);
    }

    public void save(@NonNull Collection<AiToolMessage> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.debug("LOG12195:");
            return;
        }
        convertUpdateBatch(
            entities, aiToolMessageAssembler::toAiToolMessagePO,
            aiToolMessageMapper::insertOrUpdateBatch);
    }

    public void delete(@NonNull AiToolMessage entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }
}
