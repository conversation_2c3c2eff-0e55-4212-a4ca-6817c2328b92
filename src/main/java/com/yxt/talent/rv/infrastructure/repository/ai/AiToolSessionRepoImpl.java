package com.yxt.talent.rv.infrastructure.repository.ai;

import com.yxt.talent.rv.domain.ai.AiToolSession;
import com.yxt.talent.rv.domain.ai.AiToolSessionRepo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai.AiToolSessionMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolSessionPO;
import com.yxt.talent.rv.infrastructure.repository.ai.assembler.AiToolSessionAssembler;
import com.yxt.util.OptionalEmpty;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Slf4j
@Repository
@RequiredArgsConstructor
public class AiToolSessionRepoImpl implements AiToolSessionRepo {

    private final AiToolSessionAssembler aiToolSessionAssembler;
    private final AiToolSessionMapper aiToolSessionMapper;
    private final AiToolMessageRepo aiToolMessageRepo;

    @Override
    public Optional<AiToolSession> load(
        @NonNull String orgId, @NonNull Long entityId,
        @NonNull AiToolSession.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(session -> loadSub(session, loadConfig));
    }

    private AiToolSession loadSub(
        AiToolSession session, AiToolSession.@NonNull LoadConfig loadConfig) {
        if (loadConfig.loadMessages()) {
            session.addMessage(
                aiToolMessageRepo.loadBySessionId(session.getOrgId(), session.getSessionId()));
        }
        return session;
    }

    @Override
    public Optional<AiToolSession> load(@NonNull String orgId, @NonNull Long entityId) {
        AiToolSessionPO aiToolSessionPO = aiToolSessionMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(aiToolSessionPO).map(aiToolSessionAssembler::toAiToolSession);
    }

    @Override
    public void save(@NonNull AiToolSession entity) {
        convertUpdate(
            entity, aiToolSessionAssembler::toAiToolSessionPO, aiToolSessionMapper::insertOrUpdate);
        OptionalEmpty.of(entity.getMessages()).ifNotEmpty(aiToolMessageRepo::save);
    }

    @Override
    public void delete(@NonNull AiToolSession entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }

    @Override
    public Optional<AiToolSession> loadByOrgIdAndSessionId(
        @NonNull String orgId, @NonNull String sessionId, AiToolSession.LoadConfig loadConfig) {
        AiToolSessionPO aiToolSessionPO =
            aiToolSessionMapper.selectByOrgIdAndSessionId(orgId, sessionId);
        return Optional.ofNullable(aiToolSessionPO)
            .map(aiToolSessionAssembler::toAiToolSession)
            .map(session -> loadSub(session, loadConfig));
    }
}