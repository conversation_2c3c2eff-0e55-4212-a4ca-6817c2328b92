package com.yxt.talent.rv.infrastructure.repository.ai.assembler;

import com.yxt.talent.rv.domain.ai.AiToolMessage;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;


@Mapper(config = BaseAssemblerConfig.class)
public interface AiToolMessageAssembler {

    @Nullable
    AiToolMessagePO toAiToolMessagePO(@Nullable AiToolMessage entity);

    Collection<AiToolMessagePO> toAiToolMessagePO(Collection<AiToolMessage> entities);

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    AiToolMessage toAiToolMessage(AiToolMessagePO entityPO);

    Collection<AiToolMessage> toAiToolMessages(Collection<AiToolMessagePO> entityPos);

}