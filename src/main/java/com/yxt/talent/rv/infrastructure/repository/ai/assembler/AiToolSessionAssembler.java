package com.yxt.talent.rv.infrastructure.repository.ai.assembler;

import com.yxt.talent.rv.domain.ai.AiToolSession;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolSessionPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseAssemblerConfig.class)
public interface AiToolSessionAssembler {

    @Nullable
    AiToolSessionPO toAiToolSessionPO(@Nullable AiToolSession entity);

    @Nullable
    @Mapping(target = "delete", ignore = true)
    @Mapping(target = "messages", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    AiToolSession toAiToolSession(AiToolSessionPO entityPO);

}