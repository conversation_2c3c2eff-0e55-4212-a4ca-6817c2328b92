package com.yxt.talent.rv.infrastructure.repository.aom;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.CommonRepository;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/11
 */
@RequiredArgsConstructor
@Repository
public class RvActivityParticipationMemberRepo implements CommonRepository {
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;

    public long findTotalUserCount(String orgId, String actId, Long participationId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actId)) {
            return 0L;
        }
        return rvActivityParticipationMemberMapper.findTotalUserCount(orgId, actId, participationId);
    }

    public List<String> findAllUserIdByActId(String orgId, String actId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(actId)) {
            return new ArrayList<>();
        }
        return rvActivityParticipationMemberMapper.findAllUserIdByActId(orgId, actId);
    }

    public IPage<RvActivityParticipationMemberPO> pageUserByPartId(Page<RvActivityParticipationMemberPO> pageable, String orgId,
        String actId, Long pardId) {
        return rvActivityParticipationMemberMapper.pageUserByPartId(pageable, orgId, actId, pardId);
    }

    public long countByActvId(String orgId, String actvId, String actId){
        return rvActivityParticipationMemberMapper.countByActvId(orgId, actvId, actId);
    }
}
