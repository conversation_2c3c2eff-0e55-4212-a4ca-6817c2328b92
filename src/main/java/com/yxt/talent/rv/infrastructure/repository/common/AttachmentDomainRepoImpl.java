package com.yxt.talent.rv.infrastructure.repository.common;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.common.Attachment;
import com.yxt.talent.rv.domain.common.AttachmentDomainRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.AttachmentMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO;
import com.yxt.talent.rv.infrastructure.repository.common.assembler.AttachmentAssembler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class AttachmentDomainRepoImpl implements AttachmentDomainRepo {

    private final AttachmentMapper attachmentMapper;
    private final AttachmentAssembler attachmentAssembler;

    @Override
    public Optional<Attachment> load(
            @NonNull String orgId, @NonNull String entityId,
            @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<Attachment> load(
            @NonNull String orgId, @NonNull String entityId) {
        AttachmentPO attachmentPo = attachmentMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(attachmentPo).map(attachmentAssembler::toAttachment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@NonNull Attachment entity) {
        AttachmentPO attachmentPo = attachmentAssembler.toAttachmentPo(entity);
        Optional.ofNullable(attachmentPo).ifPresent(attachmentMapper::insertOrUpdate);
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull Attachment entity) {
        String orgId = entity.getOrgId();
        attachmentMapper.deleteById(orgId, entity.getId());
    }
}
