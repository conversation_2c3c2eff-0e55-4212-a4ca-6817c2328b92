package com.yxt.talent.rv.infrastructure.repository.common;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.common.Dict;
import com.yxt.talent.rv.domain.common.DictDomainRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.DictMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO;
import com.yxt.talent.rv.infrastructure.repository.common.assembler.DictAssembler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class DictDomainRepoImpl implements DictDomainRepo {

    private final DictMapper dictMapper;
    private final DictAssembler dictAssembler;

    @Override
    public Optional<Dict> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<Dict> load(
            @NonNull String orgId, @NonNull String entityId) {
        DictPO dictPo = dictMapper.selectById(entityId);
        return Optional.ofNullable(dictPo).map(dictAssembler::toDict);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@NonNull Dict entity) {
        DictPO dictPo = dictAssembler.toDictPo(entity);
        Optional.ofNullable(dictPo).ifPresent(dictMapper::insertOrUpdate);
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull Dict entity) {
        dictMapper.deleteById(entity.getId());
    }

}
