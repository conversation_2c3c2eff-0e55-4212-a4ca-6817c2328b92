package com.yxt.talent.rv.infrastructure.repository.common.assembler;

import com.yxt.talent.rv.domain.common.Attachment;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseAssemblerConfig.class)
public interface AttachmentAssembler {

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    Attachment toAttachment(AttachmentPO attachmentPo);

    @Nullable
    AttachmentPO toAttachmentPo(Attachment attachment);
}
