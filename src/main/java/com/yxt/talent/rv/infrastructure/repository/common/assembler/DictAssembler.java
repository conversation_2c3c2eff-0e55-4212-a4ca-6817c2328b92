package com.yxt.talent.rv.infrastructure.repository.common.assembler;

import com.yxt.talent.rv.domain.common.Dict;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;

@Mapper(config = BaseAssemblerConfig.class)
public interface DictAssembler {

    @Nullable
    @Mapping(target = "pendingEvents", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    Dict toDict(DictPO dictPo);

    @Nullable
    Collection<Dict> toDicts(Collection<DictPO> dictPos);

    @Nullable
    DictPO toDictPo(Dict dict);

    @Nullable
    Collection<DictPO> toDictPos(Collection<Dict> dicts);

}
