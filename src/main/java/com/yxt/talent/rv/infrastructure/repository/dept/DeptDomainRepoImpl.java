package com.yxt.talent.rv.infrastructure.repository.dept;

import com.yxt.talent.rv.domain.dept.Dept;
import com.yxt.talent.rv.domain.dept.DeptDomainRepo;
import com.yxt.talent.rv.domain.dept.entity.UdpDept;
import com.yxt.talent.rv.infrastructure.common.exception.UdpUnsupportedOperationException;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import com.yxt.talent.rv.infrastructure.repository.dept.assembler.DeptAssembler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.escapeSql;

@Slf4j
@Repository
@RequiredArgsConstructor
public class DeptDomainRepoImpl implements DeptDomainRepo {

    private final UdpDeptMapper udpDeptMapper;
    private final DeptAssembler deptAssembler;

    @Override
    @jakarta.annotation.Nonnull
    public Optional<Dept> load(
        @NonNull String orgId, @NonNull String entityId, @NonNull Dept.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(dept -> {
            if (loadConfig.loadDescendants()) {
                loadDescendants(orgId, dept).ifPresent(dept::addDescendants);
            }
            return dept;
        });
    }

    @Override
    @jakarta.annotation.Nonnull
    public Optional<Dept> load(@NonNull String orgId, @NonNull String entityId) {
        UdpDeptPO udpDeptPo = udpDeptMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(udpDeptPo).map(deptAssembler::toUdpDept).map(Dept::new);
    }

    private Optional<Collection<Dept>> loadDescendants(String orgId, Dept dept) {
        String routingPath = dept.getRoutingPath();
        if (StringUtils.isBlank(routingPath)) {
            return Optional.empty();
        }
        Collection<UdpDeptPO> udpDeptPos =
            udpDeptMapper.selectByRoutingPath(orgId, escapeSql(routingPath));
        Collection<UdpDept> udpDepts = deptAssembler.toUdpDepts(udpDeptPos);
        return Optional.ofNullable(udpDepts)
            .map(coll -> coll.stream().map(Dept::new).collect(Collectors.toList()))
            .map(depts -> {
                depts.remove(dept);
                return depts;
            });
    }

    private Optional<List<Dept>> loadChildren(@NonNull String orgId, @NonNull String entityId) {
        Collection<UdpDeptPO> subUdpDeptPos = udpDeptMapper.selectByParentId(orgId, entityId);
        Collection<UdpDept> udpDepts = deptAssembler.toUdpDepts(subUdpDeptPos);
        return Optional.ofNullable(udpDepts)
            .map(coll -> coll.stream().map(Dept::new).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(@NonNull Dept entity) {
        throw new UdpUnsupportedOperationException();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull Dept entity) {
        throw new UdpUnsupportedOperationException();
    }
}
