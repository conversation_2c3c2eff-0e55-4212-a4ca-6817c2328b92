package com.yxt.talent.rv.infrastructure.repository.dept.assembler;

import com.yxt.talent.rv.domain.dept.entity.UdpDept;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;

import java.util.Collection;

@Mapper(config = BaseAssemblerConfig.class)
public interface DeptAssembler {

    @Nullable
    UdpDept toUdpDept(@Nullable UdpDeptPO udpDeptPo);

    @Nullable
    Collection<UdpDept> toUdpDepts(Collection<UdpDeptPO> udpDeptPos);

}
