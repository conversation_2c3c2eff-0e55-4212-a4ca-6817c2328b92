package com.yxt.talent.rv.infrastructure.repository.perf;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.perf.Perf;
import com.yxt.talent.rv.domain.perf.repo.PerfDomainRepo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.repository.perf.assembler.PerfAssembler;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PerfDomainRepoImpl implements PerfDomainRepo {

    private final PerfAssembler perfAssembler;
    private final PerfMapper perfMapper;

    @Override
    public Optional<Perf> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<Perf> load(@NonNull String orgId, @NonNull String entityId) {
        PerfPO perfPO = perfMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(perfPO).map(perfAssembler::toPerf);
    }

    @Override
    public void save(@NonNull Perf entity) {
        deleteConvertUpdate(entity.getOrgId(), entity, perfAssembler::toPerfPO,
                perfMapper::deleteByOrgIdAndId, perfMapper::insertOrUpdate);
    }

    @Override
    public void save(@Nonnull Collection<Perf> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.debug("LOG14025:");
            return;
        }
        String orgId = entities.iterator().next().getOrgId();

        deleteConvertUpdateBatch(orgId, entities, perfAssembler::toPerfPOs, perfMapper::deleteBatch,
                perfMapper::insertOrUpdateBatch);
    }

    @Override
    public Collection<Perf> loadByUserIdsAndPeriodIds(
            String orgId, Collection<String> userIds, List<String> perfPeriodIds) {
        List<PerfPO> perfPOs = perfMapper.selectByUserIdsAndPeriodIds(orgId, userIds, perfPeriodIds);
        return Objects.requireNonNull(perfAssembler.toPerfs(perfPOs));
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    @Override
    public void delete(@NonNull Perf entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }
}
