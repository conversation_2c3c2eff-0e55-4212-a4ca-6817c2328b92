package com.yxt.talent.rv.infrastructure.repository.perf;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.perf.PerfGrade;
import com.yxt.talent.rv.domain.perf.repo.PerfGradeDomainRepo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.repository.perf.assembler.PerfAssembler;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Objects;
import java.util.Optional;


@Repository
@RequiredArgsConstructor
public class PerfGradeDomainRepoImpl implements PerfGradeDomainRepo {

    private final PerfGradeMapper perfGradeMapper;
    private final PerfAssembler perfAssembler;

    @Nonnull
    @Override
    public Collection<PerfGrade> load(String orgId) {
        Collection<PerfGradePO> entities = perfGradeMapper.selectByOrgId(orgId);
        return Objects.requireNonNull(perfAssembler.toPerfGrades(entities));
    }

    @Override
    public Optional<PerfGrade> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<PerfGrade> load(@NonNull String orgId, @NonNull String entityId) {
        PerfGradePO perfGradePO = perfGradeMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(perfAssembler.toPerfGrade(perfGradePO));
    }

    @Override
    public void save(@NonNull PerfGrade entity) {
        convertUpdate(entity, perfAssembler::toPerfGradePO, perfGradeMapper::insertOrUpdate);
    }

    @Override
    public void save(@NonNull String orgId, @Nullable Collection<PerfGrade> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        entities.forEach(this::save);
    }

    @Override
    public Collection<PerfGrade> loadAllIncludeDeleted(String orgId) {
        Collection<PerfGradePO> entities = perfGradeMapper.selectByOrgIdIncludeDeleted(orgId);
        return Objects.requireNonNull(perfAssembler.toPerfGrades(entities));
    }

    /**
     * 假删
     */
    @Override
    public void delete(@NonNull PerfGrade entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }
}
