package com.yxt.talent.rv.infrastructure.repository.perf;

import com.yxt.EmptyLoadConfig;
import com.yxt.talent.rv.domain.perf.PerfPeriod;
import com.yxt.talent.rv.domain.perf.repo.PerfPeriodDomainRepo;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.repository.perf.assembler.PerfAssembler;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;

@Repository
@RequiredArgsConstructor
public class PerfPeriodDomainRepoImpl implements PerfPeriodDomainRepo {

    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfAssembler perfAssembler;

    @Override
    public Optional<PerfPeriod> load(
            @NonNull String orgId, @NonNull String entityId, @NonNull EmptyLoadConfig loadConfig) {
        return this.load(orgId, entityId);
    }

    @Override
    public Optional<PerfPeriod> load(@NonNull String orgId, @NonNull String entityId) {
        PerfPeriodPO perfPeriodPO = perfPeriodMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(perfAssembler.toPerfPeriod(perfPeriodPO));
    }

    @Override
    public Collection<PerfPeriod> load(@NonNull String orgId) {
        List<PerfPeriodPO> perfPeriodPOs = perfPeriodMapper.selectByOrgId(orgId);
        return Optional.ofNullable(perfPeriodPOs).map(perfAssembler::toPerfPeriods)
                .orElse(Collections.emptyList());
    }

    @Override
    public void save(@NonNull PerfPeriod entity) {
        Optional.ofNullable(perfAssembler.toPerfPeriodPO(entity))
                .ifPresent(perfPeriodMapper::insertOrUpdate);
    }

    @Override
    public void save(@Nonnull Collection<PerfPeriod> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            return;
        }

        convertUpdateBatch(entities, perfAssembler::toPerfPeriodPOs, perfPeriodMapper::insertOrUpdateBatch);
    }

    @Override
    public Collection<PerfPeriod> loadByIdsIncludeDeleted(@javax.annotation.Nonnull String orgId, List<String> periodIds) {
        if (CollectionUtils.isEmpty(periodIds)) {
            return Collections.emptyList();
        }
        List<PerfPeriodPO> perfPeriodPOs = perfPeriodMapper.selectByOrgIdAndIdsIncludeDeleted(orgId, periodIds);
        return Optional.ofNullable(perfPeriodPOs).map(perfAssembler::toPerfPeriods)
                .orElse(Collections.emptyList());
    }

    @Override
    public void delete(@NonNull PerfPeriod entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }
}
