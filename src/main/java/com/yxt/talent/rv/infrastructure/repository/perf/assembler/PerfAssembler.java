package com.yxt.talent.rv.infrastructure.repository.perf.assembler;

import com.yxt.talent.rv.domain.perf.Perf;
import com.yxt.talent.rv.domain.perf.PerfGrade;
import com.yxt.talent.rv.domain.perf.PerfPeriod;
import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;

@Mapper(config = BaseAssemblerConfig.class)
public interface PerfAssembler {

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "perfPeriodId", source = "periodId")
    @Mapping(target = "perfGradeValue", source = "periodLevel")
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    Perf toPerf(@Nullable PerfPO perfPO);

    @Nullable
    @Mapping(source = "perfPeriodId", target = "periodId")
    @Mapping(source = "perfGradeValue", target = "periodLevel")
    PerfPO toPerfPO(Perf perf);

    @Nullable
    Collection<Perf> toPerfs(Collection<PerfPO> perfPOs);

    @Nullable
    Collection<PerfPO> toPerfPOs(Collection<Perf> perfs);

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    PerfGrade toPerfGrade(@Nullable PerfGradePO perfGradePO);

    @Nullable
    PerfGradePO toPerfGradePO(PerfGrade perfGrade);

    @Nullable
    Collection<PerfGrade> toPerfGrades(Collection<PerfGradePO> perfGradePOs);

    @Nullable
    Collection<PerfGradePO> toPerfGradePOs(Collection<PerfGrade> perfGrades);

    @Nullable
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "newInstance", ignore = true)
    @Mapping(target = "pendingEvents", ignore = true)
    PerfPeriod toPerfPeriod(@Nullable PerfPeriodPO perfPeriodPO);

    @Nullable
    PerfPeriodPO toPerfPeriodPO(PerfPeriod perfPeriod);

    @Nullable
    Collection<PerfPeriod> toPerfPeriods(Collection<PerfPeriodPO> perfPeriodPOs);

    @Nullable
    Collection<PerfPeriodPO> toPerfPeriodPOs(Collection<PerfPeriod> perfPeriods);

}
