package com.yxt.talent.rv.infrastructure.repository.xpd;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: geyan
 * @Date: 11/12/24 17:02
 * @Description:
 **/
@Data
public class DecimalPtgBean {
    private String id;
    private String userId;
    private BigDecimal sortValue;
    private boolean allocated;
    private String levelId;
    /**
     * 是否胜任:0-不胜任 1-胜任
     */
    private Integer competent;
}
