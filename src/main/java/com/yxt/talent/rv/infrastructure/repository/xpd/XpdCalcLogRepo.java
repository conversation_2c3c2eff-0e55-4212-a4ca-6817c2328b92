package com.yxt.talent.rv.infrastructure.repository.xpd;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.common.enums.YesOrNo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdCalcLogMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdCalcLogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * @Author: geyan
 * @Date: 10/12/24 10:16
 * @Description:
 **/
@Slf4j
@RequiredArgsConstructor
@Repository
public class XpdCalcLogRepo extends ServiceImpl<XpdCalcLogMapper, XpdCalcLogPO> {

    public XpdCalcBatchDTO newCalcBatch(String orgId, int calcType, String refId) {
        int batchNo = lastBatchNo(calcType, refId) + 1;
        XpdCalcLogPO logPO = new XpdCalcLogPO();
        logPO.initData(null);
        logPO.setOrgId(orgId);
        logPO.setCalcType(calcType);
        logPO.setRefId(refId);
        logPO.setBatchNo(batchNo);
        logPO.setStartTime(LocalDateTime.now());
        logPO.setCalcStatus(YesOrNo.NO.getValue());
        baseMapper.insert(logPO);
        XpdCalcBatchDTO ret = new XpdCalcBatchDTO();
        ret.setId(logPO.getId());
        ret.setBatchNo(logPO.getBatchNo());
        return ret;
    }

    public void endCalcLog(String id, boolean success) {
        XpdCalcLogPO logPO = new XpdCalcLogPO();
        logPO.setId(id);
        logPO.setEndTime(LocalDateTime.now());
        logPO.setCalcStatus(success ? YesOrNo.YES.getValue() : 2);
        updateById(logPO);
    }

    private int lastBatchNo(int calcType, String refId) {
        Integer batchNo = baseMapper.lastBatchNo(calcType, refId);
        if (batchNo == null) {
            batchNo = 1;
        }
        return batchNo;
    }

    public XpdCalcLogPO findLatestByProfId(String orgId, int calcType, String refId) {
        return baseMapper.queryRefIdLatestCal(orgId, calcType, refId);
    }
}
