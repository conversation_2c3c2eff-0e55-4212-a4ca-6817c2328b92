package com.yxt.talent.rv.infrastructure.service.aibox;

import com.yxt.talent.rv.infrastructure.service.aibox.dto.LatestChatResponse;

import static com.yxt.common.util.BeanHelper.json2Bean;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.null2Blank;

/**
 * AiBox聊天解析器
 */
public class AiBoxChatPaser {

    public LatestChatResponse parseEventStream(String eventStream) {
        return json2Bean(eventStream, LatestChatResponse.class);
    }

    public String parseChatContent(LatestChatResponse latestChatResponse) {
        if (latestChatResponse != null && latestChatResponse.getChoices() != null && !latestChatResponse.getChoices().isEmpty()) {
            LatestChatResponse.Choice choice = latestChatResponse.getChoices().get(0);
            if (choice != null && choice.getDelta() != null) {
                return (String) null2Blank(choice.getDelta().getContent());
            }
        }
        return "";
    }

}
