package com.yxt.talent.rv.infrastructure.service.aibox;

import com.yxt.talent.rv.infrastructure.service.aibox.dto.ChatRequest;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.EventSourceListener;

import java.util.Collection;
import java.util.Map;

public interface AiboxClient {

    /**
     * 问答接口
     *
     * @param request
     * @param customHeaders
     * @param listener
     */
    void streamChat(
        ChatRequest request, Map<String, String> customHeaders, EventSourceListener listener);

    /**
     * 生成标题
     *
     * @param request 请求参数
     * @param customHeaders 自定义请求头
     * @return 生成的标题
     */
    String generateTitle(ChatRequest request, Map<String, String> customHeaders);

    /**
     * 获取问题推荐列表
     *
     * @return 推荐问题列表
     */
    Collection<String> getRecommendQuestions(ChatRequest chatRequest);

}
