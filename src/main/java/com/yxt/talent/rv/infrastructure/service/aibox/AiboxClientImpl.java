package com.yxt.talent.rv.infrastructure.service.aibox;

import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.SignedHeader;
import com.yxt.common.util.HttpUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.ChatRequest;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.EventSourceListener;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.LatestChatResponse;
import com.yxt.talent.rv.infrastructure.service.aibox.handler.StreamResponseHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.*;

import static com.yxt.common.util.BeanHelper.json2Bean;

@Slf4j
@Service
@RequiredArgsConstructor
public class AiboxClientImpl implements AiboxClient {

    /** 问答接口【智能助手类型】 */
    public static final String AIBOX_STREAM_ASSISTANT_ASK = "/stream/outer/assistant/post/ask";
    /** 设置全局变量接口 */
    public static final String AIBOX_SET_CONSTANTS = "/stream/assistantQa/out/post/setConstantsValue";

    private final WebClient webClient;
    private final AppProperties appProperties;
    private final StreamResponseHandler streamResponseHandler;
    private final AiBoxChatPaser aiBoxChatPaser = new AiBoxChatPaser();

    /**
     * 调用问答接口
     * @param chatRequest
     * @param customHeaders
     * @param listener
     */
    @Override
    public void streamChat(
        ChatRequest chatRequest, Map<String, String> customHeaders, EventSourceListener listener) {
        try {
            webClient.post().uri(buildRequestUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(headers -> addHeaders(headers, customHeaders))
                .bodyValue(chatRequest)
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(error -> handleStreamError(error, listener))
                .subscribe(
                    data -> streamResponseHandler.handle(data, chatRequest, customHeaders, listener),
                    error -> handleError(error, listener),
                    listener::onComplete
                );
        } catch (Exception e) {
            log.error("LOG10122:创建事件流失败", e);
            listener.onError(e);
        }
    }

    /**
     * 调用接口生成标题
     * @param chatRequest 请求参数
     * @param customHeaders 自定义请求头
     * @return 生成的标题
     */
    @Override
    public String generateTitle(ChatRequest chatRequest, Map<String, String> customHeaders) {
        try {
            // 第一次调用，获取初始化信息
            List<String> initResponses = callAiBoxApi(chatRequest, customHeaders);
            extractInitInfo(initResponses, chatRequest);
            
            // 第二次调用，获取实际内容
            List<String> responses = callAiBoxApi(chatRequest, customHeaders);
            return findContentFromResponses(responses);
        } catch (Exception e) {
            log.error("LOG10282:生成标题失败", e);
            throw new ApiException("生成标题失败", e);
        }
    }

    /**
     * 获取问题推荐列表
     * @return 推荐问题列表
     */
    @Override
    public Collection<String> getRecommendQuestions(ChatRequest chatRequest) {
        try {
            // 第一次调用，获取初始化信息
            List<String> initResponses = callAiBoxApi(chatRequest, new HashMap<>());
            extractInitInfo(initResponses, chatRequest);
            
            // 第二次调用，获取实际的推荐问题
            List<String> responses = callAiBoxApi(chatRequest, new HashMap<>());
            String content = findContentFromResponses(responses);
            return content != null ? json2Bean(content, List.class, String.class) : new ArrayList<>();
        } catch (Exception e) {
            log.error("LOG10292:获取问题推荐列表失败", e);
            throw new ApiException("获取问题推荐列表失败", e);
        }
    }

    /**
     * 调用AiBox API并获取响应列表
     */
    private List<String> callAiBoxApi(ChatRequest request, Map<String, String> customHeaders) {
        return webClient.post()
            .uri(appProperties.getAibox().getBaseUrl() + AIBOX_STREAM_ASSISTANT_ASK)
            .contentType(MediaType.APPLICATION_JSON)
            .headers(headers -> addHeaders(headers, customHeaders))
            .bodyValue(request)
            .retrieve()
            .bodyToFlux(String.class)
            .collectList()
            .block();
    }

    /**
     * 从响应列表中查找包含content的响应内容
     */
    private String findContentFromResponses(List<String> responses) {
        if (responses == null || responses.isEmpty()) {
            return null;
        }

        for (String response : responses) {
            LatestChatResponse chatResponse = aiBoxChatPaser.parseEventStream(response);
            if (chatResponse != null && chatResponse.getChoices() != null && 
                !chatResponse.getChoices().isEmpty() && 
                chatResponse.getChoices().get(0).getDelta() != null && 
                chatResponse.getChoices().get(0).getDelta().getContent() != null) {
                
                return chatResponse.getChoices().get(0).getDelta().getContent();
            }
        }
        return null;
    }

    /**
     * 从响应中提取初始化信息
     */
    private void extractInitInfo(List<String> responses, ChatRequest chatRequest) {
        if (responses == null || responses.isEmpty()) {
            return;
        }

        for (String response : responses) {
            LatestChatResponse chatResponse = aiBoxChatPaser.parseEventStream(response);
            if (chatResponse != null) {
                // 提取recordId
                if (chatResponse.getRecordId() != null) {
                    chatRequest.setRecordId(chatResponse.getRecordId());
                }
                // 从中断信号中提取新的nodeId
                if ("AI_532".equals(chatResponse.getCode()) && chatResponse.getData() != null) {
                    chatRequest.setCurrNodeInstanceId(chatResponse.getData());
                    break;  // 找到中断信号后就可以结束了
                }
            }
        }
    }

    private String buildRequestUrl() {
        return appProperties.getAibox().getBaseUrl() + AIBOX_STREAM_ASSISTANT_ASK;
    }

    private SignedHeader buildSignedHeader() {
        return HttpUtil.getSignedHeader(appProperties.getAibox().getAk(),
            appProperties.getAibox().getSk());
    }

    private void addHeaders(HttpHeaders headers, Map<String, String> customHeaders) {
        SignedHeader signedHeader = buildSignedHeader();
        headers.add("appkey", signedHeader.getAppkey());
        headers.add("nonce", signedHeader.getNonce());
        headers.add("timestamp", signedHeader.getTimestamp());
        headers.add("signature", signedHeader.getSignature());
        // 添加自定义请求头
        customHeaders.forEach(headers::add);
    }

    private Flux<String> handleStreamError(Throwable throwable, EventSourceListener listener) {
        log.error("LOG10132:事件流失败", throwable);
        listener.onError(throwable);
        return Flux.empty();
    }

    private void handleError(Throwable error, EventSourceListener listener) {
        log.error("事件流失败", error);
        listener.onError(error);
    }
}
