package com.yxt.talent.rv.infrastructure.service.aibox;

import com.yxt.talent.rv.infrastructure.service.aibox.handler.DefaultStreamResponseHandler;
import com.yxt.talent.rv.infrastructure.service.aibox.handler.PassThroughStreamResponseHandler;
import com.yxt.talent.rv.infrastructure.service.aibox.handler.StreamResponseHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiboxConfig {

    @Bean
    @ConditionalOnProperty(name = "sprv.aibox.stream.handler", havingValue = "passthrough", matchIfMissing = true)
    public StreamResponseHandler passThroughStreamResponseHandler() {
        return new PassThroughStreamResponseHandler();
    }

    @Bean
    @ConditionalOnProperty(name = "sprv.aibox.stream.handler", havingValue = "default")
    public StreamResponseHandler defaultStreamResponseHandler(AiboxClient aiboxClient) {
        return new DefaultStreamResponseHandler(aiboxClient);
    }
}