package com.yxt.talent.rv.infrastructure.service.aibox.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ChatRequest {

    @Schema(description = "账号")
    private String accountNo;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "平台，目前默认是aibox")
    private String platform = "aibox";

    @Schema(description = "当前执行到的组件节点实例ID")
    private String currNodeInstanceId;

    @Schema(description = "会话ID")
    private String recordId;

    @Schema(description = "流程状态")
    private String processStatus;

    @Schema(description = "用户输入")
    private String modelUserContent;

    @Schema(description = "常量值字符串")
    private String varList;
}
