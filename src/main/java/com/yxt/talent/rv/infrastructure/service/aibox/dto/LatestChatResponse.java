package com.yxt.talent.rv.infrastructure.service.aibox.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * AI对话响应实体
 */
@Data
public class LatestChatResponse {
    /**
     * 消息ID
     */
    private String id;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 选择结果列表
     */
    private List<Choice> choices;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 响应数据
     */
    private String data;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 记录ID
     */
    private String recordId;

    public boolean isEnd() {
        return this.code != null && this.code.equals("AI_200");
    }

    @Data
    public static class Choice {
        /**
         * 索引
         */
        private Integer index;

        /**
         * 增量内容
         */
        private Delta delta;

        /**
         * 结束原因
         */
        @JsonProperty("finish_reason")
        private String finishReason;

        /**
         * 判断是否结束
         */
        public boolean isFinished() {
            return "stop".equals(finishReason);
        }
    }

    @Data
    public static class Delta {

        /**
         * 内容
         */
        private String content;

        /**
         * 角色: user, assistant
         */
        private String role;
    }
}