package com.yxt.talent.rv.infrastructure.service.aibox.handler;

import com.yxt.talent.rv.infrastructure.service.aibox.AiBoxChatPaser;
import com.yxt.talent.rv.infrastructure.service.aibox.AiboxClient;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.ChatRequest;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.EventSourceListener;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.LatestChatResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class DefaultStreamResponseHandler implements StreamResponseHandler {

    private final AiboxClient aiboxClient;
    private final AiBoxChatPaser aiBoxChatPaser = new AiBoxChatPaser();

    @Override
    public void handle(String data, ChatRequest chatRequest, Map<String, String> customHeaders, EventSourceListener listener) {
        try {
            LatestChatResponse response = aiBoxChatPaser.parseEventStream(data);

            if (isInterruptSignal(response)) {
                handleInterruptSignal(response, chatRequest, customHeaders, listener);
                return;
            }

            if (hasMessageContent(response)) {
                sendMessageContent(response, listener);
            }

            if (isConversationFinished(response)) {
                listener.onComplete();
            }

        } catch (Exception e) {
            log.error("处理事件流失败", e);
            listener.onError(e);
        }
    }

    private boolean isInterruptSignal(LatestChatResponse response) {
        return "AI_532".equals(response.getCode());
    }

    private void handleInterruptSignal(
        LatestChatResponse response, ChatRequest chatRequest,
        Map<String, String> customHeaders, EventSourceListener listener) {
        String newNodeId = response.getData();
        chatRequest.setCurrNodeInstanceId(newNodeId);
        aiboxClient.streamChat(chatRequest, customHeaders, listener);
    }

    private boolean hasMessageContent(LatestChatResponse response) {
        return response.getChoices() != null && !response.getChoices().isEmpty() &&
               response.getChoices().get(0).getDelta() != null &&
               response.getChoices().get(0).getDelta().getContent() != null;
    }

    private boolean isConversationFinished(LatestChatResponse response) {
        return response.getChoices() != null && !response.getChoices().isEmpty() &&
               response.getChoices().get(0).isFinished();
    }

    private void sendMessageContent(LatestChatResponse response, EventSourceListener listener) {
        String content = response.getChoices().get(0).getDelta().getContent();
        if (content != null && !content.isEmpty()) {
            listener.onMessage(content);
        }
    }
}