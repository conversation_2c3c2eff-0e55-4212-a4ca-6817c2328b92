package com.yxt.talent.rv.infrastructure.service.aibox.handler;

import com.yxt.talent.rv.infrastructure.service.aibox.dto.ChatRequest;
import com.yxt.talent.rv.infrastructure.service.aibox.dto.EventSourceListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class PassThroughStreamResponseHandler implements StreamResponseHandler {

    @Override
    public void handle(
        String data, ChatRequest chatRequest, Map<String, String> customHeaders,
        EventSourceListener listener) {
        try {
            listener.onMessage(data);
        } catch (Exception e) {
            log.error("处理事件流失败", e);
            listener.onError(e);
        }
    }
} 