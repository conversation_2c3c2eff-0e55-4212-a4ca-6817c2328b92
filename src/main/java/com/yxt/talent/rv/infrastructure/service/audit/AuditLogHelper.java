package com.yxt.talent.rv.infrastructure.service.audit;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@ToString
public class AuditLogHelper {
    /**
     * 日志模块 <br>
     * 模块起名 不超过20个字符，否则日志那边直接 500
     */
    @Getter
    @RequiredArgsConstructor
    public enum Module {
        /*盘点项目*/
        PROJECT("盘点项目", "trv_project"),

        /*盘点校准会*/
        CALIBRATION("盘点校准会", "trv_calibration"),

        /*盘点绩效管理*/
        PERFORMANCE("盘点绩效管理", "trv_performance"),

        /**
         * 盘点设置
         */
        SETTING("盘点设置", "trv_setting")

        ;
        private final String name;
        private final String code;
    }
}
