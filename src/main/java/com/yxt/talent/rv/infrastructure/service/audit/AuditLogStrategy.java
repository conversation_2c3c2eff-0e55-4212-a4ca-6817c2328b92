package com.yxt.talent.rv.infrastructure.service.audit;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.common.annotation.DbHintMaster;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;

/**
 * 业务日志的策略接口,
 * [注意]: 它的实现类必须要是原型或者requestScope作用域, 不能是单例模式
 */
public interface AuditLogStrategy {

    Logger log = LoggerFactory.getLogger(AuditLogStrategy.class);

    String EMPTY = "--";

    /**
     * 前置工作、数据准备
     *
     * @param argsNames - 参数名列表
     * @param args      - 参数列表[与参数名称列表顺序一致]
     */
    default void doPrepare(String[] argsNames, Object[] args) {
        // do nothing
    }

    /**
     * 异步执行日志组装等工作
     *
     * @param auditLog  - 在切面上下文中获取的日志对象，
     * @param argsNames - 参数名列表
     * @param args      - 参数列表[与参数名称列表顺序一致]
     * @param result    - 业务执行完毕的返回值
     */
    @Async
    @DbHintMaster
    void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result);

    default AuditDetail getUpdateAuditDetail(String model) {
        AuditDetail ad = getAuditDetail(model);
        ad.setAction(AuditConsts.UPDATE);
        return ad;
    }

    default AuditDetail getAuditDetail(String model) {
        AuditDetail ad = AuditDetail.of();
        ad.setModule(model);
        return ad;
    }

}
