package com.yxt.talent.rv.infrastructure.service.audit;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value = ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditingPlus {

    /**
     * 日志策略Class
     */
    Class<? extends AuditLogStrategy> strategyClass();

    /**
     * 实体ID, 支持SpEL
     */
    String entityId() default "";

    /**
     * 正则表达式是否在调用接口前即可解析到值，
     * 一般创建接口需要在调用接口之后的返回值中才能获取到主键id,
     * 而更新接口则在调用接口之前即可获取到主键id
     *
     * @return
     */
    boolean before() default true;

}
