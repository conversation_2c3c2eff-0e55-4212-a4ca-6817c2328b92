package com.yxt.talent.rv.infrastructure.service.auth;

import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.JwtUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.jwt.JwtUserInfo;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通用认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthenticateService {

    private final AppProperties appProperties;

    /**
     * 生成JWT
     *
     * @param claim
     * @return
     */
    public String generateToken(JwtUserInfo claim) {
        AppProperties.Jwt jwt = appProperties.getJwt();
        return JwtUtil.generateToken(claim, jwt.getSecret(), jwt.getTimeout());
    }

    /**
     * 解析JWT
     *
     * @return
     */
    public JwtUserInfo getInfoFromRequest() {
        String token = ApiUtil.getToken(ApiUtil.getRequestByContext());
        return JwtUtil.getInfoFromToken(token, appProperties.getJwt().getSecret());
    }


}
