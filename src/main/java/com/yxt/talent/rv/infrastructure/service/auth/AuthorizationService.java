package com.yxt.talent.rv.infrastructure.service.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionBean;
import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionDept;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.criteria.ScopeQuery;
import com.yxt.dmp.api.facade.TeamFacade;
import com.yxt.dmp.api.model.req.team.TeamSubordinatesReqDTO;
import com.yxt.dmp.api.model.req.team.TeamUserInfoReqDTO;
import com.yxt.dmp.api.model.resp.team.TeamSubordinatesRespDTO;
import com.yxt.talent.rv.application.user.UserQryAppService;
import com.yxt.talent.rv.application.user.dto.ScopeAuthDTO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.common.IdsBean;
import com.yxt.udpfacade.bean.dept.DeptManagerBean;
import com.yxt.udpfacade.bean.dept.DeptSimpleBean;
import com.yxt.udpfacade.bean.dept.DeptTreeId;
import com.yxt.udpfacade.bean.dept.ManagedDeptBean;
import com.yxt.udpfacade.bean.user.es.DeptSearchBean;
import com.yxt.udpfacade.bean.user.es.search.EsUserInfoVo;
import com.yxt.udpfacade.bean.user.es.search.EsUserSearchParam;
import com.yxt.udpfacade.constants.UdpapiConstants;
import com.yxt.udpfacade.enums.SourceFromEnum;
import com.yxt.udpfacade.service.UdpEsUserSearchFacade;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.RedisKeys.CK_USER_AUTH_CLIENT_TEAM;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.PRODUCT_CODE;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;
import static java.lang.String.format;

/**
 * 授权服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthorizationService {

    private static final long CACHE_TIME = 30; // 缓存时间 分钟
    private final CoreAclService coreAclService;
    private final UdpAclService udpAclService;
    private final UdpEsUserSearchFacade udpEsUserSearchFacade;
    private final RedisRepository talentRedisRepository;
    private final UserQryAppService userQryAppService;
    private final TeamFacade teamFacade;
    private final CoreApiFacade coreApiFacade;

    /**
     * 获取当前用户的数据权限
     *
     * @param userCacheDetail
     * @param navCode
     * @param dataPermCode
     * @return
     */
    public ScopeAuthDTO getUserDataPermAuth(
        UserCacheDetail userCacheDetail, String navCode, String dataPermCode) {
        String orgId = userCacheDetail.getOrgId();
        String userId = userCacheDetail.getUserId();
        boolean isAdmin = "1".equals(userCacheDetail.getAdmin());
        return getScopeAuthDTO(orgId, userId, isAdmin, navCode, dataPermCode);
    }

    /**
     * 获取指定用户的数据的数据权限
     *
     * @param orgId
     * @param userId
     * @param isAdmin
     * @param navCode
     * @param dataPermCode
     * @return
     */
    public ScopeAuthDTO getScopeAuthDTO(
        String orgId, String userId, boolean isAdmin, String navCode, String dataPermCode) {
        log.debug("LOG10392:orgId={},userId={},isAdmin={},navCode={},dataPermCode={}", orgId,
            userId, isAdmin, navCode, dataPermCode);
        if (isAdmin) {
            ScopeAuthDTO scopeAuth = new ScopeAuthDTO();
            scopeAuth.setAdmin(true);
            return scopeAuth;
        }

        DataAuthPermissionBean dataAuthPerm =
            this.doGetUserDataPermAuth(orgId, userId, navCode, dataPermCode);
        log.debug("LOG10382:orgId={},userId={},navCode={},dataPermCode={},respDeptSize={}", orgId,
            userId, navCode, dataPermCode, BeanHelper.bean2Json(dataAuthPerm, ALWAYS));
        return this.getScopeAuth(orgId, userId, dataAuthPerm);
    }

    public DataAuthPermissionBean doGetUserDataPermAuth(
        String orgId, String userId, String navCode, String dataPermCode) {
        DataAuthPermissionBean authPermission;
        try {
            authPermission =
                coreAclService.getUserDataPermission(orgId, userId, navCode, dataPermCode,
                    PRODUCT_CODE);
        } catch (Exception e) {
            authPermission = new DataAuthPermissionBean();
            log.info("LOG10402:getUserDataPermission error", e);
        }
        return authPermission;
    }

    public ScopeAuthDTO getScopeAuth(
        String orgId, String currUserId, DataAuthPermissionBean dataPerm) {
        ScopeAuthDTO scopeAuth = new ScopeAuthDTO();
        if (StringUtils.isBlank(currUserId) || dataPerm == null) {
            return scopeAuth;
        }

        // 拿到所有有权限的部门id及其后代部门id
        List<DataAuthPermissionDept> deptList = dataPerm.getDeptList();
        Set<String> filterAuthDeptIds = new HashSet<>();
        Set<String> hasAuthChildrenDeptIds = new HashSet<>();
        for (DataAuthPermissionDept dataAuthPermissionDept : deptList) {
            filterAuthDeptIds.add(dataAuthPermissionDept.getId());
            if (dataAuthPermissionDept.getIncludeSub() == 1) {
                hasAuthChildrenDeptIds.add(dataAuthPermissionDept.getId());
            }
        }
        if (!hasAuthChildrenDeptIds.isEmpty()) {
            filterAuthDeptIds.addAll(
                udpAclService.exchangeSubDeptIds(orgId, new ArrayList<>(hasAuthChildrenDeptIds)));
        }
        scopeAuth.setScopeDeptIds(new ArrayList<>(filterNullAndBlank(filterAuthDeptIds)));

        //如果数据权限设置的是【自己的数据】时，需将自己加入到userIds中
        if (CollectionUtils.isNotEmpty(dataPerm.getUserIds())) {
            scopeAuth.setScopeUserIds(dataPerm.getUserIds());
        }
        scopeAuth.setAdmin(dataPerm.getIsAllOrgPermission() == YesOrNo.YES.getValue());
        return scopeAuth;
    }

    /**
     * 检查传入的搜索部门是否是用户管辖的部门 authorization
     *
     * @param orgId
     * @param userId
     * @param scopeSearchCriteria
     */
    public void checkAndFillUserDeptManageScope(
        String orgId, String userId, ScopeQuery scopeSearchCriteria) {
        Set<String> allScopeDeptIds = userQryAppService.findUserManageDeptIds(orgId, userId);

        // 不是任何部门的部门经理
        Validate.isNotEmpty(allScopeDeptIds, ExceptionKeys.DMP_SCOPE_DEPT_IDS_NO_PERMISSION);

        if (CollectionUtils.isEmpty(scopeSearchCriteria.getScopeDeptIds())) {
            // 如果前端没有传deptIds，则默认查询用户管辖的所有部门的数据
            scopeSearchCriteria.setScopeDeptIds(new ArrayList<>(allScopeDeptIds));
        } else {
            // 如果传了待校验deptIds，则校验传入的deptIds是否全部包含在allScopeDeptIds中
            Validate.isTrue(
                allScopeDeptIds.containsAll(scopeSearchCriteria.getScopeDeptIds()),
                ExceptionKeys.DMP_SCOPE_DEPT_IDS_NO_PERMISSION);
        }
    }

    public void checkAndFillUserDeptManageScope(String orgId, String userId, List<String> checkDeptIds) {
        if (CollectionUtils.isEmpty(checkDeptIds)) {
            return;
        }

        // 获取用户任部门经理的所有部门id及其后代部门id集合
        Set<String> allScopeDeptIds = userQryAppService.findUserManageDeptIds(orgId, userId);
        // 不是任何部门的部门经理
        Validate.isNotEmpty(allScopeDeptIds, ExceptionKeys.DMP_SCOPE_DEPT_IDS_NO_PERMISSION);
        // 如果传了待校验deptIds，则校验传入的deptIds是否全部包含在allScopeDeptIds中
        Validate.isTrue(
            allScopeDeptIds.containsAll(checkDeptIds),
            ExceptionKeys.DMP_SCOPE_DEPT_IDS_NO_PERMISSION);
    }

    /**
     * 用户端 <br>
     * 我直属 & 我管辖的
     *
     * @param currentUser
     * @param isManager   true 我直属、false 我管辖
     */
    public List<String> getClientAuthUserIds(UserCacheDetail currentUser, boolean isManager) {
        String userId = currentUser.getUserId();
        log.info("LOG62390: userId={}, isManager={}", userId, isManager);
        List<String> userIds;

        // 加缓冲
        String cacheKey = format(RedisKeys.CK_USER_AUTH_CLIENT, userId, isManager);
        String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
        if (!StringUtils.isEmpty(valueByKey)) {
            userIds = BeanHelper.json2Bean(valueByKey, ArrayList.class, String.class);
            talentRedisRepository.expire(cacheKey, CACHE_TIME, TimeUnit.MINUTES); // 更新缓存时间
            return userIds;
        }

        userIds = doGetClientAuthUserIdsForV2(isManager, currentUser);
        log.info(
            "LOG62400:{}", "用户端我直属&我管辖的2.0 --> " + userId + " 管辖用户数：" +
                           (userIds != null ? userIds.size() : 0));

        if (null == userIds) {
            return new ArrayList<>();
        }

        // 设置缓存并设置时间
        talentRedisRepository.setValue(
            cacheKey, BeanHelper.bean2Json(userIds, ALWAYS), CACHE_TIME, TimeUnit.MINUTES);

        return userIds;
    }

    private List<String> doGetClientAuthUserIdsForV2(
        boolean isManager, UserCacheDetail currentUser) {
        if (isManager) {
            // 直属员工
            CommonList<String> subMember =
                udpAclService.managerSubMember(currentUser.getOrgId(), currentUser.getUserId());
            if (null == subMember) {
                return new ArrayList<>();
            }
            return subMember.getDatas();
        } else {
            // 我管辖的
            List<ManagedDeptBean> deptBeans =
                udpAclService.findManagedDeptUsers(currentUser.getOrgId(), currentUser.getUserId());
            log.debug("LOG65160:{}", BeanHelper.bean2Json(deptBeans, ALWAYS));
            if (CollectionUtils.isNotEmpty(deptBeans)) {
                List<String> deptIds =
                    deptBeans.stream().map(ManagedDeptBean::getDeptId).collect(Collectors.toList());
                EsUserSearchParam searchCriteria = new EsUserSearchParam();
                searchCriteria.setSourceFrom(SourceFromEnum.TALENT.getValue());
                searchCriteria.setIsOpenLimit(true);
                searchCriteria.setOrgId(currentUser.getOrgId());
                searchCriteria.setProductCode(PRODUCT_CODE);
                List<EsUserInfoVo> esUserInfoVos =
                    this.getEsUserListByDeptIds(searchCriteria, deptIds);
                if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                    return esUserInfoVos.stream()
                        .map(EsUserInfoVo::getId)
                        .collect(Collectors.toList());
                }
            }
        }
        return new ArrayList<>();
    }

    private List<EsUserInfoVo> getEsUserListByDeptIds(
        EsUserSearchParam searchParam, List<String> deptIds) {
        // deptSearchList 入参udp限制最多500
        // 循环调用udp接口，每次限制入参500个部门id
        List<EsUserInfoVo> result = Lists.newArrayList();
        List<List<String>> splitList = Lists.partition(deptIds, 500);
        for (List<String> childList : splitList) {
            List<DeptSearchBean> deptSearchList = new ArrayList<>();
            childList.forEach(deptId -> {
                DeptSearchBean bean = new DeptSearchBean();
                bean.setOrgId(searchParam.getOrgId());
                bean.setIncludeSubDept(1);
                bean.setDeptId(deptId);
                deptSearchList.add(bean);
            });
            searchParam.setDeptSearchList(deptSearchList);
            searchParam.setProductCode(PRODUCT_CODE);
            searchParam.setStatus(UdpapiConstants.ALL);
            searchParam.setShowAll(1);
            List<EsUserInfoVo> esUserInfoVos = udpEsUserSearchFacade.search4List(searchParam);
            if (CollectionUtils.isNotEmpty(esUserInfoVos)) {
                result.addAll(esUserInfoVos);
            }
        }
        return result;
    }

    /**
     * 根据我的团队id获取下辖的部门和人员
     *
     * @param orgId
     * @param userId
     * @param teamId
     */
    public ScopeAuthDTO getTeamMgrScopeAuth(String orgId, String userId, String teamId) {
        log.debug("LOG65140:userId={}, orgId={}, teamId={}", userId, orgId, teamId);
        ScopeAuthDTO scopeAuthDTO = new ScopeAuthDTO();

        if (!CharSequenceUtil.isAllNotBlank(orgId, userId, teamId)) {
            return scopeAuthDTO;
        }

        // 加缓冲
        String cacheKey = format(CK_USER_AUTH_CLIENT_TEAM, orgId, userId, teamId);
        String valueByKey = talentRedisRepository.getValueByKey(cacheKey);
        if (StringUtils.isNotEmpty(valueByKey)) {
            scopeAuthDTO = BeanHelper.json2Bean(valueByKey, ScopeAuthDTO.class);
            return scopeAuthDTO;
        }

        scopeAuthDTO = doGetTeamMgrScopeAuth(teamId, orgId, userId);
        log.debug("LOG65150:{}", BeanHelper.bean2Json(scopeAuthDTO, ALWAYS));

        if (scopeAuthDTO.isAdmin() || CollectionUtils.isNotEmpty(scopeAuthDTO.getScopeUserIds()) ||
            CollectionUtils.isNotEmpty(scopeAuthDTO.getScopeDeptIds())) {
            // 设置缓存并设置时间
            talentRedisRepository.setValue(
                cacheKey, BeanHelper.bean2Json(scopeAuthDTO, ALWAYS), 5, TimeUnit.MINUTES);
        }

        return scopeAuthDTO;
    }

    private ScopeAuthDTO doGetTeamMgrScopeAuth(String teamId, String orgId, String userId) {
        ScopeAuthDTO scopeAuthDTO = new ScopeAuthDTO();
        MyTeamTypeEnum myTeamTypeEnum = MyTeamTypeEnum.getByDefId(teamId);
        switch (myTeamTypeEnum) {
            case DEPT_MANAGER:
                Set<String> deptIds = findManageDeptIds(orgId, userId);
                CommonList<String> deptList = new CommonList<>(new ArrayList<>(deptIds));
                CommonList<DeptTreeId> deptTree = udpAclService.getDeptTreeIds(orgId, deptList);
                Set<String> allDepts = new HashSet<>(deptIds);
                if (deptTree != null && CollectionUtils.isNotEmpty(deptTree.getDatas())) {
                    deptTree.getDatas().forEach(idTree -> allDepts.addAll(idTree.getTreeIds()));
                }
                scopeAuthDTO.setScopeDeptIds(new ArrayList<>(allDepts));
                return scopeAuthDTO;
            case UNDER_MANAGER:
                // 直属的直属也会被查出来,需求如此
                CommonList<String> stringCommonList = udpAclService.managerSubMember(orgId, userId);
                scopeAuthDTO.setScopeUserIds(stringCommonList.getDatas());
                return scopeAuthDTO;
            case CUSTOM:
                return getScopeWithCustom(orgId, userId, teamId);
            default:
                return scopeAuthDTO;
        }
    }

    private ScopeAuthDTO getScopeWithCustom(String orgId, String userId, String teamId) {
        ScopeAuthDTO scopeAuthDTO = new ScopeAuthDTO();
        Set<String> tempDeptIds = getDeptIds(orgId, userId, teamId);
        // 如果团队只配置了一个部门 并且他是根部门  那就直接返回这个部门的范围就行（注意 要标注需要后期铺平）
        if (tempDeptIds.size() == 1) {
            String deptId = tempDeptIds.stream().findFirst().orElse(null);
            boolean isRootDept = checkIsRootDept(orgId, deptId);
            if (isRootDept) {
                // 如果是根部门，按照管理员角色处理
                scopeAuthDTO.setAdmin(true);
                return scopeAuthDTO;
            }
        }
        Set<String> tempUserIds = getUserIds(orgId, userId, teamId);
        if (CollectionUtils.isNotEmpty(tempDeptIds)) {
            scopeAuthDTO.setScopeDeptIds(new ArrayList<>(tempDeptIds));
        }
        if (CollectionUtils.isNotEmpty(tempUserIds)) {
            scopeAuthDTO.setScopeUserIds(new ArrayList<>(tempUserIds));
        }
        return scopeAuthDTO;
    }

    public Set<String> findManageDeptIds(String orgId, String userId) {
        IdsBean bean = new IdsBean();
        bean.setIds(ListUtil.toList(userId));

        List<DeptManagerBean> beans = udpAclService.checkIsDeptManagers(orgId, bean);
        if (CollUtil.isEmpty(beans)) {
            return Collections.emptySet();
        }

        return beans.stream().filter(Objects::nonNull) // 非空
            .filter(o -> o.getManager() == 1) // 是部门经理的
            .map(DeptManagerBean::getDeptId).collect(Collectors.toSet());
    }

    public Set<String> getDeptIds(String orgId, String userId, String teamId) {
        TeamSubordinatesReqDTO reqDto = new TeamSubordinatesReqDTO();
        reqDto.setOrgId(orgId);
        reqDto.setUserId(userId);
        reqDto.setTeamId(teamId);
        List<TeamSubordinatesRespDTO> resList = teamFacade.teamSubordinates(reqDto);
        if (CollUtil.isEmpty(resList)) {
            return Collections.emptySet();
        }
        return resList.stream()
            .filter(Objects::nonNull)
            .map(TeamSubordinatesRespDTO::getTargetId)
            .collect(Collectors.toSet());
    }

    public Set<String> getUserIds(String orgId, String userId, String teamId) {
        TeamUserInfoReqDTO reqDto = new TeamUserInfoReqDTO();
        reqDto.setOrgId(orgId);
        reqDto.setUserId(userId);
        reqDto.setTeamId(teamId);
        reqDto.setOffset(0);
        reqDto.setLimit(1_0000);

        PagingList<TeamSubordinatesRespDTO> pagingList = teamFacade.teamUsersInfo(reqDto);

        if (pagingList == null || CollUtil.isEmpty(pagingList.getDatas())) {
            return Collections.emptySet();
        }
        return pagingList.getDatas()
            .stream()
            .filter(Objects::nonNull)
            .map(TeamSubordinatesRespDTO::getTargetId)
            .collect(Collectors.toSet());
    }

    public boolean checkIsRootDept(String orgId, @Nullable String deptId) {
        AuthorizationService authorizationService =
            (AuthorizationService) AopContext.currentProxy();
        DeptSimpleBean dept = authorizationService.getRootDept(orgId);
        return dept != null && CharSequenceUtil.equals(deptId, dept.getId());
    }

    // 查询机构的根部门信息
    @Nullable
    @Cacheable(value = RedisKeys.CK_ORG_ROOT_DEPT, key = "#orgId", condition = "#orgId!=null and #orgId!=''", unless = "#result==null")
    public DeptSimpleBean getRootDept(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return null;
        }
        return udpAclService.findOrgRootDept(orgId);
    }

}
