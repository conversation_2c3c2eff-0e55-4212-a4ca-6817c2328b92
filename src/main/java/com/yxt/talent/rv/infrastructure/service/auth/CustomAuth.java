package com.yxt.talent.rv.infrastructure.service.auth;

import com.yxt.common.component.Authentication;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

@Component
@RefreshScope
public class CustomAuth implements Authentication {

    @Value("${custom.token:b75a2a8b-4e10-41e7-a4e8-b23052a355b4}")
    private String customToken;

    @Override
    public boolean auth(HttpServletRequest request, boolean verifyBlank) {
        String requestHeader = request.getHeader("X-Request-Authorization");
        return requestHeader != null && requestHeader.equals(this.customToken);
    }
}
