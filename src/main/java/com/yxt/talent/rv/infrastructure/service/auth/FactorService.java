package com.yxt.talent.rv.infrastructure.service.auth;

import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.coreapi.client.bean.em.OrgFactorStateEnum;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 后端去版本改造 前提：去版本是基于2.0企业设置，首先企业要是2.0企业，1.0企业不存在去版本，依旧使用1.0机构参的形式 此类维护的数据来自产品经理，并维护在运营平台
 * 盘点去版本PRD：<a href="https://lanhuapp.com/web/#/item/project/product?type=share_mark&tab=product&pid=e479da26-acad-4d4e-a7b2-b3633d9412d5&versionId=27e35c6b-30eb-4d87-a34e-e9c0b304a86e&docId=5c315253-65e8-4504-ac66-41ef90bfb6f1&image_id=5c315253-65e8-4504-ac66-41ef90bfb6f1&docType=axure&imgId=undefined&pageId=4cedbf3fd4b0463689b88af53bfe9bd0&teamId=660b69c7-a779-44e9-9443-671b909da1ea">...</a>
 * 去版本方案设计：<a href="https://confluence.yunxuetang.com.cn/pages/viewpage.action?pageId=68624868">...</a>
 *
 * <p>一、运营平台： >1、开发环境：<a href="https://123.yunxuetang.com.cn">...</a> >2、预发布环境： >3、产线环境：
 *
 * <p>二、人才盘点要素(要素可能是应用APP，模块MODEL或者能力SKILL) >1、人才盘点(gwnl_talentrv) 三、人才盘点功能点
 * >1、创建盘点项目(talentrv_projectlistoperate_add) >2、创建校准会(talentrv_calibration_add)
 *
 * <p>四、使用方法 >1、方法中需要硬编码的部分可以只用coreApiFacade中的verifyOrgFactors方法用于鉴权，具体返回参照verifyOrgFactors文档使用说明。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FactorService {
    public static final String APP_GWNL_PRO = "gwnl_pro";

    // 应用ID,分别对应人才发展基础、人才发展测训、人才发展高级
    private static final String APP_GWNL_BASIC = "gwnl_basic";
    private static final String APP_GWNL_CX = "gwnl_cx";
    private static final List<String> APP_GWNL_FACTOR_LIST =
            Lists.newArrayList(APP_GWNL_BASIC, APP_GWNL_CX, APP_GWNL_PRO);

    // 自定义orgEdition（0-未开通人才发展，1-基础版，2-测训版，3-高级版）
    private static final int ORG_EDITION_NONE = 0;
    private static final int ORG_EDITION_BASIC = 1;
    private static final int ORG_EDITION_CX = 2;
    private static final int ORG_EDITION_PRO = 3;
    private final CoreAclService coreAclService;

    /**
     * 机构是否开启poc开关
     */
    @Value("${base.info.service.disable:false}")
    private String baseInfoServiceDisable;

    /**
     * 获取机构开通的 此接口只提供给2.0机构使用
     *
     * @param orgId 机构id
     * @return 0-未开通，1-基础版，2-测训版，3-高级版
     */
    public int getOrgEdition(String orgId) {
        if (isProV2(orgId)) {
            return ORG_EDITION_PRO;
        }
        if (isCxV2(orgId)) {
            return ORG_EDITION_CX;
        }
        if (isBasicV2(orgId)) {
            return ORG_EDITION_BASIC;
        }
        return ORG_EDITION_NONE;
    }

    /**
     * 是否是测训版 2.0专用 要素包中开了人才发展测训要素包且未开人才发展收费要素包，则为测训版
     *
     * <p>1、测训版需要隐藏能力模型库的新建模型和批量发布功能
     *
     * <p>1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是测训版，false-不是测训版
     */
    public boolean isCxV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap =
                getOrgVerifyFactorBeanMap(orgId, APP_GWNL_FACTOR_LIST);
        log.info(
                "LOG61800:获取测训要素判断，coreapi返回：{}",
                BeanHelper.bean2Json(orgVerifyFactorBeanMap, ALWAYS));
        return isCx(orgVerifyFactorBeanMap);
    }

    private boolean isCx(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int cxState = orgVerifyFactorBeanMap.get(APP_GWNL_CX).getFactorState();
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(cxState) && !isFactorPurchasedEnable(proState);
    }

    /**
     * 是否是高级版 2.0 专用 要素包中开了人才发展收费要素包，则为高级版
     *
     * <p>1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是高级版，false-不是高级版
     */
    public boolean isProV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap =
                getOrgVerifyFactorBeanMap(orgId, APP_GWNL_FACTOR_LIST);
        return isPro(orgVerifyFactorBeanMap);
    }

    private boolean isPro(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(proState);
    }

    /**
     * 是否是基础 2.0专用 这里判断要素包中开且只开了人才发展基础要素包，则为基础版
     *
     * <p>1、免费版需要去除岗位配置的<批量导入>功能 2、免费版只能进行三级模式配置
     *
     * <p>1.0机构默认是开通的，走机构参，不走去版本判别
     *
     * @param orgId 机构id
     * @return true-是基础版，false-不是基础版
     */
    public boolean isBasicV2(String orgId) {
        Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap =
                getOrgVerifyFactorBeanMap(orgId, APP_GWNL_FACTOR_LIST);
        return isBasic(orgVerifyFactorBeanMap);
    }

    private boolean isBasic(Map<String, OrgVerifyFactorBean> orgVerifyFactorBeanMap) {
        int basicState = orgVerifyFactorBeanMap.get(APP_GWNL_BASIC).getFactorState();
        int cxState = orgVerifyFactorBeanMap.get(APP_GWNL_CX).getFactorState();
        int proState = orgVerifyFactorBeanMap.get(APP_GWNL_PRO).getFactorState();
        return isFactorPurchasedEnable(basicState) && !isFactorPurchasedEnable(cxState) &&
               !isFactorPurchasedEnable(proState);
    }

    private Map<String, OrgVerifyFactorBean> getOrgVerifyFactorBeanMap(
            String orgId, List<String> factorCodes) {
        List<OrgVerifyFactorBean> orgVerifyFactorBeans =
                coreAclService.verifyOrgFactors(orgId, factorCodes);
        return StreamUtil.list2map(orgVerifyFactorBeans, OrgVerifyFactorBean::getFactorCode);
    }

    public OrgVerifyFactorBean getVerifyFactorBean(String orgId, String factorCode) {
        List<OrgVerifyFactorBean> verifyFactorBean =
                coreAclService.verifyOrgFactors(orgId, Lists.newArrayList(factorCode));
        if (CollectionUtils.isNotEmpty(verifyFactorBean)) {
            return verifyFactorBean.get(0);
        } else {
            return new OrgVerifyFactorBean();
        }
    }

    private boolean isFactorPurchasedEnable(int state) {
        return state == OrgFactorStateEnum.PURCHASED_NOT_EXPIRED.getState();
    }

    /**
     * 判断某一个要素是否开启
     *
     * @param orgId  机构id
     * @param factor 要素code，可以是应用、模块、能力任意要素中的一个
     * @return true-要素正常使用，false-要素不可使用
     */
    private boolean verifyFactor(String orgId, String factor) {
        OrgVerifyFactorBean verifyFactorBean = getVerifyFactorBean(orgId, factor);
        log.info(
                "LOG61810:获取测评要素判断，coreapi返回：{}",
                BeanHelper.bean2Json(verifyFactorBean, ALWAYS));
        int isOpenFactor = verifyFactorBean.getFactorState();
        return isFactorPurchasedEnable(isOpenFactor);
    }
}
