package com.yxt.talent.rv.infrastructure.service.auth;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 我的团队-特殊团队id
 */
@Getter
@RequiredArgsConstructor
public enum MyTeamTypeEnum {
    /**
     * 部门经理
     */
    DEPT_MANAGER("00000000-0000-0000-0000-000000000000"),
    /**
     * 直属经理
     */
    UNDER_MANAGER("11111111-1111-1111-1111-111111111111"),
    CUSTOM(null);

    private final String defId;

    public static MyTeamTypeEnum getByDefId(String id) {
        if (StringUtils.isBlank(id)) {
            return CUSTOM;
        }
        for (MyTeamTypeEnum en : MyTeamTypeEnum.values()) {
            if (id.equals(en.getDefId())) {
                return en;
            }
        }
        return CUSTOM;
    }

}
