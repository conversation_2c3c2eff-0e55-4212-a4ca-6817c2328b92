package com.yxt.talent.rv.infrastructure.service.auth.tinahe;

import com.yxt.common.pojo.api.PagingList;
import com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 天和服务远程调用
 *
 * <AUTHOR>
 * @since 2022/5/9 13:35
 */
@FeignClient(name = "tianheapi", url = "${sprv.tianhe.base-url}", configuration = TianheFeignClientInterceptor.class)
public interface TianheApiClient {

    /**
     * 获取天和用户组详情
     */
    @GetMapping(value = "/public/group", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<List<TianheUserGroup>> listUserGroups();

    /**
     * 获取用户id获取天和用户详情
     */
    @GetMapping(value = "/public/employee/info", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<TianheUser> getUserInfo(@RequestParam("id") String tianheUserId);

    /**
     * auth免登
     */
    @GetMapping(value = "/oauth/access_token", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<TianheToken> acquireToken(
        @RequestParam("grant_type") String grantType, @RequestParam("client_id") String clientId,
        @RequestParam("client_secret") String clientSecret, @RequestParam String code);

    /**
     * 根据token获取登录人信息
     */
    @GetMapping(value = "/oauth/userinfo", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<TianheUser> acquireUser(@RequestParam("access_token") String token);

    /**
     * 天和登出
     */
    @GetMapping(value = "/oauth/logout", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<Void> logout(@RequestParam("access_token") String token);

    /**
     * 获取部门树
     */
    @GetMapping(value = "/public/dept/tree", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<List<TianheDept>> getTianheDeptTree();

    /**
     * 搜索员工列表
     */
    @GetMapping(value = "/public/employee/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    TianheResult<PagingList<TianheUser>> searchUsers(@SpringQueryMap TianheUserSearchCriteria criteria);

}
