package com.yxt.talent.rv.infrastructure.service.auth.tinahe;

import com.yxt.common.util.ApiUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * feign拦截器
 *
 * <AUTHOR>
 * @since 2022/5/9 13:48
 */
@Slf4j
public class TianheFeignClientInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        String token = ApiUtil.getToken(ApiUtil.getRequestByContext());
        log.debug("LOG00440:token:{}", token);
        if (!template.queries().containsKey("access_token") && StringUtils.hasText(token)) {
            template.query("access_token", token);
        }
    }

}
