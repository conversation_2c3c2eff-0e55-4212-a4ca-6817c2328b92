package com.yxt.talent.rv.infrastructure.service.auth.tinahe;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.function.Supplier;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.AUTH_TIANHE_REQUEST_FAILED;

/**
 * <AUTHOR>
 * @since 2022/5/10 20:59
 */
@Slf4j
@Setter
@Getter
@ToString(callSuper = true)
public class TianheResult<T> {

    private Integer code;

    private String msg;

    private Long timestamp;

    private T data;

    public static <T> TianheResult<T> fallback(Throwable cause) {
        TianheResult<T> fallback = new TianheResult<>();
        fallback.setCode(-1);
        fallback.setMsg(cause.getMessage());
        fallback.setTimestamp(System.currentTimeMillis());
        return fallback;
    }

    public static <T> T resolve(Supplier<TianheResult<T>> supplier) {
        TianheResult<T> response;
        try {
            response = supplier.get();
        } catch (Exception throwable) {
            // 应对没有fallback托底，或者fallback本身逻辑报错的情况
            log.error("LOG00900:tianhe request error.", throwable);
            throw new ApiException(AUTH_TIANHE_REQUEST_FAILED, throwable.getMessage());
        }
        doHandleException(response);
        return Objects.requireNonNull(response).getData();
    }

    private static <T> void doHandleException(TianheResult<T> response) throws ApiException {
        if (response == null || response.getCode() == null ||
            !StringUtils.hasText(response.getMsg())) {
            log.error("LOG00910:返回数据无效。{}", bean2Json(response, ALWAYS));
            throw new ApiException(AUTH_TIANHE_REQUEST_FAILED, bean2Json(response, ALWAYS));
        }
        if (response.getCode() != 0) {
            if (response.getCode() == 4004) {
                // 登出操作,然后抛出异常,前端跳转到登陆页面
                SpringContextHolder.getBean(TianheAuthService.class).logout();
                // NOSONAR: {\"code\":4004,\"msg\":\"access_token 已失效\",\"timestamp\":1654503322661,\"data\":null}
                throw new ApiException(ExceptionKeys.AUTH_TIANHE_TOKEN_INVALID);
            }
            log.error("LOG00920:数据请求不成功。response={}", response);
            throw new ApiException(AUTH_TIANHE_REQUEST_FAILED, bean2Json(response, ALWAYS));
        }
    }

}
