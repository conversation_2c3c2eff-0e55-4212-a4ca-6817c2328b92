package com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/10 20:45
 */
@Setter
@Getter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianheUser {

    /**
     * 用户id
     */
    private String id;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 是否离职（0：在职，1：离职）
     */
    private Integer resignFlag;

    /**
     * 是否禁用（0：可用，1：禁用）
     */
    private Integer userAccountControl;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户在天和系统中的id, 等同于上面的id
     */
    private String userId;

    /**
     * 用户中文名字
     */
    private String username;

    /**
     * 用户天和平台账号
     */
    private String accountNo;

    /**
     * 用户工号
     */
    private String employeeCode;

    /**
     * 所在部门名称
     */
    private String deptName;

    /**
     * 所属租户id
     */
    private String tenantId;

    /**
     * 所属租户名称
     */
    private String tenantName;

    /**
     * 手机号
     */
    private String phoneNo;

    @JsonProperty("groupList")
    private List<TianheUserGroup> tianheUserGroups;

}
