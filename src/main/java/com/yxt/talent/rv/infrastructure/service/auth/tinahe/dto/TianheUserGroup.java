package com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/5/9 14:00
 */
@Setter
@Getter
@ToString(callSuper = true)
@Schema(name = "天和用户组详情")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianheUserGroup {

    @Schema(description = "天和用户组主键，对应svcmaker中用户组表的originId字段")
    private String id;

    @Schema(description = "天和用户组名")
    private String groupName;

    @Schema(description = "用户组应用编号/标识")
    private String appFlag;

}
