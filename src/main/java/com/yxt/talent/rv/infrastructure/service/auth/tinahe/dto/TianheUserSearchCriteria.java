package com.yxt.talent.rv.infrastructure.service.auth.tinahe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @since 2022/5/11 17:49
 */
@Setter
@Getter
@NoArgsConstructor
@Schema(name = "搜索天和用户")
@ToString(callSuper = true)
@SuperBuilder(toBuilder = true)
public class TianheUserSearchCriteria {

    @Schema(description = "天和账号，支持模糊搜索")
    private String accountNo;

    @Schema(description = "所属天和部门id")
    private String deptId;

    @Schema(description = "天和员工姓名，支持模糊搜索")
    private String employeeName;

    @Schema(description = "天和员工姓名、帐号左匹配搜索")
    private String uName;

    @Schema(description = "分页参数，默认0，偏移量")
    private int offset;

    @Schema(description = "分页参数，默认20,查询条数")
    private int limit;

}
