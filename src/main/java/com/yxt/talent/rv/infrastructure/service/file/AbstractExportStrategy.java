package com.yxt.talent.rv.infrastructure.service.file;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.OutputStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractExportStrategy implements OutputStrategy {

    private static final Logger log = LoggerFactory.getLogger(AbstractExportStrategy.class);
    public static final String MODULE_CODE = "talentRvProject";

    /**
     * 子应用code(ote,udp)
     */
    public static final String APP_CODE = "gwnl";

    /**
     * 平台标识（10:金牌团队，20:机构网校，40:商城，50:绚星企业大学）
     */
    public static final String SOURCE_CODE = "50";

    protected DownInfo4Add buildDownInfo(
            UserCacheDetail userCache, String fileName, String taskName) {
        // 如果filename没有后缀--Orig，则后缀加上
        String newFileName = fileName.endsWith(FileConstants.ORIG) ? fileName : fileName + FileConstants.ORIG;
        log.debug("LOG20523:{}", newFileName);
        return DownInfo4Add.builder()
                .orgId(userCache.getOrgId())
                .fullname(userCache.getFullname())
                .userId(userCache.getUserId())
                .sourceCode(SOURCE_CODE)
                .appCode(APP_CODE)
                .moduleCode(MODULE_CODE)
                .fileName(newFileName)
                .name(taskName).build();
    }
}
