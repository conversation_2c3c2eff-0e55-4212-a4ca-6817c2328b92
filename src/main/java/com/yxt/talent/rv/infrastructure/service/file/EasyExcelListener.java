package com.yxt.talent.rv.infrastructure.service.file;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EasyExcelListener extends AnalysisEventListener<Object> {

    private List<Map<Integer, String>> data = new ArrayList<>();

    private List<Map<Integer, String>> header = new ArrayList<>();

    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {
        if (o instanceof Map<?, ?> m) {
            int mapSize = m.size();
            Map<Integer, String> map = new HashMap<>(mapSize);
            for (int i = 0; i < mapSize; i++) {
                Object v = m.get(i);
                if (v instanceof String v1) {
                    map.put(i, v1);
                }
            }
            data.add(map);
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        header.add(headMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // Do nothing
    }
}
