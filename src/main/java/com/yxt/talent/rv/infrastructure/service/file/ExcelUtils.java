package com.yxt.talent.rv.infrastructure.service.file;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.yxt.common.pojo.IdName;
import com.yxt.export.bean.FillBean;
import com.yxt.export.handler.DefaultCustomhandler;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;


@Slf4j
public class ExcelUtils {
    
    /**
     * 导出excel - 动态表头 & 动态页
     *
     * @param headers  数据头 表单的id 即为 获取数据头的key
     * @param sheets   表单
     * @param data     数据体 表单的id 即为 获取数据体的key
     * @param filePath 文件导出的的地址路径 example：xxx/xx/xx.xslx
     * @throws IOException
     */
    public static void exportWithDynamicHeader(
        Map<String, List<List<String>>> headers, List<IdName> sheets, Map<String, List<Object>> data,
        String filePath) throws IOException {
        exportWithDynamicHeader(headers, null, sheets, data, filePath);
    }

    /**
     * 导出excel - 动态表头 & 动态页 & 动态数据列
     *
     * @param headers             数据头 表单的id 即为 获取数据头的key
     * @param dynamicColumnFields 动态数据列字段列表
     * @param sheets              表单
     * @param data                数据体 表单的id 即为 获取数据体的key
     * @param filePath            文件导出的的地址路径 example：xxx/xx/xx.xslx
     * @throws IOException
     */
    public static void exportWithDynamicHeader(
        Map<String, List<List<String>>> headers, Collection<String> dynamicColumnFields, List<IdName> sheets,
        Map<String, List<Object>> data, String filePath) throws IOException {
        File file = new File(filePath);
        boolean fileExist = file.exists();
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }
            fileExist = file.createNewFile();
        }
        if (fileExist & CollectionUtils.isNotEmpty(sheets)) {
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();
            DefaultCustomhandler defaultCustomhandler = new DefaultCustomhandler();

            ExcelWriter excelWriter = null;
            if (dynamicColumnFields == null || dynamicColumnFields.isEmpty()) {
                excelWriter = EasyExcel.write(new FileOutputStream(file)).build();
            } else {
                excelWriter =
                    EasyExcel.write(new FileOutputStream(file)).includeColumnFiledNames(dynamicColumnFields).build();
            }
            for (int i = 0; i < sheets.size(); i++) {
                IdName sheet = sheets.get(i);
                List<List<String>> header = headers.get(sheet.getId());
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheet.getName())
                    .head(header)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(defaultCustomhandler)
                    .build();
                List<Object> list = data.get(sheet.getId());
                if (CollectionUtils.isNotEmpty(list)) {
                    excelWriter.write(list, writeSheet);
                }
            }
            excelWriter.finish();
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

    /**
     * 导出excel-用模板的形式
     *
     * @param headers      数据头，在模板中用{xxx}的形式标识
     * @param data         数据体，在模板中用{.xxx}的形式标识
     * @param filePath     文件导出的的地址路径 example：xxx/xx/xx.xslx
     * @param templatePath 模板的的的地址路径 example：xxx/xx/xx.xslx
     * @throws IOException
     */
    public static void exportWithTemplate(
        Map<String, Object> headers, Object data, String filePath, String templatePath) throws IOException {
        exportWithTemplate(headers, data, filePath, new ClassPathResource(templatePath).getInputStream());
    }


    /**
     * 导出excel-用模板的形式
     *
     * @param headers             数据头，在模板中用{xxx}的形式标识
     * @param data                数据体，在模板中用{.xxx}的形式标识
     * @param filePath            文件导出的的地址路径 example：xxx/xx/xx.xslx
     * @param templateInputStream InputStream 模板的的的输入流
     * @throws IOException
     */
    public static void exportWithTemplate(
        Map<String, Object> headers, Object data, String filePath, InputStream templateInputStream) throws IOException {
        // 创建文件对象
        File file = new File(filePath);
        boolean fileExist = file.exists();
        // 如果文件不存在则新建文件
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }
            fileExist = file.createNewFile();
        }
        if (fileExist) {
            ExcelWriter excelWriter =
                EasyExcel.write(new FileOutputStream(file)).withTemplate(templateInputStream).build();

            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(data, fillConfig, writeSheet);
            if (headers != null) {
                excelWriter.fill(headers, writeSheet);
            }
            excelWriter.finish();
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

    /**
     * 导出excel-用模板的形式填充多sheet
     *
     * @param exportData   多sheet的导出的数据，多sheet的导出为list的顺序
     * @param filePath     文件导出的的地址路径 example：xxx/xx/xx.xslx
     * @param templatePath 模板的的的地址路径 example：xxx/xx/xx.xslx
     * @throws IOException
     */
    public static void exportWithTemplate(List<FillBean> exportData, String filePath, String templatePath) throws
        IOException {
        // 创建文件对象
        File file = new File(filePath);
        boolean fileExist = file.exists();
        // 如果文件不存在则新建文件
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }
            fileExist = file.createNewFile();
        }
        if (fileExist) {
            ExcelWriter excelWriter = EasyExcel.write(new FileOutputStream(file))
                .withTemplate(new ClassPathResource(templatePath).getInputStream())
                .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            for (int i = 0; i < exportData.size(); i++) {
                FillBean cuExportBean = exportData.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i).build();
                excelWriter.fill(cuExportBean.getData(), fillConfig, writeSheet);
                if (exportData.get(i).getHeader() != null) {
                    excelWriter.fill(cuExportBean.getHeader(), writeSheet);
                }
            }
            excelWriter.finish();
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

    /**
     * 根据url地址，获取文件输入流
     *
     * @param downloadUrl 远程url地址
     * @return 文件输入流 InputStream
     */
    public static InputStream getRemoteInputStream(String downloadUrl) throws IOException {
        URL url = new URL(downloadUrl);
        log.info("getRemoteInputStream downloadUrl {} ", downloadUrl);
        // 创建url连接;
        HttpURLConnection urlconn = (HttpURLConnection) url.openConnection();
        // 链接远程服务器;
        urlconn.connect();
        InputStream fis = urlconn.getInputStream();
        return fis;
    }

    /**
     * 读取excel文件 支持合并单元格
     *
     * @param stream
     * @param sheetIndex    sheet页下标:从0开始
     * @param startReadLine 开始读取的行:从0开始
     */
    public static List<Map<Integer, String>> readExcelIfMergedRegion(
        InputStream stream, int sheetIndex, int startReadLine) {
        ArrayList<Map<Integer, String>> result = new ArrayList<>();
        try (Workbook wb = WorkbookFactory.create(stream)) {
            Sheet sheet = wb.getSheetAt(sheetIndex);
            Row row;
            // 逐行读取
            for (int i = startReadLine; i <= sheet.getLastRowNum(); i++) {
                row = sheet.getRow(i);
                if (null == row) {
                    continue;
                }
                Map<Integer, String> map = new HashMap<>(8);
                for (Cell c : row) {
                    String returnStr;
                    boolean isMerge = isMergedRegion(sheet, i, c.getColumnIndex());
                    // 判断是否具有合并单元格
                    if (isMerge) {
                        returnStr = getMergedRegionValue(sheet, row.getRowNum(), c.getColumnIndex());
                    } else {
                        returnStr = getCellValue(c);
                    }
                    map.put(c.getColumnIndex(), returnStr);
                }
                result.add(map);
            }
        } catch (Exception e) {
            log.error("LOG62520:Excel解析异常", e);
        }
        return result;
    }

    /**
     * 获取合并单元格的值
     *
     * @param sheet
     * @param row
     * @param column
     */
    @Nullable
    private static String getMergedRegionValue(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress ca = sheet.getMergedRegion(i);
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();
            if (row >= firstRow && row <= lastRow && column >= firstColumn && column <= lastColumn) {
                Row fRow = sheet.getRow(firstRow);
                Cell fCell = fRow.getCell(firstColumn);
                return getCellValue(fCell);
            }
        }
        return null;
    }

    /**
     * 判断指定的单元格是否是合并单元格
     *
     * @param sheet
     * @param row    行下标
     * @param column 列下标
     */
    private static boolean isMergedRegion(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (row >= firstRow && row <= lastRow && column >= firstColumn && column <= lastColumn) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取单元格的值
     *
     * @param cell
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        if (cell.getCellTypeEnum() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellTypeEnum() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellTypeEnum() == CellType.FORMULA) {
            return cell.getCellFormula();
        } else if (cell.getCellTypeEnum() == CellType.NUMERIC) {
            // 当excel里单元格的内容为纯数字 例如 '123’或 ‘12345678’ 时，
            // 读取后，会变成 123.0 或科学计数法。
            // 读取前将单元格设置为文本类型读取
            cell.setCellType(CellType.STRING);
            return cell.getStringCellValue();
        }
        return "";
    }


    /**
     * 动态表头导出excel，非模版导出 && 导出的excel按照指定的索引（themeCol）列进行单元格合并
     *
     * @param headers
     * @param data
     * @param filePath
     * @param themeCol
     * @throws IOException
     */
    public static void exportWithDynamicHeaderAndCellMerge(
        Map<String, List<List<String>>> headers, List<IdName> sheets, Map<String, List<Object>> data, String filePath,
        int... themeCol) throws IOException {
        doExportWithDynamicHeaderAndCellMerge(headers, sheets, data, filePath, themeCol);
    }

    private static void doExportWithDynamicHeaderAndCellMerge(
        Map<String, List<List<String>>> headers, List<IdName> sheets, Map<String, List<Object>> data, String filePath,
        int... themeCol) throws IOException {
        File file = new File(filePath);
        boolean fileExist = file.exists();
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                //noinspection ResultOfMethodCallIgnored
                file.getCanonicalFile().getParentFile().mkdirs(); // NOSONAR
            }

            fileExist = file.createNewFile();
        }

        if (fileExist && CollectionUtils.isNotEmpty(sheets)) {
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = getHorizontalCellStyleStrategy();
            DefaultCustomhandler defaultCustomhandler = new DefaultCustomhandler();

            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcelFactory.write(new FileOutputStream(file)).build();
                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                for (int i = 0; i < sheets.size(); i++) {
                    IdName sheet = sheets.get(i);
                    List<List<String>> header = headers.get(sheet.getId());
                    WriteSheet writeSheet = EasyExcelFactory.writerSheet(i, sheet.getName())
                        .head(header)
                        .registerWriteHandler(horizontalCellStyleStrategy)
                        .registerWriteHandler(defaultCustomhandler)
                        .build();
                    List<Object> list = data.get(sheet.getId());
                    if (CollectionUtils.isNotEmpty(list)) {
                        excelWriter.write(list, writeSheet);
                    }
                    doMergeCells(workbook.getSheetAt(i), themeCol);
                }
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

    @SuppressWarnings("java:S3776")
    private static void doMergeCells(Sheet sheet, int... themeCol) {
        Map<Integer, Map<String, List<Integer>>> columnValuesMap = new HashMap<>();
        int lastColumnIndex = sheet.getRow(sheet.getFirstRowNum()).getLastCellNum() - 1;
        int firstTheme = Integer.MAX_VALUE;

        Map<Integer, Map<Integer, String>> themeRowVal = new HashMap<>();
        if (null == themeCol || themeCol.length == 0) {
            Map<Integer, String> col1RowVal = new HashMap<>();
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row currentRow = sheet.getRow(i);
                if (currentRow != null) {
                    Cell currentCell = currentRow.getCell(0);
                    col1RowVal.put(i, getCellValue(currentCell));
                }

            }
            themeRowVal.put(0, col1RowVal);
            firstTheme = 0;
        } else {
            for (int number : themeCol) {
                Map<Integer, String> colRowVal = new HashMap<>();
                for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                    Row currentRow = sheet.getRow(i);
                    if (currentRow != null) {
                        Cell currentCell = currentRow.getCell(number - 1);
                        colRowVal.put(i, getCellValue(currentCell));
                    }
                }
                themeRowVal.put(number - 1, colRowVal);
                firstTheme = Math.min(number - 1, firstTheme);
            }
        }

        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row currentRow = sheet.getRow(i);
            Row previousRow = sheet.getRow(i - 1);
            if (currentRow != null && previousRow != null) {
                for (int j = 0; j <= lastColumnIndex; j++) {
                    Cell currentCell = currentRow.getCell(j);
                    Cell previousCell = previousRow.getCell(j);

                    StringBuilder currentTheme = new StringBuilder();
                    StringBuilder previousTheme = new StringBuilder();
                    for (Map.Entry<Integer, Map<Integer, String>> map : themeRowVal.entrySet()) {
                        Integer col = map.getKey();
                        if (col <= j) {
                            currentTheme.append(map.getValue().get(i));
                            previousTheme.append(map.getValue().get(i - 1));
                        }
                    }

                    // 处理第一列不是主题列的情况
                    if (StringUtils.isBlank(currentTheme) && StringUtils.isBlank(previousTheme)) {
                        currentTheme.append(themeRowVal.get(firstTheme).get(i));
                        previousTheme.append(themeRowVal.get(firstTheme).get(i - 1));
                    }

                    if (currentCell != null && previousCell != null) {
                        String currCellVal = getCellValue(currentCell);
                        String previousCellVal = getCellValue(previousCell);

                        if ((currCellVal + currentTheme).equals(previousCellVal + previousTheme)) {
                            if (!columnValuesMap.containsKey(j)) {
                                columnValuesMap.put(j, new HashMap<>());
                            }
                            Map<String, List<Integer>> columnValues = columnValuesMap.get(j);
                            String cellValue = getCellValue(currentCell);
                            if (!columnValues.containsKey(cellValue + currentTheme)) {
                                columnValues.put(cellValue + currentTheme, new ArrayList<>());
                            }
                            List<Integer> rows = columnValues.get(cellValue + currentTheme);
                            rows.add(i - 1);
                        }
                    }
                }
            }
        }

        Map<Integer, List<CellRangeAddress>> mergeRanges = new HashMap<>();
        for (int columnIndex = 0; columnIndex <= lastColumnIndex; columnIndex++) {
            if (!columnValuesMap.containsKey(columnIndex)) {
                continue;
            }

            Map<String, List<Integer>> columnValues = columnValuesMap.get(columnIndex);
            for (List<Integer> rows : columnValues.values()) {
                if (!mergeRanges.containsKey(columnIndex)) {
                    mergeRanges.put(columnIndex, new ArrayList<>());
                }

                List<List<Integer>> rowList = splitNumbers(rows);
                for (List<Integer> spliterows : rowList) {
                    mergeRanges.get(columnIndex)
                        .add(new CellRangeAddress(spliterows.get(0), spliterows.get(spliterows.size() - 1) + 1,
                            columnIndex, columnIndex));
                }
            }
        }

        for (List<CellRangeAddress> ranges : mergeRanges.values()) {
            for (CellRangeAddress range : ranges) {
                sheet.addMergedRegion(range);
            }
        }
    }

    public static List<List<Integer>> splitNumbers(List<Integer> numbers) {
        List<List<Integer>> result = new ArrayList<>();
        if (numbers.isEmpty()) {
            return result;
        }

        List<Integer> currentSequence = new ArrayList<>();
        currentSequence.add(numbers.get(0));
        for (int i = 1; i < numbers.size(); i++) {
            int current = numbers.get(i);
            int prev = numbers.get(i - 1);
            if (current != prev + 1) {
                result.add(new ArrayList<>(currentSequence));
                currentSequence.clear();
            }
            currentSequence.add(current);
        }
        result.add(new ArrayList<>(currentSequence));

        return result;
    }

    /**
     * copy from {@link com.yxt.export.ExcelUtil}
     */
    private static HorizontalCellStyleStrategy horizontalCellStyleStrategy;

    private static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        if (horizontalCellStyleStrategy == null) {
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setWrapped(false);
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            headWriteCellStyle.setTopBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headWriteCellStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headWriteCellStyle.setLeftBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headWriteCellStyle.setRightBorderColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteFont.setFontName("微软雅黑");
            headWriteCellStyle.setWriteFont(headWriteFont);

            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(false);
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 10);
            contentWriteFont.setFontName("微软雅黑");
            contentWriteCellStyle.setWriteFont(contentWriteFont);

            horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        }
        return horizontalCellStyleStrategy;
    }


    /**
     * 处理sheetName中存在的特殊字符，长度超过31个字符，则截断，并替换特殊字符为空字符串
     *
     * @param sheetName
     * @return
     */
    public static String sanitizeSheetName(String sheetName) {
        if (sheetName == null || sheetName.isEmpty()) {
            return "Sheet";
        }

        // 替换非法字符为空字符串
        String sanitized = sheetName.replaceAll("[\\\\/:\\*\\?\\[\\]\\']|(?<!\\.)\\.(?!\\.)", "");

        // 截断到最大长度 31 个字符
        if (sanitized.length() > 31) {
            sanitized = sanitized.substring(0, 31);
        }

        // 如果名称为空，则返回默认名称
        if (sanitized.isEmpty()) {
            return "Sheet";
        }

        return sanitized;
    }

}
