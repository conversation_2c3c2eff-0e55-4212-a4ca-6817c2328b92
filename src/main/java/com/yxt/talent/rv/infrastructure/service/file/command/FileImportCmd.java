package com.yxt.talent.rv.infrastructure.service.file.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件导入时的统一入参")
public class FileImportCmd implements Command {

    @Schema(description = "导入文件ID")
    private String fileId;

    @Schema(description = "主体目标ID")
    private String targetId;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
