package com.yxt.talent.rv.infrastructure.service.file.dto;

import com.yxt.common.pojo.IdName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 动态表头：导出excel文件的内容
 * 支持动态表头
 * 支持多sheet
 */
@Setter
@Getter
@ToString
public class DynamicExcelExportContent {

    // 表头,支持多sheet导出: key为sheet名称(注意需要和sheets中的IdName.id保持一致), value为表头集合
    private Map<String, List<List<String>>> headers;

    // sheet名称, 支持多sheet
    private List<IdName> sheets;

    // 具体导出的数据, key为sheet名称(注意需要和sheets中的IdName.id保持一致), value为数据集合
    private Map<String, List<Object>> data;

    // 提供下拉框使用,
    private Map<String, List<String>> fillData;
}
