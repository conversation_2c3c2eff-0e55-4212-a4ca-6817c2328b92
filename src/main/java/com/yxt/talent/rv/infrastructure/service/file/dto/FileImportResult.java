package com.yxt.talent.rv.infrastructure.service.file.dto;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.GenericImportResult;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class FileImportResult extends GenericImportResult {

    // 设计模式：创建一个空对象
    public static final FileImportResult EMPTY = new FileImportResult(-1, -1, -1, "");

    private String filePath;
    private String errorFileUrl;
    // 1-无效的导入模版，2-模版数据格式错误，默认值0表示无错误
    private int errorCode;
    private String errorFileId;

    public void setFilePath(String filePath) {
        this.filePath = filePath;
        this.errorFileUrl = filePath;
    }

    public FileImportResult(int totalCount, int failedCount, int successCount, String filePath) {
        super(totalCount, failedCount, failedCount, successCount);
        this.filePath = filePath;
        this.errorFileUrl = filePath;
    }
}
