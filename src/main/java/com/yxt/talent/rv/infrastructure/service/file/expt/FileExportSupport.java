package com.yxt.talent.rv.infrastructure.service.file.expt;

import com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 导出上下文支持类,用于传递一些导出相关的参数,不包括具体的导出文件内容
 */
@Setter
@Getter
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FileExportSupport extends TransferSupport {

    /**
     * 导出文件名, 务必不能为空
     * 【注意】这里不许添加 --Orig 后缀，否则会导致二开混布有问题， 统一个 AbstractExportStrategy 中做了处理
     */
    @NonNull
    private String fileName;

}
