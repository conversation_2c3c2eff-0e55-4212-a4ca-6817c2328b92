package com.yxt.talent.rv.infrastructure.service.file.expt;

import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.core.ExportBase;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * ubiz-export封装不够理想，除非简单的大数据量导出，否则不推荐使用该方式导出，尽量采用原waf-export中提供的导出方案
 * @see FileExportSupportWithWaf
 */
@Setter
@Getter
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FileExportSupportWithUbiz extends FileExportSupport {

    @NonNull
    private ExportFileInfo exportFileInfo;

    @NonNull
    private ExportBase exportBase;

}
