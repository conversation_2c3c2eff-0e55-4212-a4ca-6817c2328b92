package com.yxt.talent.rv.infrastructure.service.file.expt;

import com.yxt.export.OutputStrategy;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FileExportSupportWithWaf extends FileExportSupport {

    // 导出文件处理策略: 当采用ExportVersions.UBIZ_EXPORT导出时，无需提供策略类
    @Nullable
    private OutputStrategy outputStrategy;

    // 导出的具体数据
    @NonNull
    private Object exportContent;

}
