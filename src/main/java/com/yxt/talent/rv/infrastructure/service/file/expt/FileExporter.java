package com.yxt.talent.rv.infrastructure.service.file.expt;

import com.google.common.base.Preconditions;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.IdName;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.rv.infrastructure.common.transfer.GenericTransfer;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import com.yxt.ubiz.export.bean.DownInfo;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.bean.ExportParam;
import com.yxt.ubiz.export.core.AbstractExportWrapper;
import com.yxt.ubiz.export.core.ExportBase;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.service.file.FileConstants.SHEET_1;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * 文件导出抽象类
 */
public abstract class FileExporter extends GenericTransfer<FileExportSupport, GenericFileExportVO> {
    private static final Logger log = LoggerFactory.getLogger(FileExporter.class);

    /* 导出模块code */
    public static final String MODULE_CODE = "talentRvProject";
    /* 子应用code(ote,udp) */
    public static final String APP_CODE = "gwnl";
    /* 平台标识（10:金牌团队，20:机构网校，40:商城，50:绚星企业大学） */
    public static final String SOURCE_CODE = "50";

    /**
     * 导出文件通用方法,应用内所有文件导出的通用入口类
     *
     * @param fileExportSupport
     * @return
     */
    @Nonnull
    protected GenericFileExportVO toExport(FileExportSupport fileExportSupport) {
        return handle(fileExportSupport, this::doExport);
    }

    @Nonnull
    protected GenericFileExportVO doExport(FileExportSupport fileExportSupport) {
        GenericFileExportVO genericFileExportVO;
        if (fileExportSupport instanceof FileExportSupportWithWaf fileExportSupportWithWaf) {
            // 原来老的waf-export中提供的导出方案
            genericFileExportVO = doExportByWafExport(fileExportSupportWithWaf);
        } else if (fileExportSupport instanceof FileExportSupportWithUbiz fileExportSupportWithUbiz) {
            // 新的ubiz-export中提供的导出方案
            genericFileExportVO = doExportByUbizExport(fileExportSupportWithUbiz);
        } else {
            genericFileExportVO = new GenericFileExportVO();
        }
        return genericFileExportVO;
    }

    private GenericFileExportVO doExportByUbizExport(FileExportSupportWithUbiz fileExportSupport) {
        ExportBase exportBase = fileExportSupport.getExportBase();
        AbstractExportWrapper exportWrapper = wrapAbstractExport(exportBase);
        ExportFileInfo exportFileInfo = fileExportSupport.getExportFileInfo();
        if (log.isInfoEnabled()) {
            log.info("LOG11645:{}", BeanHelper.bean2Json(exportFileInfo, ALWAYS));
        }
        return buildExportResult(EMPTY, exportWrapper.export(exportFileInfo));
    }

    @Nonnull
    private static AbstractExportWrapper wrapAbstractExport(ExportBase exportBase) {
        return new AbstractExportWrapper() {
            @Override
            public Map<String, String> getExportHeader(Object customParam) {
                return exportBase.getExportHeader(customParam);
            }

            @Override
            public Map<String, String> getExportHeader(Object customParam, int sheetNum) {
                return exportBase.getExportHeader(customParam, sheetNum);
            }

            @Override
            public void loadData(
                Object queryParams, BiConsumer<List<?>, ExportParam> consumerList) {
                exportBase.loadData(queryParams, consumerList);
            }
        };
    }

    private GenericFileExportVO doExportByWafExport(FileExportSupportWithWaf fileExportSupport) {
        String fileName = fileExportSupport.getFileName();
        OutputStrategy outputStrategy = fileExportSupport.getOutputStrategy();
        @NonNull Object exportContentList = fileExportSupport.getExportContent();

        DlcComponent dlcComponent = getDlcComponent();
        long taskId = dlcComponent.prepareExport(fileName, outputStrategy);
        String path = dlcComponent.upload2Disk(fileName, exportContentList, outputStrategy, taskId);
        return buildExportResult(path, -1L);
    }

    protected GenericFileExportVO buildExportResult(String path, Long exportTaskId) {
        return new GenericFileExportVO(path, exportTaskId);
    }

    @Nonnull
    protected DlcComponent getDlcComponent() {
        DlcComponent dlcComponent = SpringContextHolder.getBean("dlcComponent", DlcComponent.class);
        Validate.isNotNull(dlcComponent, "dlcComponent is null");
        return dlcComponent;
    }

    /**
     * 当导出的文件只有一个sheet时，可以使用此方法
     *
     * @param sheetId
     * @param sheetName
     * @return
     */
    @Nonnull
    public static List<IdName> buildSingleSheets(String sheetId, String sheetName) {
        Preconditions.checkArgument(sheetId != null, "sheetId不能为空");
        Preconditions.checkArgument(sheetName != null, "sheetName不能为空");
        List<IdName> sheets = new ArrayList<>();
        IdName idName = new IdName();
        idName.setId(sheetId);
        idName.setName(sheetName);
        sheets.add(idName);
        return sheets;
    }

    /**
     * 当导出文件涉及多个sheet时,可以使用此方法
     */
    @Nonnull
    @SuppressWarnings(value = {"java:S1319", "java:S112"})
    public static List<IdName> buildMultiSheets(LinkedHashMap<String, String> sheetIdNames) {
        if (sheetIdNames == null || sheetIdNames.isEmpty()) {
            throw new RuntimeException("sheetIdNames不能为空");
        }
        List<IdName> sheets = new ArrayList<>();
        sheetIdNames.forEach((k, v) -> {
            IdName idName = new IdName();
            idName.setId(k);
            idName.setName(v);
            sheets.add(idName);
        });
        return sheets;
    }

    @Nonnull
    public static DownInfo buildDownInfo() {
        DownInfo downInfo = new DownInfo();
        downInfo.setAppCode(APP_CODE);
        downInfo.setModuleCode(MODULE_CODE);
        downInfo.setSourceCode(SOURCE_CODE);
        downInfo.setUseOriginFileName(true);
        return downInfo;
    }

    /**
     * 填充固定表头
     * @return
     */
    public static Map<String, List<List<String>>> initFixedHeaders(String headerPrefix, String[] headerKeys) {
        I18nComponent i18nComponent = SpringContextHolder.getBean("i18nComponent", I18nComponent.class);
        Validate.isNotNull(i18nComponent, "i18nComponent is null");

        Map<String, List<List<String>>> results = new LinkedHashMap<>();
        List<List<String>> headers = new ArrayList<>();
        // 处理固定表头
        for (String key : headerKeys) {
            List<String> fixedHeaders = new ArrayList<>();
            fixedHeaders.add(i18nComponent.getI18nValue(headerPrefix + key));
            headers.add(fixedHeaders);
        }
        results.put(SHEET_1, headers);
        return results;
    }

}
