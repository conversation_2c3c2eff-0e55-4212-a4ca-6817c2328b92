package com.yxt.talent.rv.infrastructure.service.file.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.yxt.common.exception.ApiException;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.transfer.ProcessedResult;
import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 默认的excel数据读取器，支持从文件流和文件中心读取数据，
 * 要求导入数据的实体类必须继承FileImportContent，且字段需要与excel列名一一对应
 */
@Slf4j
@RequiredArgsConstructor
public class DefaultFileReader<T extends ImportContent, U extends ProcessedResult<T>, R extends FileImportResult>
        extends FileReader<T> {

    private final FileImportSupport<T, U, R> fileImportSupport;

    @Override
    @jakarta.annotation.Nonnull
    public List<T> read() {
        MultipartFile file = fileImportSupport.getFile();
        String fileId = fileImportSupport.getFileId();
        try (InputStream inputStream = getInputStream(file, fileId)) {
            return EasyExcelFactory.read(inputStream)
                    .headRowNumber(fileImportSupport.getStartRow())
                    .head(fileImportSupport.getImportContentClazz())
                    .autoTrim(Boolean.TRUE)
                    .sheet()
                    .doReadSync();
        } catch (Exception e) {
            log.error("LOG11115:", e);
            throw new ApiException(ExceptionKeys.TRANSFER_IMPORT_FILE_READ_ERROR);
        }
    }

}
