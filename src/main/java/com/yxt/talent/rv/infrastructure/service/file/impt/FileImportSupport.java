package com.yxt.talent.rv.infrastructure.service.file.impt;

import com.yxt.export.OutputStrategy;
import com.yxt.talent.rv.infrastructure.common.transfer.ProcessedResult;
import com.yxt.talent.rv.infrastructure.common.transfer.impt.GeneticImportSupport;
import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import lombok.*;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
public class FileImportSupport<T extends ImportContent, U extends ProcessedResult<T>, R extends FileImportResult>
        extends GeneticImportSupport<T, U, R> {

    /**
     * 使用文件流上传
     */
    private MultipartFile file;

    /**
     * 使用文件id上传
     */
    private String fileId;

    /**
     * 读取excel的起始行, 默认值从第三行开始读取(第一行说明, 第二行标题, 第三行正式数据)
     */
    @Builder.Default
    private int startRow = 1;

    /**
     * 错误数据导出时的文件名
     */
    @jakarta.annotation.Nullable
    private String errorFileName;

    /**
     * 错误数据导出时的输出策略
     */
    @jakarta.annotation.Nullable
    private OutputStrategy outputStrategy;

}
