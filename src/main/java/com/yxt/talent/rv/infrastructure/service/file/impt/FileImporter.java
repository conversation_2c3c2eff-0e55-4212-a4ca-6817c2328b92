package com.yxt.talent.rv.infrastructure.service.file.impt;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.common.transfer.Reader;
import com.yxt.talent.rv.infrastructure.common.transfer.impt.GenericImporter;
import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.function.UnaryOperator;

@Slf4j
public abstract class FileImporter<T extends ImportContent, U extends FileProcessedResult<T>, R extends FileImportResult>
        extends GenericImporter<FileImportSupport<T, U, R>, T, U, R> {

    /**
     * 大部分情况下，导入数据时，使用默认的数据提取器，只有少部分动态表头的导入才需要自定义数据提取器
     * 所以这里做一层通用抽象
     *
     * @param fileImportSupport
     * @return
     */
    @jakarta.annotation.Nullable
    @Override
    protected R doImport(FileImportSupport<T, U, R> fileImportSupport) {
        // 读取数据(没有指定数据提取器时,使用默认的提取器)
        Reader<T> dataReader = fileImportSupport.getDataReader();
        if (dataReader == null) {
            fileImportSupport.setDataReader(new DefaultFileReader<>(fileImportSupport));
        }

        // 导出错误数据(没有指定错误处理器时,使用默认的处理器)
        UnaryOperator<U> errorProcessor = fileImportSupport.getErrorProcessor();
        if (errorProcessor == null) {
            fileImportSupport.setErrorProcessor(processedResult -> {
                DlcComponent dlcComponent = getDlcComponent();
                List<T> failedData = processedResult.getFailedData();
                String errorFileName = Objects.requireNonNull(fileImportSupport.getErrorFileName());
                OutputStrategy outputStrategy =
                        Objects.requireNonNull(fileImportSupport.getOutputStrategy());
                String path = dlcComponent.upload2TemporaryDisk(errorFileName, failedData,
                        outputStrategy);
                processedResult.setErrorFilePath(path);
                return processedResult;
            });
        }
        return super.doImport(fileImportSupport);
    }

    @jakarta.annotation.Nonnull
    protected DlcComponent getDlcComponent() {
        DlcComponent dlcComponent = SpringContextHolder.getBean("dlcComponent", DlcComponent.class);
        Validate.isNotNull(dlcComponent, "dlcComponent is null");
        return dlcComponent;
    }

}
