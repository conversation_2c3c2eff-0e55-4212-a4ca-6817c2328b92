package com.yxt.talent.rv.infrastructure.service.file.impt;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.transfer.Reader;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.remote.FileAclService;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

import static java.text.MessageFormat.format;

/**
 * 文件数据提取器
 */
public abstract class FileReader<T> implements Reader<T> {

    @jakarta.annotation.Nonnull
    protected InputStream getInputStream(MultipartFile file, String fileId) throws IOException {
        if (Objects.nonNull(file)) {
            return file.getInputStream();
        } else if (fileId != null) {
            return getFileStreamFromFileCenter(fileId);
        } else {
            throw new ApiException("无效的文件输入");
        }
    }

    // 从文件中心获取文件流的逻辑
    @jakarta.annotation.Nonnull
    protected InputStream getFileStreamFromFileCenter(String fileId) throws IOException {
        String downloadUrl = getFileAclService().getDownloadUrl(fileId,
                ExceptionKeys.TRANSFER_IMPORT_FILE_ID_INVALID);
        if (downloadUrl == null) {
            throw new IOException(format("fileId [{0}] 没有找到对应的下载链接!", fileId));
        }
        return ExcelUtils.getRemoteInputStream(downloadUrl);
    }

    @jakarta.annotation.Nonnull
    protected FileAclService getFileAclService() {
        FileAclService fileAclService = SpringContextHolder.getBean(FileAclService.class);
        Validate.isNotNull(fileAclService, "fileAclService is null");
        return fileAclService;
    }

}
