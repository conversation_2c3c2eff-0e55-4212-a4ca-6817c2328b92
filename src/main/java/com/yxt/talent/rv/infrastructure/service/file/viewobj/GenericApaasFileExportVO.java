package com.yxt.talent.rv.infrastructure.service.file.viewobj;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "文件导出时的统一响应体")
public class GenericApaasFileExportVO extends GenericFileExportVO {

    public static final GenericApaasFileExportVO EMPTY_RESULT = new GenericApaasFileExportVO();

    @Schema(description = "APAAS要求返回的文件路径")
    private String fileUrl = EMPTY;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "下载中心给出来的导出任务id")
    private Long exportTaskId = -1L;

    public static GenericApaasFileExportVO byFilePath(String filePath) {
        GenericApaasFileExportVO genericFileExportVO = new GenericApaasFileExportVO();
        genericFileExportVO.setFileUrl(filePath);
        genericFileExportVO.setFilePath(filePath);
        return genericFileExportVO;
    }

}
