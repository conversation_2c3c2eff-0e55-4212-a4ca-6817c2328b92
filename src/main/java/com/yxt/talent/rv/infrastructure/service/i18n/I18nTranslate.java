package com.yxt.talent.rv.infrastructure.service.i18n;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要进行多语言翻译的字段
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface I18nTranslate {
    /**
     * 多语言编码字段的名称，如果为空则使用字段本身的值作为编码
     */
    String codeField() default "";
    
    /**
     * 默认值字段的名称，如果为空则使用字段本身的值作为默认值
     */
    String defaultValueField() default "";
}
