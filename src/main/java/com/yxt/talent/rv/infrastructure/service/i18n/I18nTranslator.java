package com.yxt.talent.rv.infrastructure.service.i18n;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.L10NTranslator;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.service.MessageSourceService;
import com.yxt.globalfacade.bean.langorg.LangOrg4Batch;
import com.yxt.globalfacade.bean.langorg.LangOrg4Detail;
import com.yxt.globalfacade.service.GlobalFacade;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.persistence.cache.CommonCacheRepository;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.org.OrgBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;

/**
 * 多语言翻译工具类，支持使用注解的方式标识需要翻译的字段
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class I18nTranslator {
    private final L10NTranslator l10nTranslator;
    private final RedisRepository talentRedisRepository;
    private final GlobalFacade globalFacade;
    private final UdpAclService udpAclService;
    private final MessageSourceService messageSourceService;
    private final CommonCacheRepository commonCacheRepository;

    /**
     * 统一的翻译方法，支持业务对象和系统基础信息的翻译, 推荐使用
     * 支持递归处理包含L10NContent对象的字段
     *
     * @param orgId    组织ID
     * @param userLang 用户当前使用的语言code
     * @param obj      需要翻译的对象或对象集合
     */
    public void translate(String orgId, String userLang, Object obj) {
        if (obj == null) {
            return;
        }

        // 处理集合类型
        if (obj instanceof Collection<?> collection) {
            if (!collection.isEmpty()) {
                // 检查集合中的第一个非空元素
                Object firstItem = collection.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (firstItem != null) {
                    // 收集所有需要翻译的字段
                    List<I18nFieldInfo> allI18nFields = new ArrayList<>();
                    collection.forEach(item -> collectI18nFields(item, allI18nFields));
                    
                    // 批量翻译所有字段
                    if (!allI18nFields.isEmpty()) {
                        log.debug("LOG20573:");
                        translateI18nFieldsBatch(allI18nFields, orgId, userLang);
                    }
                    
                    // 如果是L10NContent类型，批量处理
                    if (firstItem instanceof L10NContent) {
                        log.debug("LOG20563:");
                        translateL10NContent(collection, orgId, userLang);
                    }
                }
            }
            return;
        }

        // 处理Map类型
        if (obj instanceof Map<?, ?>) {
            ((Map<?, ?>) obj).values().forEach(value -> translate(orgId, userLang, value));
            return;
        }

        // 处理L10NContent类型
        if (obj instanceof L10NContent) {
            translateL10NContent(obj, orgId, userLang);
            return;
        }

        // 处理普通对象类型
        List<I18nFieldInfo> i18nFields = new ArrayList<>();
        collectI18nFields(obj, i18nFields);
        if (!i18nFields.isEmpty()) {
            translateI18nFieldsBatch(i18nFields, orgId, userLang);
        }
    }

    /**
     * 处理单个多语言对象的便捷方法
     *
     * @param orgId        组织ID
     * @param userLang     用户语言
     * @param code         多语言编码
     * @param defaultValue 默认值
     * @return 翻译后的值
     */
    public String translateSingle(String orgId, String userLang, String code, String defaultValue) {
        Map<String, String> result = translateBatch(orgId, userLang,
            Collections.singletonMap(code, defaultValue));
        return result.getOrDefault(code, defaultValue);
    }

    /**
     * 批量处理多语言翻译
     * 如果一个类中存在多个需要翻译的字段和多语言key时，该方法不满足，推荐使用 {@link #translate(String, String, Object)}
     *
     * @param orgId    组织ID
     * @param userLang 用户语言
     * @param codeMap  多语言编码和默认值的映射
     * @return 翻译后的值映射
     */
    public Map<String, String> translateBatch(
        String orgId, String userLang, Map<String, String> codeMap) {
        if (codeMap == null || codeMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 检查组织是否启用多语言
        OrgBean orgInfo = getOrgInfoWithCache(orgId);
        if (Objects.requireNonNull(orgInfo).getEnableI18n() != YesOrNo.YES.getValue()) {
            // 如果未启用多语言，返回默认值
            return new HashMap<>(codeMap);
        }

        // 标准化语言代码
        String languageCode = messageSourceService.getLocalLanguageCode(userLang);

        // 获取多语言内容映射
        Map<String, String> langContentMap = getLangContentMap(codeMap.keySet(), languageCode, orgId);

        // 返回翻译后的值，如果没有翻译则使用默认值
        return codeMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> langContentMap.getOrDefault(e.getKey(), e.getValue()),
                (existing, replacement) -> existing // 保持原有值
            ));
    }

    /**
     * 检查组织是否启用了国际化
     *
     * @param orgId 组织ID
     * @return 是否启用国际化
     */
    public boolean isEnableLocalization(String orgId) {
        try {
            String cacheKey = String.format(RedisKeys.CK_ORG_LOCALIZATION, orgId);

            // 从缓存中获取状态
            String enableLocalization = (String) talentRedisRepository.opsForHash().get(cacheKey, orgId);
            if(StringUtils.isNotBlank(enableLocalization)){
                log.debug("LOG10459: Using cached localization status for orgId: {}", orgId);
                return "1".equals(enableLocalization);
            }

            // 从服务获取状态
            boolean isEnable = l10nTranslator.isEnableLocalization(orgId);

            // 更新缓存
            String value = isEnable ? "1" : "0";
            talentRedisRepository.opsForHash().put(cacheKey, orgId, value);

            // 设置缓存过期时间（4小时）
            long expirationHours = 4;
            talentRedisRepository.expire(cacheKey, expirationHours, TimeUnit.HOURS);

            log.debug("LOG10460: Updated localization status cache for orgId: {}, status: {}", orgId, isEnable);
            return isEnable;
        } catch (Exception e) {
            log.error("LOG10461: Failed to check localization status for orgId: {}, error: {}", orgId, e.getMessage(), e);
        }
        return false;
    }

    /**
     * 批量翻译字段
     */
    private void translateI18nFieldsBatch(List<I18nFieldInfo> allI18nFields, String orgId, String userLang) {
        // 检查组织是否启用多语言
        OrgBean orgInfo = getOrgInfoWithCache(orgId);
        if (Objects.requireNonNull(orgInfo).getEnableI18n() != YesOrNo.YES.getValue()) {
            return;
        }

        // 标准化语言代码
        String languageCode = messageSourceService.getLocalLanguageCode(userLang);

        // 收集所有需要翻译的codes和默认值
        Map<String, List<I18nFieldInfo>> codeFieldMap = new HashMap<>();
        allI18nFields.forEach(fieldInfo -> {
            codeFieldMap.computeIfAbsent(fieldInfo.getCode(), k -> new ArrayList<>()).add(fieldInfo);
        });

        // 批量获取翻译
        Map<String, String> langContentMap = getLangContentMap(codeFieldMap.keySet(), languageCode, orgId);

        // 设置翻译后的值
        langContentMap.forEach((code, translatedValue) -> {
            List<I18nFieldInfo> fieldInfos = codeFieldMap.get(code);
            if (fieldInfos != null) {
                fieldInfos.forEach(fieldInfo -> {
                    try {
                        fieldInfo.getField().set(fieldInfo.getTargetObject(),
                            translatedValue != null ? translatedValue : fieldInfo.getDefaultValue());
                    } catch (Exception e) {
                        log.error("LOG10442:Failed to set translated value for field: {}",
                            fieldInfo.getField().getName(), e);
                    }
                });
            }
        });
    }

    /**
     * 批量翻译L10N内容
     *
     * @param obj L10NContent对象或其集合
     * @param orgId 组织ID
     * @param userLang 用户语言
     * @param <T> L10NContent类型
     */
    @SuppressWarnings("unchecked")
    private <T extends L10NContent> void translateL10NContent(Object obj, String orgId, String userLang) {
        try {
            if (!isEnableLocalization(orgId)) {
                log.debug("LOG10453: Organization {} has not enabled localization", orgId);
                return;
            }

            List<T> dataList;
            Class<T> type;

            if (obj instanceof Collection<?> collection) {
                if (collection.isEmpty()) {
                    log.debug("LOG10454: Empty collection provided for translation");
                    return;
                }
                // 获取集合中第一个非空元素的类型
                T firstItem = (T) collection.stream()
                        .filter(item -> item instanceof L10NContent)
                        .findFirst()
                        .orElse(null);
                if (firstItem == null) {
                    log.debug("LOG10455: No valid L10NContent items found in collection");
                    return;
                }
                type = (Class<T>) firstItem.getClass();
                dataList = collection.stream()
                        .filter(item -> item instanceof L10NContent)
                        .map(item -> (T) item)
                        .collect(Collectors.toList());

                log.debug("LOG10456: Processing {} L10NContent items for translation", dataList.size());
            } else {
                type = (Class<T>) obj.getClass();
                dataList = List.of((T) obj);
                log.debug("LOG10457: Processing single L10NContent item for translation");
            }

            if (!dataList.isEmpty()) {
                String languageCode = messageSourceService.getLocalLanguageCode(userLang);
                l10nTranslator.translate(List.of(orgId), languageCode, type, dataList);
                log.debug("LOG10458: Successfully translated L10NContent items for orgId: {}", orgId);
            }
        } catch (Exception e) {
            log.error("LOG10452: Failed to translate L10N content for orgId: {}, error: {}", orgId, e.getMessage(), e);
        }
    }

    /**
     * 获取多语言内容映射
     */
    private Map<String, String> getLangContentMap(
            Collection<String> codes, String languageCode, String orgId) {
        Map<String, String> langContentMap = Maps.newHashMap();
        codes = filterNullAndBlank(codes);
        if (codes.isEmpty()) {
            log.debug("LOG10422: No i18n codes to process");
            return langContentMap;
        }

        LangOrg4Batch langOrg4Batch = new LangOrg4Batch();
        langOrg4Batch.setOrgId(orgId);
        langOrg4Batch.setKeys(Lists.newArrayList(codes));

        LangOrg4Detail langOrg4Detail = globalFacade.orgBatchSearchKeys(langOrg4Batch);
        if (langOrg4Detail == null || langOrg4Detail.getKeys() == null) {
            log.debug("LOG10412: No translations found");
            return langContentMap;
        }

        // 获取最终的多语言翻译文本
        langOrg4Detail.getKeys()
                .stream()
                .filter(key -> key.getTrans().containsKey(languageCode))
                .forEach(key -> langContentMap.put(key.getLangKey(), key.getTrans().get(languageCode)));

        return langContentMap;
    }

    /**
     * 递归收集所有需要翻译的字段
     */
    private void collectI18nFields(Object obj, List<I18nFieldInfo> i18nFields) {
        collectI18nFields(obj, i18nFields, new HashSet<>());
    }

    private void collectI18nFields(Object obj, List<I18nFieldInfo> i18nFields, Set<Object> visited) {
        if (obj == null || visited.contains(obj)) {
            return;
        }

        Class<?> clazz = obj.getClass();

        // 如果是基本类型或字符串，直接返回
        if (clazz.isPrimitive() || obj instanceof String || obj instanceof Number || obj instanceof Boolean) {
            return;
        }

        visited.add(obj);

        // 处理集合类型
        if (obj instanceof Collection<?>) {
            ((Collection<?>) obj).forEach(item -> collectI18nFields(item, i18nFields, visited));
            return;
        }

        // 处理Map类型
        if (obj instanceof Map<?, ?>) {
            ((Map<?, ?>) obj).values().forEach(value -> collectI18nFields(value, i18nFields, visited));
            return;
        }

        // 获取所有字段（包括父类的字段）
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                try {
                    // 检查字段是否可以被访问
                    if (!isFieldAccessible(field)) {
                        continue;
                    }
                    
                    field.setAccessible(true);

                    // 处理带有I18nTranslate注解的字段
                    I18nTranslate annotation = field.getAnnotation(I18nTranslate.class);
                    if (annotation != null) {
                        processAnnotatedField(obj, field, annotation, i18nFields);
                    }

                    // 递归处理字段值
                    Object fieldValue = field.get(obj);
                    collectI18nFields(fieldValue, i18nFields, visited);

                } catch (IllegalAccessException e) {
                    log.error("LOG10443: Failed to access field: {}", field.getName(), e);
                } catch (NoSuchFieldException e) {
                    log.error("LOG10444: Field not found: {}", field.getName(), e);
                } catch (Exception e) {
                    log.debug("LOG10445: Skipping inaccessible field: {}", field.getName());
                }
            }
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 在类及其父类中查找指定名称的字段
     */
    private Field findField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                Field field = currentClass.getDeclaredField(fieldName);
                // 检查字段是否可以被访问
                if (!isFieldAccessible(field)) {
                    currentClass = currentClass.getSuperclass();
                    continue;
                }
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        throw new NoSuchFieldException("LOG10446: Field " + fieldName + " not found or not accessible in class " + clazz.getName() + " or its superclasses");
    }

    /**
     * 处理带有I18nTranslate注解的字段
     */
    private void processAnnotatedField(Object obj, Field field, I18nTranslate annotation, List<I18nFieldInfo> i18nFields)
            throws IllegalAccessException, NoSuchFieldException {
        String code;
        String defaultValue;

        // 获取多语言编码
        if (StringUtils.isNotBlank(annotation.codeField())) {
            Field codeField = findField(obj.getClass(), annotation.codeField());
            code = (String) codeField.get(obj);
        } else {
            code = (String) field.get(obj);
        }

        // 获取默认值
        if (StringUtils.isNotBlank(annotation.defaultValueField())) {
            Field defaultValueField = findField(obj.getClass(), annotation.defaultValueField());
            defaultValue = (String) defaultValueField.get(obj);
        } else {
            defaultValue = (String) field.get(obj);
        }

        i18nFields.add(new I18nFieldInfo(field, obj, code, defaultValue));
    }

    /**
     * 检查字段是否可以被访问
     * @param field 要检查的字段
     * @return 如果字段可以被访问返回true，否则返回false
     */
    private boolean isFieldAccessible(Field field) {
        try {
            int modifiers = field.getModifiers();
            
            // 排除静态字段
            if (java.lang.reflect.Modifier.isStatic(modifiers)) {
                return false;
            }
            
            // 排除final字段
            if (java.lang.reflect.Modifier.isFinal(modifiers)) {
                return false;
            }
            
            // 检查字段是否来自系统模块
            Module module = field.getDeclaringClass().getModule();
            if (module != null && module.isNamed()) {
                return false;
            } 
            
            // 检查字段是否来自java.*包
            if (field.getDeclaringClass().getName().startsWith("java.")) {
                return false;
            }
            
            // 检查字段是否可以被设置为可访问
            return field.trySetAccessible();
        } catch (Exception e) {
            return false;
        }
    }

    private OrgBean getOrgInfoWithCache(String orgId) {
        return commonCacheRepository.cache(
            RedisKeys.ORG_INFO_CACHE_KEY + orgId,
            5,
            TimeUnit.MINUTES,
            () -> Optional.ofNullable(udpAclService.getOrgInfo(orgId)),
            OrgBean.class
        ).orElse(null);
    }

    @Data
    @AllArgsConstructor
    private static class I18nFieldInfo {
        private Field field;
        private Object targetObject;  // 保存字段所属的对象实例
        private String code;
        private String defaultValue;
    }

}
