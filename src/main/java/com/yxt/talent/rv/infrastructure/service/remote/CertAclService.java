package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.cerapifacade.bean.*;
import com.yxt.common.pojo.api.PagingList;
import jakarta.annotation.Nullable;

import java.util.List;

/**
 * 证书服务接口
 */
public interface CertAclService {

    /**
     * 获取证书列表
     */
    @Nullable
    List<CerSimpleInfoBean> getCerList(CerReqBean cerReqBean);

    /**
     * 获取证书颁发记录分页
     */
    @Nullable
    PagingList<IssueListSearchConditionResult> getIssueList(IssueListSearchConditionReq req);

    /**
     * 吊销证书
     */
    void tempRevoke(CerRevoke4ExternalReq req);
}
