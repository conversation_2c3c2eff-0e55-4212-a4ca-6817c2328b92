package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionBean;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermission;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermissionResponse;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import jakarta.annotation.Nullable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

public interface CoreAclService {

    @Nullable
    DataAuthPermissionBean getUserDataPermission(
            String orgId, String userId, String navCode, String dataPermissionCode,
            String productCode);

    List<OrgVerifyFactorBean> verifyOrgFactors(String orgId, List<String> factorCodes);

    @Nullable
    UserDataPermissionResponse verifyUserDataPermission(
            @RequestBody UserDataPermission userDataPermission);

    /**
     * 添加业务角色人员
     *
     * @param productCode
     * @param roleCode
     * @param userIds
     * @param orgId
     * @param createUserId
     * @param createFullName
     */
    void addRoleUserByCode(
            String productCode, String roleCode, Set<String> userIds, String orgId,
            String createUserId, String createFullName);

    String getScanentryURL(String orgId, String pcUrl, String h5Url);
}
