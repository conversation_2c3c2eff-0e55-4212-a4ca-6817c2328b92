package com.yxt.talent.rv.infrastructure.service.remote;


import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;

import java.util.List;
import java.util.Set;

public interface L10nAclService {

    /**
     * @deprecated 该方法已经废弃，请使用{@link com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator}，它已经支持用户名部门名等基础信息的多语言翻译，同时还支持业务字段多语言的翻译
     */
    @Deprecated(since = "5.8")
    boolean isEnableLocalization(String orgId);

    /**
     * @deprecated 该方法已经废弃，请使用{@link com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator}，它已经支持用户名部门名等基础信息的多语言翻译，同时还支持业务字段多语言的翻译
     * @param enableLocalization
     * @param corpOrgIds
     * @param lang
     * @param type
     * @param dataList
     * @param <T>
     * @see com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator
     */
    @Deprecated(since = "5.8")
    <T extends L10NContent> void translateList(
            boolean enableLocalization, List<String> corpOrgIds, String lang, Class<T> type,
            List<T> dataList);

    /**
     * 根据关键字模糊搜索部门/用户名，如果机构没有开通国际化，则返回空集合
     * @param enableLocalization
     * @param corpOrgIds
     * @param resourceType
     * @param searchKey
     * @return
     */
    Set<String> searchContentByKey(
            boolean enableLocalization, List<String> corpOrgIds, ResourceTypeEnum resourceType,
            String searchKey);

}
