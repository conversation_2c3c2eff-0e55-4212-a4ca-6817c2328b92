package com.yxt.talent.rv.infrastructure.service.remote;

import jakarta.annotation.Nullable;

import java.util.List;

public interface OrginitAclService {

    /**
     * 搜索关键词搜索逻辑。当查询用户时应为用户名称、名称拼音或者英文名；当查询部门时应为部门名称或者部门名称拼音
     *
     * @param orgId      查询企业ID
     * @param keyword    搜索关键词
     * @param type       查询类型 1-用户 2-部门
     * @param sourceCode 渠道号
     */
    List<String> searchContactList(String orgId, String keyword, int type, String sourceCode);
}
