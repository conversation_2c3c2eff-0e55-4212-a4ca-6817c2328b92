package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum;
import com.yxt.spevalfacade.bean.evaluation.Evaluation;
import com.yxt.spevalfacade.bean.form.FormUserDevelopResp;
import com.yxt.spevalfacade.bean.form.FormUserDevelopSearchBean;
import com.yxt.spevalfacade.bean.standar.StandarReq;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SpevalAclService {


    List<Evaluation> getBatchEvaluation(StandarReq req);

    List<FormUserDevelopResp> searchUserDevelop(FormUserDevelopSearchBean bean);

    List<String>  getHasExplainEvalIds(String orgId,List<String> evalIds);

    /**
     * idMapKey取值自：{@link DemoIdMapKeyEnum}
     *
     * @param sourceOrgId
     * @param targetOrgId
     * @param idMapKey
     * @return
     */
    Map<String, String> getEntityIdMap(String sourceOrgId, String targetOrgId, String idMapKey);

    Map<String, String> getTypeStrIdName(String orgId, String locale, Collection<String> typeIds);

    Map<Long, String> getTypeIdName(String orgId, String locale, List<Long> typeIds);

    void clearUserRecord(String orgId, String evalId, List<String> userIds);
}
