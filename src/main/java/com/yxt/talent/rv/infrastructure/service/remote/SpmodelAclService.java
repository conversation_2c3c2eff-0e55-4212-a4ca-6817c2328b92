package com.yxt.talent.rv.infrastructure.service.remote;

import com.alibaba.fastjson.JSONObject;
import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spmodel.facade.bean.rule.ExecuteRuleVO;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import jakarta.annotation.Nullable;

import java.util.List;

public interface SpmodelAclService {

    @Nullable
    ExecuteRuleVO getRule(Long id, String orgId, String appCode);

    List<JSONObject> sql(SqlParam param);

    OrgDemoIdMappingVO getOrgDemoIdMappingVO(String orgId);
}

