package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.spsdfacade.bean.spsd.*;
import com.yxt.talent.rv.application.xpd.common.dto.DimTreeDto;
import com.yxt.talent.rv.infrastructure.service.remote.dto.ModelDimDTO;

import java.util.List;
import java.util.Map;

public interface SpsdAclService {

    /**
     * @param sourceOrgId
     * @param targetOrgId
     * @param mapKey      {@link com.yxt.spsdfacade.constants.SdDemoConstants}
     * @return
     */
    Map<String, String> getDemoOrgMapping(String sourceOrgId, String targetOrgId, String mapKey);

    List<IndicatorBaseDto> getModelIndicatorInfoList(String orgId, List<String> indicatorIds);

    List<IndicatorDto> getIndicatorByModelId(String orgId, String modelId);

    /**
     * 获取模型下的所有维度信息，摊平返回
     * @param orgId
     * @param modelId
     * @return
     */
    List<ModelDimDTO> getModelDimInfo(String orgId, String modelId);

    /**
     * 填充对象中的维度名称和指标名称
     * @param obj 需要填充的对象
     * @param orgId 组织ID
     */
    void fillNamesAndI18nNames(Object obj, String orgId);

    List<ModelBase4Facade> getDimInfoList(String orgId, String modelId, List<String> sdDimIds);

    Map<String, ModelBase4Facade> getDimInfoMap(String orgId, String modelId, List<String> sdDimIds);

    List<DimTreeDto> getDimTreeNoIndicator(String orgId, String modelId);

    List<IndicatorDto> getLastIndicators(String orgId, String modelId);

    List<IndicatorDto> getLastIndicators(String orgId, String modelId, List<String> dimIds);

    List<IndicatorDto> getIndicatorInfo(String orgId, String modelId, List<String> indicatorIds);

    Map<String, IndicatorDto> getIndicatorInfoMap(String orgId, String modelId, List<String> indicatorIds);

    ModelInfo getModelInfo(String orgId, String modelId);

    List<IndicatorBaseDto> getIndicatorBaseInfoByIndicatorIds(String orgId, List<String> ids);

    List<IndicatorDto> getLastIndicatorByDims(String orgId, String modelId, String dimId);

    List<IndicatorDto> getLastIndicatorByDims(String orgId, String modelId, List<String> dimIds);

    Map<ModelBase4Facade, List<IndicatorDto>> dimIndicatorMapByDims(
        String orgId, String modelId,
        List<String> sdDimIds);

    List<DimensionList4Get> getBaseDimDetail(String orgId, List<String> allDimIds);

    List<DimensionList4Get> allEnabledBaseDims(String orgId);

    Map<String, String> getIndIdsByIndNums(String orgId, List<String> indNums);
}