package com.yxt.talent.rv.infrastructure.service.remote;

import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.udpfacade.bean.common.IdsBean;
import com.yxt.udpfacade.bean.dept.DeptManagerBean;
import com.yxt.udpfacade.bean.dept.DeptSimpleBean;
import com.yxt.udpfacade.bean.dept.DeptTreeId;
import com.yxt.udpfacade.bean.dept.ManagedDeptBean;
import com.yxt.udpfacade.bean.mq.org.OrgSyncRequest4MqBean;
import com.yxt.udpfacade.bean.mq.org.OrgSyncResponse4MqBean;
import com.yxt.udpfacade.bean.org.OrgBean;
import jakarta.annotation.Nullable;

import java.util.List;

public interface UdpAclService {
    List<DeptManagerBean> checkIsDeptManagers(String orgId, IdsBean bean);

    @Nullable
    DeptSimpleBean findOrgRootDept(String orgId);

    CommonList<DeptTreeId> getDeptTreeIds(String orgId, CommonList<String> bean);

    List<ManagedDeptBean> findManagedDeptUsers(String orgId, String userId);

    CommonList<String> managerSubMember(String orgId, String managerUserId);

    List<String> exchangeSubDeptIds(String orgId, List<String> deptIds);

    @Nullable
    OrgBean getOrgInfo(String orgId);

    @Nullable
    OrgSyncResponse4MqBean syncOrgDatasRequest4Mq(OrgSyncRequest4MqBean bean);

    CommonList<String> getDeptTreeIds(String orgId, String deptId);

    boolean checkIsDeptManager(String orgId, String userId);

    List<IdName> getDeptInfoByIds(String orgId, List<String> deptIds);

    /**
     * 获取指定员工的部门管理者
     * @param orgId
     * @param userId
     * @return
     */
    List<String> getDeptManagers(String orgId, String userId);
}
