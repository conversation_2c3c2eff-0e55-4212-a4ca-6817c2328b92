package com.yxt.talent.rv.infrastructure.service.remote.dto;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class MessageDTO {

    /**
     * 消息模板Code
     */
    private String templateCode;

    /**
     * 机构
     */
    private String orgId;

    /**
     * 业务主键ID *
     */
    private String targetId;

    /**
     * 占位符 Map<{{fullName}},王小明> <br>
     * 消息模板中需要替换的文字，如：尊敬的{{fullName}}您好，您的得分为{{score}}.
     */
    private Map<String, String> placeholderMap = new HashMap<>(8);

    /**
     * 需要通知的人
     */
    private List<String> userIds;

    /**
     * 登录人ID
     */
    private String userId;

    /**
     * 登录人姓名
     */
    private String userFullName;

    /**
     * 域名
     */
    private String domain;

    private String token;
}
