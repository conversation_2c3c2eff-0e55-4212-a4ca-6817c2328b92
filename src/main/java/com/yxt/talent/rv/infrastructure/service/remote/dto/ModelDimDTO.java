package com.yxt.talent.rv.infrastructure.service.remote.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "模型下的维度信息")
public class ModelDimDTO {

    public static final String DIM_PERF_NAME = "绩效";

    @Schema(description = "维度id")
    private String dimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "维度类型")
    private Integer dimType;

    @Nullable
    @Schema(description = "自定义维度名称")
    private String customName;

    @Schema(description = "来源，0-内置，1-自建")
    private Integer sourceType;

    @Schema(description = "类型，0-普通、1-能力、2-技能、3-知识、4-任务")
    private Integer indicatorType;

    @Nullable
    @Schema(description = "名称国际化id")
    private String nameI18n;

    @Nullable
    @Schema(description = "自定义名称国际化id")
    private String customNameI18n;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "父维度id")
    private String parentId;

    @Schema(description = "级别类型，0-父级，1-末级")
    private Integer levelType;

    @Schema(description = "排序")
    private Integer orderIndex;

    /**
     * 是否内置绩效维度
     */
    public boolean isPerfDim() {
        return DIM_PERF_NAME.equals(this.getDimName()) && this.getSourceType() == 0;
    }

}
