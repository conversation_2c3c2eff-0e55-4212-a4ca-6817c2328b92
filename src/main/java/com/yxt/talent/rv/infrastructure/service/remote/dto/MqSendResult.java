package com.yxt.talent.rv.infrastructure.service.remote.dto;


import com.yxt.criteria.Result;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 消息发送结果
 */
@Setter
@Getter
@ToString
@SuperBuilder
public class MqSendResult extends Result<Void> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String msgId;

    private String topic;

}
