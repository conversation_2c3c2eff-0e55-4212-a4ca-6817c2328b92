package com.yxt.talent.rv.infrastructure.service.remote.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.sptalentapifacade.bean.SkillLevelMap4Get;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SpJqDimDTO {

    @Schema(name = "机构id")
    private String orgId;

    @Schema(name = "任职资格id")
    private String jqId;

    @Schema(name = "模板分类ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tplCateId;

    @Schema(name = "模板分类名称")
    private String tplCateName;

    @Schema(name = "根维度分类id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String rootCateId;

    @Schema(name = "根维度分类名称")
    private String rootCateName;

    @Schema(name = "维度分类id")
    private String dimCateId;

    @Schema(name = "维度分类名称")
    private String dimCateName;

    @Schema(name = "维度 ID, 标签跟指标是 Long 类型，能力跟任务是 String 类型")
    private String jqDimId;

    @Schema(name = "维度名称")
    private String jqDimName;

    @Schema(name = "取值类型: 3、普通文本  4、能力模型，5、任务模型，8、备注")
    private Integer rangeType;

    @Schema(name = "维度规则")
    private String ruleId;

    @Schema(name = "排序")
    private Integer firstOrderIndex;

    @Schema(name = "标准等级，存的是标准等级的顺序值，可以根据skillLevelMaps列表通过下标找到对应的维度等级")
    private Integer skillStandLevel;

    @Schema(name = "能力等级列表")
    private List<SkillLevelMap4Get> skillLevelMaps = new ArrayList<>(0);

    @Schema(name = "模板的itemId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long temItemId;

}
