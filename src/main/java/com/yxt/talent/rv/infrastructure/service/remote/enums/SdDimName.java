package com.yxt.talent.rv.infrastructure.service.remote.enums;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 用于标识需要设置人才标准维度组名称的标识
 */
@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SdDimName {

    @AliasFor("id")
    String value() default "";

    /**
     * 用于标识需要设置人才标准维度名称的标识，通过该标识查询到对应的维度名称，必填
     *
     * @return
     */
    String id() default "";

    /**
     * 设置国际化key的字段名
     *
     * @return
     */
    String i18n() default "";

}
