package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.cerapifacade.bean.*;
import com.yxt.cerapifacade.service.CerFacade;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.talent.rv.infrastructure.service.remote.CertAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 证书服务接口实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CertAclServiceImpl implements CertAclService {

    private final CerFacade cerFacade;

    @Override
    public List<CerSimpleInfoBean> getCerList(CerReqBean cerReqBean) {
        try {
            return cerFacade.getCerList(cerReqBean);
        } catch (Exception e) {
            log.error("获取证书列表失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public PagingList<IssueListSearchConditionResult> getIssueList(IssueListSearchConditionReq req) {
        try {
            return cerFacade.getIssueList(req);
        } catch (Exception e) {
            log.error("获取证书颁发记录失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void tempRevoke(CerRevoke4ExternalReq req) {
        try {
            cerFacade.tempRevoke(req);
            log.info(
                "证书吊销成功: orgId={}, cerId={}, issueIds={}",
                req.getOrgId(), req.getCerId(), req.getCerIssueIds());
        } catch (Exception e) {
            log.error("证书吊销失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
