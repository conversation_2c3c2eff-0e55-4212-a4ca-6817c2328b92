package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.coreapi.client.bean.dataauth.CoreExchangeResult;
import com.yxt.coreapi.client.bean.dataauth.DataAuthPermissionBean;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermission;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermissionResponse;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import com.yxt.coreapi.client.bean.shorturl.ShortUrl4Exchange;
import com.yxt.coreapi.client.bean.shorturl.ShortUrl4ExchangeBase;
import com.yxt.coreapi.client.bean.shorturl.UrlPair;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@RequiredArgsConstructor
@Service("coreAclService")
public class CoreAclServiceImpl implements CoreAclService {

    private final CoreApiFacade coreApiFacade;

    /**
     * 获取用户有权限的部门和人员id
     *
     * @param orgId              机构Id
     * @param userId             userId
     * @param navCode            导航code
     * @param dataPermissionCode 数据权限code 不支持使用前端传递的值
     * @return
     */
    @Override
    public DataAuthPermissionBean getUserDataPermission(
            String orgId, String userId, String navCode, String dataPermissionCode,
            String productCode) {
        return coreApiFacade.getUserDataPermission(
                orgId, userId, navCode, dataPermissionCode, productCode);
    }

    /**
     * 校验机构要素
     *
     * @param orgId
     * @param factorCodes
     * @return
     */
    @Override
    public List<OrgVerifyFactorBean> verifyOrgFactors(String orgId, List<String> factorCodes) {
        return Optional.ofNullable(coreApiFacade.verifyOrgFactors(orgId, factorCodes)).orElse(new ArrayList<>());
    }

    /**
     * 校验用户id是否在数据权限内
     *
     * @param userDataPermission
     * @return 符合数据权限的用户id
     */
    @Override
    public UserDataPermissionResponse verifyUserDataPermission(
            UserDataPermission userDataPermission) {
        return coreApiFacade.verifyUserDataPermission(userDataPermission);
    }

    @Override
    public void addRoleUserByCode(
            String productCode, String roleCode, Set<String> userIds, String orgId,
            String createUserId, String createFullName) {
        try {
            coreApiFacade.addRoleUserByCode(
                    productCode, roleCode, userIds, orgId, createUserId, createFullName);
        } catch (Exception e) {
            log.error("LOG67620:{}", userIds, e);
        }
    }


    /**
     * 获取scanentryurl
     *
     * @param orgId 机构号
     * @param pcUrl pc链接
     * @param h5Url h5链接
     * @return 免登授权的短址
     */
    @Override
    public String getScanentryURL(String orgId, String pcUrl, String h5Url) {
        ShortUrl4Exchange shortUrl4Exchange = new ShortUrl4Exchange();
        shortUrl4Exchange.setOrgId(orgId);
        shortUrl4Exchange.setMode(new ShortUrl4ExchangeBase.Mode(1, 0));
        UrlPair urlPair = new UrlPair();
        urlPair.setUrlH5(h5Url);
        urlPair.setUrlPc(pcUrl);
        shortUrl4Exchange.setUrlPair(urlPair);
        try {
            log.info("LOG11675:getScanentryURL input:{}", JSON.toJSONString(shortUrl4Exchange));
            CoreExchangeResult coreExchangeResult = coreApiFacade.exchange(shortUrl4Exchange);
            log.info("LOG11685:getScanentryURL output:{}", JSON.toJSONString(coreExchangeResult));
            return coreExchangeResult.getUrl();
        } catch (Exception e) {
            log.error("LOG12045:getScanentryURL error: {}", e.getMessage());
            return EMPTY;
        }
    }

}
