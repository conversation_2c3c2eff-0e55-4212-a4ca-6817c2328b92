package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.common.exception.ApiException;
import com.yxt.export.DlcComponent;
import com.yxt.talent.rv.infrastructure.service.remote.FileAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 封装关于文件处理相关的逻辑 <br>
 * [注意] 不要放入跟盘点领域相关的内容
 */
@Slf4j
@RequiredArgsConstructor
@Service("fileAclService")
public class FileAclServiceImpl implements FileAclService {

    private final DlcComponent dlcComponent;

    @Override
    public String getDownloadUrl(String fileId, String errorKey) {
        String downloadUrl = dlcComponent.getDownloadUrl(fileId);
        log.debug("LOG63150:get downloadUrl by fileId: {}, {}", fileId, downloadUrl);
        if (StringUtils.isBlank(downloadUrl)) {
            throw new ApiException(errorKey);
        }
        return downloadUrl;
    }

}
