package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.globalfacade.bean.langorg.LangOrg4Batch;
import com.yxt.globalfacade.bean.langorg.LangOrg4Detail;
import com.yxt.globalfacade.service.GlobalFacade;
import com.yxt.talent.rv.infrastructure.service.remote.GlobalAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service("GlobalAclService")
public class GlobalAclServiceImpl implements GlobalAclService {

    private final GlobalFacade globalFacade;

    @Override
    public LangOrg4Detail orgBatchSearchKeys(LangOrg4Batch langOrg4Batch) {
        return globalFacade.orgBatchSearchKeys(langOrg4Batch);
    }

    @Override
    public String getTransByLangAndOrgId(String langKey, String lang, String orgId) {
        return globalFacade.getTransByLangAndOrgId(lang<PERSON>ey, lang, orgId);
    }

}
