package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.L10NTranslator;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.repo.RedisRepository;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service("l10nAclService")
public class L10nAclServiceImpl implements L10nAclService {

    private final L10NTranslator translator;
    private final RedisRepository talentRedisRepository;

    @Override
    public boolean isEnableLocalization(String orgId) {
        try {
            String cacheKey = String.format(RedisKeys.CK_ORG_LOCALIZATION, orgId);
            String enableLocalization = (String) talentRedisRepository.opsForHash().get(cacheKey, orgId);
            if(StringUtils.isNotBlank(enableLocalization)){
                return "1".equals(enableLocalization);
            }
            boolean isEnable = translator.isEnableLocalization(orgId);
            if(isEnable){
                talentRedisRepository.opsForHash().put(cacheKey, orgId, "1");
                talentRedisRepository.expire(cacheKey, TimeUnit.HOURS.toHours(4), TimeUnit.HOURS);
            }else {
                talentRedisRepository.opsForHash().put(cacheKey, orgId, "0");
                talentRedisRepository.expire(cacheKey, TimeUnit.HOURS.toHours(4), TimeUnit.HOURS);
            }
            return isEnable;
        } catch (Exception e) {
            log.info("LOG13645:L10nAclService isEnableLocalization error", e);
        }
        return false;
    }

    /**
     * @deprecated 该方法已经废弃，请使用I18nTranslator，它已经支持用户名部门名等基础信息的多语言翻译，同时还支持业务字段多语言的翻译
     * @param enableLocalization
     * @param corpOrgIds
     * @param lang
     * @param type
     * @param dataList
     * @param <T>
     */
    @Override
    @Deprecated(since = "5.8")
    public <T extends L10NContent> void translateList(
            boolean enableLocalization,
            List<String> corpOrgIds, String lang, Class<T> type,
            List<T> dataList) {
        try {
            if (enableLocalization) {
                translator.translate(corpOrgIds, lang, type, dataList);
            }
        } catch (Exception e) {
            log.info("LOG13655:L10nAclService translateList error", e);
        }
    }

    @Override
    public Set<String> searchContentByKey(
            boolean enableLocalization,
            List<String> corpOrgIds, ResourceTypeEnum resourceType,
            String searchKey) {
        try {
            if (enableLocalization) {
                CommonList<IdName> idNameList =
                        translator.searchContentByKey(
                                corpOrgIds,
                                resourceType,
                                searchKey);
                return idNameList.getDatas()
                        .stream()
                        .map(IdName::getId)
                        .collect(Collectors.toSet());

            }
        } catch (Exception e) {
            log.info("LOG13665:L10nAclService searchContentByKey error", e);
        }
        return new HashSet<>();
    }

}
