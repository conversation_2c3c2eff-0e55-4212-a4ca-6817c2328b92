package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.BeanHelper;
import com.yxt.msgfacade.bean.MsgBean;
import com.yxt.msgfacade.service.MsgFacade;
import com.yxt.talent.rv.infrastructure.service.remote.MessageAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MessageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 封装与消息中心交互的逻辑<br>
 * [注意] 不要放入跟盘点领域相关的内容
 */
@Slf4j
@RequiredArgsConstructor
@Service("messageAclService")
public class MessageAclServiceImpl implements MessageAclService {

    private final MsgFacade msgFacade;

    /**
     * 发送消息
     *
     * @param message
     */
    @Override
    public void sendTemplateMessage(MessageDTO message) {
        if (CollectionUtils.isEmpty(message.getUserIds())) {
            return;
        }
        try {
            MsgBean msgBean = MsgBean.builder()
                    .templateCode(message.getTemplateCode())
                    .targetId(message.getTargetId())
                    .isCustomTemplate(YesOrNo.YES.getValue())
                    .orgId(message.getOrgId())
                    .userIds(message.getUserIds())
                    .params(message.getPlaceholderMap())
                    .build();

            msgBean.setCreateUserId(message.getUserId());
            msgBean.setCreateFullname(message.getUserFullName());
            msgBean.setIsCustomTemplate(0);
            log.info("LOG64080:{}", JSON.toJSONString(msgBean));
            msgFacade.sendTemMsg(msgBean);
        } catch (Exception e) {
            log.error("LOG64050:{}", message);
            throw e;
        }
    }

    /**
     * 通用发模版消息
     *
     * @param orgId        机构id
     * @param tempCode     模板code
     * @param bizTargetId  业务id标识
     * @param receivers    接受人数组
     * @param params       模板内容动态参数
     * @param isCustomTemp 是否使用自定义模板，不传默认使用自定义模板
     */
    @Override
    public void sendTempMsg(
            String orgId, String tempCode, String bizTargetId, List<String> receivers,
            Map<String, String> params, Integer isCustomTemp) {
        // 默认走使用自定义模版
        isCustomTemp = Objects.isNull(isCustomTemp) ? 1 : isCustomTemp;
        MsgBean bean = createMsgBean(orgId, tempCode, bizTargetId, receivers, params, isCustomTemp);
        log.debug("LOG63180:sendTempMsg bizTargetId={}, bean={}", bizTargetId,
                BeanHelper.bean2Json(bean, ALWAYS));
        msgFacade.sendTemMsg(bean);
    }

    private MsgBean createMsgBean(
            String orgId, String tempCode, String bizTargetId, List<String> receivers,
            Map<String, String> params, Integer isCustomTemp) {
        return MsgBean.builder()
                .templateCode(tempCode)
                .targetId(bizTargetId)
                .isCustomTemplate(isCustomTemp)
                .orgId(orgId)
                .userIds(receivers)
                .params(params)
                .build();
    }

}
