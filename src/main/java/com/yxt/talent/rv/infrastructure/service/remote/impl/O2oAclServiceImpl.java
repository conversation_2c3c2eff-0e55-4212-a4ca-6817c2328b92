package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.o2ofacade.bean.project.DemoCopyIDReq;
import com.yxt.o2ofacade.bean.project.DemoCopyIDResp;
import com.yxt.o2ofacade.service.ProjectFacade;
import com.yxt.talent.rv.infrastructure.service.remote.O2oAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service("o2oAclService")
public class O2oAclServiceImpl implements O2oAclService {

    private final ProjectFacade projectFacade;

    @Override
    public List<DemoCopyIDResp> getDemoCopyNewId(String orgId, int queryType) {
        DemoCopyIDReq req = new DemoCopyIDReq();
        req.setOrgId(orgId);
        req.setQueryType(queryType);
        return projectFacade.getDemoCopyNewId(req);
    }
}
