package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.common.util.BeanHelper;
import com.yxt.coreapi.client.clients.CoreApiClient;
import com.yxt.orginitfacade.bean.OrgContact4Search;
import com.yxt.orginitfacade.service.OrginitFacade;
import com.yxt.talent.rv.infrastructure.service.remote.OrginitAclService;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 封装通讯录加解密相关的逻辑<br>
 * [注意] 不要放入跟盘点领域相关的内容
 */
@Slf4j
@RequiredArgsConstructor
@Service("orginitAclService")
public class OrginitAclServiceImpl implements OrginitAclService {

    private final OrginitFacade orginitFacade;
    private final CoreApiClient coreApiClient;

    /**
     * 搜索关键词搜索逻辑。当查询用户时应为用户名称、名称拼音或者英文名；当查询部门时应为部门名称或者部门名称拼音。
     * 如果机构没有开通通讯录加密要素时，返回空集合
     *
     * @param orgId      查询企业ID
     * @param keyword    搜索关键词
     * @param type       查询类型 1-用户 2-部门
     * @param sourceCode 渠道号
     */
    @Override
    public List<String> searchContactList(
            String orgId, String keyword, int type,
            String sourceCode) {
        // 先调用 core 要素接口 判断此机构是否已接入通讯录加密能力
        if (StringUtils.isEmpty(sourceCode) || Boolean.FALSE.equals(
                coreApiClient.addressBookEncryptionCapacity(orgId, sourceCode))) {
            log.debug("LOG66260:");
            return new ArrayList<>();
        }
        // 如果有通讯录加密能力，则调用 orginit 接口；如果没有通讯录加密能力，则走现有流程，不用修改
        if (StringUtils.isEmpty(keyword)) {
            return new ArrayList<>();
        }
        return searchByOrginit(orgId, keyword, type);
    }

    @Nonnull
    private List<String> searchByOrginit(String orgId, String keyword, int type) {
        OrgContact4Search search = new OrgContact4Search();
        search.setOrgId(orgId);
        search.setType(type);
        search.setKeyword(keyword);
        List<String> ids = new ArrayList<>();
        try {
            log.info(
                    "LOG61880:searchContactList-input, orgId:{},keyword:{}, type:{}", orgId,
                    keyword,
                    type);
            ids = Optional.ofNullable(orginitFacade.searchMailList(search))
                    .orElse(new ArrayList<>());
            log.info(
                    "LOG61890:searchContactList-output, {}",
                    BeanHelper.bean2Json(ids, ALWAYS));
        } catch (Exception e) {
            log.error("LOG62590:OrginitapiService-searchByOrginit. ", e);
        }
        return ids;
    }
}
