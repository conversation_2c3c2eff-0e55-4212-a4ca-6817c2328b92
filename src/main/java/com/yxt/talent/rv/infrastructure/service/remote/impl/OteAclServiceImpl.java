package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.oteapifacade.bean.uem.ResetUemBean;
import com.yxt.oteapifacade.service.UemFacade;
import com.yxt.talent.rv.infrastructure.service.remote.OteAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OteAclServiceImpl implements OteAclService {

    private final UemFacade uemFacade;


    @Override
    public void clearUserRecord(String orgId, String arrangeId, List<String> userIds, String opUserId){
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        log.info("clearOteUserRecord: orgId={}, arrangeId={}, userIds={}", orgId, arrangeId, BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
        BatchOperationUtil.batchExecute(userIds, 100, userIdsBatch -> {
            ResetUemBean resetUemBean = new ResetUemBean();
            resetUemBean.setOrgId(orgId);
            resetUemBean.setArrangeId(arrangeId);
            resetUemBean.setUserIds(userIdsBatch);
            resetUemBean.setUserId(opUserId);
            resetUemBean.setSource("501");
            resetUemBean.setAdmin("1");
            uemFacade.resetUemInfo(resetUemBean);
        });
    }
}
