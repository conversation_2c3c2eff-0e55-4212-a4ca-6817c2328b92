package com.yxt.talent.rv.infrastructure.service.remote.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.criteria.Result;
import com.yxt.usdk.components.rocketmq.core.RocketMQTemplate;
import com.yxt.talent.rv.infrastructure.service.remote.MqAclSender;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MqSendResult;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.function.Supplier;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * 消息发送服务类,底层依赖rocketmq
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RocketMqAclSender implements MqAclSender {

    private final RocketMQTemplate rocketMQTemplate;

    @jakarta.annotation.Nonnull
    private static MessageQueueSelector getQueueSelector(String hashKey) {
        return (mqs, msg, arg) -> {
            int id = Objects.hashCode(hashKey);
            long index = Math.abs(id) % mqs.size();
            return mqs.get((int) index);
        };
    }

    @jakarta.annotation.Nonnull
    private static MqSendResult buildMqSendResult(@Nullable SendResult sendResult, @Nullable String errMsg) {
        if (sendResult == null) {
            return MqSendResult.builder()
                    .code(-1)
                    .err(errMsg == null ? "sendResult is null" : errMsg)
                    .build();
        }

        long code = Result.SUCCESS_CODE;
        if (sendResult.getSendStatus() != SendStatus.SEND_OK || StringUtils.isNotBlank(errMsg)) {
            code = Result.FAIL_COMMON_CODE;
        }
        return MqSendResult.builder()
                .msgId(sendResult.getMsgId())
                .code(code)
                .ext(BeanHelper.bean2Json(sendResult, ALWAYS))
                .err(errMsg)
                .build();
    }

    @jakarta.annotation.Nonnull
    private MqSendResult doSend(String topic, Supplier<SendResult> supplier) {
        SendResult sendResult = null;
        String errMsg = null;
        try {
            sendResult = supplier.get();
        } catch (Exception e) {
            errMsg = ExceptionUtil.getMessage(e);
            log.error("LOG00160:topic={}", topic, e);
        }
        MqSendResult mqSendResult = buildMqSendResult(sendResult, errMsg);
        if (mqSendResult.isFailure()) {
            log.info("LOG00170:{}", BeanHelper.bean2Json(mqSendResult, ALWAYS));
        }
        return mqSendResult;
    }

    /**
     * 同步发送普通消息
     */
    @Override
    public MqSendResult send(String topic, String body) {
        log.debug("LOG00180:topic={}, body={}", topic, body);
        return doSend(topic, () -> rocketMQTemplate.syncSend(topic, body));
    }

    /**
     * 异步发送普通消息
     *
     * @param topic
     * @param body
     */
    @Async
    @Override
    public void asyncSend(String topic, String body) {
        log.debug("LOG00190:topic={}, body={}", topic, body);
        doSend(topic, () -> rocketMQTemplate.syncSend(topic, body));
    }

    /**
     * 同步发送普通延迟消息 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @return
     */
    @Override
    public MqSendResult sendDelay(String topic, String body, int delayLevel) {
        log.debug("LOG00200:topic={}, body={}, delayLevel={}", topic, body, delayLevel);
        return doSend(topic, () -> rocketMQTemplate.syncDelaySend(topic, body, delayLevel));
    }

    /**
     * 异步发送普通延迟消息，等级从1开始，1代表1s，2代表5s，3代表10s，以此类推
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param topic
     * @param body
     * @param delayLevel
     */
    @Async
    @Override
    public void asyncSendDelay(String topic, String body, int delayLevel) {
        log.debug("LOG00210:topic={}, body={}, delayLevel={}", topic, body, delayLevel);
        doSend(topic, () -> rocketMQTemplate.syncDelaySend(topic, body, delayLevel));
    }

    @Override
    public MqSendResult sendDelay(String topic, String body, long delayTime) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void asyncSendDelay(String topic, String body, long delayTime) {
        throw new UnsupportedOperationException();
    }

    /**
     * 同步发送带Tag的消息, 以便消费端可以根据tag值进行选择性消费
     *
     * @return
     */
    @Override
    public MqSendResult sendWithTag(String topic, String tag, String body) {
        log.debug("LOG00220:topic={}, tag={}, body={}", topic, tag, body);
        return doSend(topic, () -> rocketMQTemplate.syncSend(topic, body, tag));
    }

    /**
     * 异步发送带Tag的消息, 以便消费端可以根据tag值进行选择性消费
     *
     * @param topic
     * @param tag
     * @param body
     */
    @Async
    @Override
    public void asyncSendWithTag(String topic, String tag, String body) {
        // 指定topic的同时，设置tag值，以便消费端可以根据tag值进行选择性消费
        log.info("LOG60770:mq sending topic {} with tag: {}, body: {}", topic, tag, body);
        doSend(topic, () -> rocketMQTemplate.syncSend(topic, body, tag));
    }

    /**
     * 同步发送有序消息，根据hashKey来判断应该发送到哪个队列
     *
     * @param topic
     * @param jsonBody
     * @param hashKey
     * @return
     */
    @Override
    public MqSendResult sendOrderly(String topic, String jsonBody, String hashKey) {
        log.debug("LOG66180:topic={}, body={}", topic, jsonBody);
        MessageQueueSelector messageQueueSelector = getQueueSelector(hashKey);
        return doSend(topic,
                () -> rocketMQTemplate.syncSendOrderly(topic, (Object) jsonBody, messageQueueSelector,
                        hashKey));
    }

    /**
     * 异步发送有序消息，根据hashKey来判断应该发送到哪个队列
     *
     * @param topic
     * @param jsonBody
     * @param hashKey
     */
    @Async
    @Override
    public void asyncSendOrderly(String topic, String jsonBody, String hashKey) {
        log.debug("LOG00230:topic: {}, body: {}", topic, jsonBody);
        MessageQueueSelector messageQueueSelector = getQueueSelector(hashKey);
        doSend(topic, () -> rocketMQTemplate.syncSendOrderly(topic, (Object) jsonBody, messageQueueSelector,
                hashKey));
    }
}
