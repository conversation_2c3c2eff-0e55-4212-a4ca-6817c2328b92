package com.yxt.talent.rv.infrastructure.service.remote.impl;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.util.BeanHelper;
import com.yxt.spevalfacade.bean.DimTypeI18nMsgReq;
import com.yxt.spevalfacade.bean.demo.OrgCopyBean;
import com.yxt.spevalfacade.bean.evaluation.Evaluation;
import com.yxt.spevalfacade.bean.evaluation.EvaluationFilterReq;
import com.yxt.spevalfacade.bean.form.FormUserDevelopResp;
import com.yxt.spevalfacade.bean.form.FormUserDevelopSearchBean;
import com.yxt.spevalfacade.bean.standar.StandarReq;
import com.yxt.spevalfacade.service.SpEvalApiFacade;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 针对测评能力的封装,适配盘点服务<br>
 * [注意] 不要放入跟盘点领域相关的内容
 */
@Slf4j
@RequiredArgsConstructor
@Service("spevalAclService")
public class SpevalAclServiceImpl implements SpevalAclService {

    private final SpEvalApiFacade spEvalApiFacade;

    @Override
    public List<Evaluation> getBatchEvaluation(StandarReq req) {
        return Optional.ofNullable(spEvalApiFacade.getBatchEvaluation(req)).orElse(new ArrayList<>());
    }

    @Override
    public List<FormUserDevelopResp> searchUserDevelop(FormUserDevelopSearchBean bean) {
        return Optional.ofNullable(spEvalApiFacade.searchUserDevelop(bean)).orElse(new ArrayList<>());
    }

    @Override
    public Map<String, String> getEntityIdMap(String sourceOrgId, String targetOrgId, String idMapKey) {
        OrgCopyBean orgCopyBean = new OrgCopyBean();
        orgCopyBean.setSourceOrgId(sourceOrgId);
        orgCopyBean.setTargetOrgId(targetOrgId);
        orgCopyBean.setIdMapKey(idMapKey);
        return spEvalApiFacade.getEntityIdMap(orgCopyBean);
    }

    public Map<String, String> getTypeStrIdName(String orgId, String locale, Collection<String> typeIds) {
        if (CollectionUtils.isEmpty(typeIds)) {
            return Map.of();
        }
        Map<Long, String> map = getTypeIdName(
            orgId, locale, typeIds.stream()
                .map(idStr -> NumberUtil.parseLong(idStr, null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        Map<String, String> ret = new HashMap<>(map.size());
        map.forEach((key, value) -> ret.put(String.valueOf(key), value));
        return ret;
    }

    public Map<Long, String> getTypeIdName(String orgId, String locale, List<Long> typeIds) {
        if (StringUtils.isEmpty(orgId) || CollectionUtils.isEmpty(typeIds)) {
            return Map.of();
        }
        DimTypeI18nMsgReq req = new DimTypeI18nMsgReq();
        req.setOrgId(orgId);
        req.setLocale(locale);
        req.setDimTypeIds(typeIds);
        return spEvalApiFacade.getMessagesByIds(req);
    }

    @Override
    public List<String> getHasExplainEvalIds(String orgId, List<String> evalIds) {
        if (StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(evalIds)) {
            return new ArrayList<>();
        }
        EvaluationFilterReq req = new EvaluationFilterReq();
        req.setOrgId(orgId);
        req.setEvaluationIds(evalIds);
        return spEvalApiFacade.filterEvaluationIds(req);
    }

    @Override
    public void clearUserRecord(String orgId, String evalId, List<String> userIds){
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        log.info("clearUserRecord: orgId={}, evalId={}, userIds={}", orgId, evalId, BeanHelper.bean2Json(userIds, JsonInclude.Include.NON_NULL));
        spEvalApiFacade.clearUserRecord(orgId, evalId, userIds);
    }
}
