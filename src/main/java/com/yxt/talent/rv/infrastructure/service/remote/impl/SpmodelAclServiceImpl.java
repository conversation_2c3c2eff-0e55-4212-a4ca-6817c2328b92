package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.alibaba.fastjson.JSONObject;
import com.yxt.spmodel.facade.bean.demo.OrgDemoIdMappingVO;
import com.yxt.spmodel.facade.bean.rule.ExecuteRuleVO;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.spmodel.facade.service.*;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Service("spmodelAclService")
public class SpmodelAclServiceImpl implements SpmodelAclService {

    private final SpmodelIndicatorsService spmodelIndicatorsService;
    private final SpmodelLabelService spmodelLabelService;
    private final SpmodelRuleService spmodelRuleService;
    private final SpmodelSqlService spmodelSqlService;
    private final SpmodelDemoCopyIdMappingService spmodelDemoCopyIdMappingService;

    /**
     * 查询单个规则-对外
     *
     * @param id      规则ID
     * @param orgId   orgId
     * @param appCode 接入方appCode
     * @return ExecuteRuleVO
     */
    @Override
    public ExecuteRuleVO getRule(Long id, String orgId, String appCode) {
        return spmodelRuleService.getRule(id, orgId, appCode);
    }

    /**
     * 查询指标分类列表 -- 不分页
     *
     * @param param IndicatorsCategoryParam
     * @return CommonList<IndicatorsCategoryVO>
     */
    @Override
    public List<JSONObject> sql(SqlParam param) {
        return Optional.ofNullable(spmodelSqlService.sql(param)).orElse(new ArrayList<>());
    }

    @Override
    public OrgDemoIdMappingVO getOrgDemoIdMappingVO(String orgId) {
        try {
            return spmodelDemoCopyIdMappingService.getOrgDemoIdMappingVO(orgId);
        } catch (FeignException.BadRequest e) {
            String respBody = e.contentUTF8();
            if (respBody != null && respBody.contains("apis.spm.org.info.not.exist")) {
                log.warn("org spmodel not init {} resp {}", orgId, respBody);
                return new OrgDemoIdMappingVO();
            }
            throw e;
        }
    }

}
