package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdfacade.bean.spsd.*;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.talent.rv.application.xpd.common.dto.DimTreeDto;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.ModelDimDTO;
import com.yxt.talent.rv.infrastructure.service.remote.enums.SdDimName;
import com.yxt.talent.rv.infrastructure.service.remote.enums.SdIndicatorName;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service("spsdAclService")
public class SpsdAclServiceImpl implements SpsdAclService {

    private final SptalentsdFacade sptalentsdFacade;
    private final Map<Object, Boolean> visitedObjects = new ConcurrentHashMap<>();

    @Override
    public Map<String, String> getDemoOrgMapping(String sourceOrgId, String targetOrgId, String mapKey) {
        return sptalentsdFacade.getDemoOrgMapping(sourceOrgId, targetOrgId, mapKey);
    }

    @Override
    public List<IndicatorBaseDto> getModelIndicatorInfoList(String orgId, List<String> indicatorIds) {
        ModelIndicatorBaseReq modelIndicatorBaseReq = new ModelIndicatorBaseReq();
        modelIndicatorBaseReq.setOrgId(orgId);
        modelIndicatorBaseReq.setIds(indicatorIds);
        log.info("获取模型指标信息列表，参数：{}", JSON.toJSONString(modelIndicatorBaseReq));
        return sptalentsdFacade.getIndicatorBaseInfo(modelIndicatorBaseReq);
    }

    @Override
    public List<IndicatorDto> getIndicatorByModelId(String orgId, String modelId) {
        if (StringUtils.isEmpty(modelId) || StringUtils.isEmpty(orgId)) {
            return new ArrayList<>();
        }
        return sptalentsdFacade.getIndicatorByModelId(orgId, modelId);
    }

    @Override
    public List<ModelDimDTO> getModelDimInfo(String orgId, String modelId) {
        ModelInfo modelInfo = sptalentsdFacade.getModelInfo(orgId, modelId);
        if (modelInfo == null) {
            return new ArrayList<>();
        }
        List<ModelDimDTO> modelDims = new ArrayList<>();
        buildModelInfo(modelDims, modelInfo.getDms());
        return modelDims;
    }

    private static void buildModelInfo(List<ModelDimDTO> modelDims, List<ModelRequireBaseInfo> dms) {
        for (ModelRequireBaseInfo dim : dms) {
            ModelDimDTO modelDimDTO = new ModelDimDTO();
            modelDimDTO.setDimId(dim.getDmId());
            modelDimDTO.setDimName(dim.getDmName());
            modelDimDTO.setDimType(dim.getDmType());
            modelDimDTO.setCustomName(dim.getCustomName());
            modelDimDTO.setSourceType(dim.getSourceType());
            modelDimDTO.setIndicatorType(dim.getIndicatorType());
            modelDimDTO.setNameI18n(dim.getNameI18n());
            modelDimDTO.setCustomNameI18n(dim.getCustomNameI18n());
            modelDimDTO.setRemark(dim.getRemark());
            modelDimDTO.setParentId(dim.getParentId());
            modelDimDTO.setLevelType(dim.getLevelType());
            modelDimDTO.setOrderIndex(dim.getOrderIndex());
            modelDims.add(modelDimDTO);
            if (CollectionUtils.isNotEmpty(dim.getChilds())) {
                buildModelInfo(modelDims, dim.getChilds());
            }
        }
    }

    @Override
    public void fillNamesAndI18nNames(Object obj, String orgId) {
        if (obj == null || visitedObjects.containsKey(obj)) {
            return;
        }
        try {
            visitedObjects.put(obj, true);
            // 收集所有需要查询的ID
            FieldCollector collector = new FieldCollector();
            collectFields(obj, collector);

            // 批量查询维度信息
            if (CollectionUtils.isNotEmpty(collector.getDimIds())) {
                List<DimensionList4Get> baseDimDetail =
                    sptalentsdFacade.getBaseDimDetail(orgId, new ArrayList<>(collector.getDimIds()));
                Map<String, DimensionList4Get> dimMap = baseDimDetail.stream()
                    .collect(Collectors.toMap(DimensionList4Get::getId, Function.identity(), (u, v) -> v));

                collector.getDimFields().forEach(fieldInfo -> {
                    try {
                        String dimId = getAnnotationId(fieldInfo.field().getAnnotation(SdDimName.class).id(),
                            fieldInfo.field().getAnnotation(SdDimName.class).value(), fieldInfo.target()
                        );
                        DimensionList4Get dim = dimMap.get(dimId);
                        if (dim != null) {
                            setFieldValue(fieldInfo.target(), fieldInfo.field().getName(), dim.getDmName());
                            if (StringUtils.isNotBlank(fieldInfo.i18nField())) {
                                setFieldValue(fieldInfo.target(), fieldInfo.i18nField(), dim.getNameI18n());
                            }
                        }
                    } catch (Exception e) {
                        log.error("设置维度名称时发生错误", e);
                    }
                });
            }

            // 批量查询指标信息
            if (CollectionUtils.isNotEmpty(collector.getIndicatorIds())) {
                List<IndicatorBaseDto> indicators =
                    getModelIndicatorInfoList(orgId, new ArrayList<>(collector.getIndicatorIds()));
                Map<String, IndicatorBaseDto> indicatorMap = indicators.stream()
                    .collect(Collectors.toMap(IndicatorBaseDto::getId, Function.identity(), (u, v) -> v));

                collector.getIndicatorFields().forEach(fieldInfo -> {
                    try {
                        String indicatorId =
                            getAnnotationId(fieldInfo.field().getAnnotation(SdIndicatorName.class).id(),
                                fieldInfo.field().getAnnotation(SdIndicatorName.class).value(), fieldInfo.target()
                            );
                        IndicatorBaseDto indicator = indicatorMap.get(indicatorId);
                        if (indicator != null) {
                            setIndicatorName(
                                fieldInfo.target(), fieldInfo.field().getName(), fieldInfo.i18nField(), indicator);
                        }
                    } catch (Exception e) {
                        log.error("设置指标名称时发生错误", e);
                    }
                });
            }
        } finally {
            visitedObjects.remove(obj);
        }
    }

    private void collectFields(Object obj, FieldCollector collector) {
        if (obj == null) {
            return;
        }

        if (obj instanceof Collection<?>) {
            ((Collection<?>) obj).forEach(item -> collectFields(item, collector));
            return;
        }
        if (obj instanceof Map<?, ?>) {
            ((Map<?, ?>) obj).values().forEach(value -> collectFields(value, collector));
            return;
        }
        if (obj.getClass().isArray()) {
            Object[] array = (Object[]) obj;
            for (Object item : array) {
                collectFields(item, collector);
            }
            return;
        }

        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 处理@SdDimName注解
                SdDimName sdDimName = field.getAnnotation(SdDimName.class);
                if (sdDimName != null) {
                    String dimId = getAnnotationId(sdDimName.id(), sdDimName.value(), obj);
                    if (StringUtils.isNotBlank(dimId)) {
                        collector.addDimField(obj, field, sdDimName.i18n(), dimId);
                    }
                }

                // 处理@SdIndicatorName注解
                SdIndicatorName sdIndicatorName = field.getAnnotation(SdIndicatorName.class);
                if (sdIndicatorName != null) {
                    String indicatorId = getAnnotationId(sdIndicatorName.id(), sdIndicatorName.value(), obj);
                    if (StringUtils.isNotBlank(indicatorId)) {
                        collector.addIndicatorField(obj, field, sdIndicatorName.i18n(), indicatorId);
                    }
                }

                // 递归处理复杂对象
                Object fieldValue = field.get(obj);
                if (fieldValue != null && !isPrimitiveOrWrapper(fieldValue.getClass())) {
                    collectFields(fieldValue, collector);
                }
            } catch (IllegalAccessException e) {
                log.error("处理对象字段时发生错误", e);
            }
        }
    }


    private record FieldInfo(
        Object target,
        Field field,
        String i18nField
    ) {
    }

    @Getter
    private static class FieldCollector {
        private final Set<String> dimIds = new HashSet<>();
        private final Set<String> indicatorIds = new HashSet<>();
        private final List<FieldInfo> dimFields = new ArrayList<>();
        private final List<FieldInfo> indicatorFields = new ArrayList<>();

        public void addDimField(Object target, Field field, String i18nField, String dimId) {
            dimIds.add(dimId);
            dimFields.add(new FieldInfo(target, field, i18nField));
        }

        public void addIndicatorField(Object target, Field field, String i18nField, String indicatorId) {
            indicatorIds.add(indicatorId);
            indicatorFields.add(new FieldInfo(target, field, i18nField));
        }

    }

    private void setIndicatorName(Object obj, String nameField, String i18nField, IndicatorBaseDto indicator) {
        Optional.ofNullable(indicator.getItemType()).ifPresent(itemType -> {
            try {
                switch (itemType) {
                    case 0:
                        setFieldValue(obj, nameField, indicator.getItemValue());
                        break;
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                        setFieldValue(obj, nameField, indicator.getRt().getName());
                        if (StringUtils.isNotBlank(i18nField)) {
                            setFieldValue(obj, i18nField, indicator.getRt().getNameI18n());
                        }
                        break;
                    default:
                        setFieldValue(obj, nameField, indicator.getItems().getName());
                        if (StringUtils.isNotBlank(i18nField)) {
                            setFieldValue(obj, i18nField, indicator.getItems().getNameI18n());
                        }
                        break;
                }
            } catch (Exception e) {
                log.error("设置指标名称时发生错误", e);
            }
        });

        if (indicator.getItemType() == null) {
            log.debug("LOG10532:indicatorBaseDto={}", indicator);
        }
    }

    private String getAnnotationId(String idField, String value, Object obj) {
        if (StringUtils.isNotBlank(value)) {
            return value;
        }
        try {
            Field idFieldObj = obj.getClass().getDeclaredField(idField);
            idFieldObj.setAccessible(true);
            Object idValue = idFieldObj.get(obj);
            return idValue != null ? idValue.toString() : null;
        } catch (Exception e) {
            log.error("获取注解ID时发生错误", e);
            return null;
        }
    }

    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            log.error("设置字段值时发生错误: " + fieldName, e);
        }
    }

    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               String.class.equals(clazz) ||
               Number.class.isAssignableFrom(clazz) ||
               Boolean.class.equals(clazz) ||
               Character.class.equals(clazz) ||
               Date.class.equals(clazz);
    }

    /**
     * 从人才标准拿维度信息
     * 返回查询维度及其所包含的子维度信息
     *
     * @param orgId    机构ID
     * @param modelId  模型ID
     * @param sdDimIds 维度IDs-标准
     * @return list
     */
    @Override
    public List<ModelBase4Facade> getDimInfoList(String orgId, String modelId, List<String> sdDimIds) {

        if (StringUtils.isEmpty(modelId) || CollectionUtils.isEmpty(sdDimIds)) {
            return Lists.newArrayList();
        }

        List<ModelBase4Facade> modelBaseInfoList = sptalentsdFacade.getModelBaseInfo(orgId, modelId, sdDimIds);
        if (CollectionUtils.isEmpty(modelBaseInfoList)) {
            return Lists.newArrayList();
        }

        // 把根节点的父级ID置为"0"
        modelBaseInfoList.forEach(modelBase -> {
            if (StringUtils.isEmpty(modelBase.getParentId())) {
                modelBase.setParentId(AppConstants.ROOT_ID);
            }
        });

        Map<String, List<ModelBase4Facade>> parentIdMap = modelBaseInfoList.stream()
            .collect(Collectors.groupingBy(ModelBase4Facade::getParentId));
        modelBaseInfoList.forEach(modelBase -> {
            if (parentIdMap.containsKey(modelBase.getId())) {
                modelBase.setLevelType(0);
            } else {
                modelBase.setLevelType(1);
            }
        });

        return modelBaseInfoList;
    }

    @Override
    public Map<String, ModelBase4Facade> getDimInfoMap(String orgId, String modelId, List<String> sdDimIds) {
        List<ModelBase4Facade> dimInfoList = getDimInfoList(orgId, modelId, sdDimIds);
        if (CollectionUtils.isEmpty(dimInfoList)) {
            return new HashMap<>();
        }
        return StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId);
    }

    /**
     * 获取模型下的维度树-无指标
     *
     * @param orgId   机构ID
     * @param modelId 模型ID
     * @return 维度树结构
     */
    @Override
    public List<DimTreeDto> getDimTreeNoIndicator(String orgId, String modelId) {

        if (StringUtils.isEmpty(modelId)) {
            return Lists.newArrayList();
        }

        List<DimTreeDto> dimTreeDtoList = Lists.newArrayList();
        ModelInfo modelInfo = sptalentsdFacade.getModelInfo(orgId, modelId);

        if (modelInfo == null) {
            return dimTreeDtoList;
        }

        // 祖先节点
        List<ModelRequireBaseInfo> ancestorDimList = modelInfo.getDms();
        for (ModelRequireBaseInfo ancestorDim : ancestorDimList) {
            DimTreeDto dimTreeDto = buildDim(ancestorDim);

            dimTreeDto.setSubDims(getChildren(ancestorDim));
            dimTreeDtoList.add(dimTreeDto);
        }
        return dimTreeDtoList;
    }

    private void addModelDims(List<DimTreeDto> dimList, List<ModelRequireBaseInfo> modelDims) {
        if (CollectionUtils.isNotEmpty(modelDims)) {
            for (ModelRequireBaseInfo modelDim : modelDims) {
                dimList.add(buildDim(modelDim));
                addModelDims(dimList, modelDim.getChilds());
            }
        }
    }

    private DimTreeDto buildDim(ModelRequireBaseInfo dim) {
        String dimName = StringUtils.isBlank(dim.getCustomName()) ? dim.getDmName() : dim.getCustomName();
        return DimTreeDto.builder().sdDimId(dim.getDmId()).dimName(dimName).dimType(dim.getIndicatorType())
            .levelType(dim.getLevelType()).deqType(dim.getDeqType()).orderIndex(dim.getOrderIndex()).build();
    }

    private List<DimTreeDto> getChildren(ModelRequireBaseInfo sourceParent) {

        if (Objects.isNull(sourceParent)) {
            return Lists.newArrayList();
        }

        List<DimTreeDto> children = Lists.newArrayList();
        for (ModelRequireBaseInfo sourceChild : sourceParent.getChilds()) {
            String dimName = StringUtils.isBlank(sourceChild.getCustomName()) ? sourceChild.getDmName() : sourceChild.getCustomName();
            DimTreeDto child = DimTreeDto.builder().sdDimId(sourceChild.getDmId()).dimName(dimName)
                .dimType(sourceChild.getIndicatorType()).levelType(sourceChild.getLevelType())
                .deqType(sourceChild.getDeqType()).orderIndex(sourceChild.getOrderIndex()).build();
            child.setSubDims(getChildren(sourceChild));
            children.add(child);
        }
        return children;
    }

    /**
     * 获取模型下所有的末级指标
     *
     * @param orgId   机构ID
     * @param modelId 模型ID
     * @return list
     */
    @Override
    public List<IndicatorDto> getLastIndicators(String orgId, String modelId) {
        return sptalentsdFacade.getLastStageIndicator(orgId, modelId);
    }

    /**
     * 获取模型下所有的末级指标
     *
     * @param orgId   机构ID
     * @param modelId 模型ID
     * @return list
     */
    @Override
    public List<IndicatorDto> getLastIndicators(String orgId, String modelId, List<String> dimIds) {
        return sptalentsdFacade.getLastStageIndicatorByDmIds(orgId, modelId, dimIds);
    }

    /**
     * 指定指标IDs获取指标列表
     *
     * @param orgId        机构ID
     * @param indicatorIds 指标IDs
     * @return list
     */
    @Override
    public List<IndicatorDto> getIndicatorInfo(String orgId, String modelId, List<String> indicatorIds) {
        if (CollectionUtils.isEmpty(indicatorIds)) {
            return Lists.newArrayList();
        }
        //        IndicatorFacadeReq req = new IndicatorFacadeReq();
        //        req.setOrgId(orgId);
        //        req.setIndicatorIds(indicatorIds);
        ModelIndicatorReq req = new ModelIndicatorReq();
        req.setOrgId(orgId);
        req.setModelId(modelId);
        req.setBaseIds(indicatorIds);
        return sptalentsdFacade.getModelIndicator(req);
    }

    /**
     * 指定指标IDs获取指标列表
     *
     * @param orgId        机构ID
     * @param indicatorIds 指标IDs
     * @return list
     */
    @Override
    public Map<String, IndicatorDto> getIndicatorInfoMap(String orgId, String modelId, List<String> indicatorIds) {
        List<IndicatorDto> indicatorDetail = getIndicatorInfo(orgId, modelId, indicatorIds);
        return StreamUtil.list2map(indicatorDetail, IndicatorDto::getItemId);
    }

    @Override
    public ModelInfo getModelInfo(String orgId, String modelId) {
        return sptalentsdFacade.getModelInfo(orgId, modelId);
    }

    @Override
    public List<IndicatorBaseDto> getIndicatorBaseInfoByIndicatorIds(String orgId, List<String> ids) {
        if (StringUtils.isEmpty(orgId) || CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ModelIndicatorBaseReq search = new ModelIndicatorBaseReq();
        search.setIds(ids);
        search.setOrgId(orgId);
        return sptalentsdFacade.getIndicatorBaseInfo(search);
    }

    @Override
    public List<IndicatorDto> getLastIndicatorByDims(String orgId, String modelId, String dimId) {
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(modelId) || StringUtils.isBlank(dimId)) {
            return Lists.newArrayList();
        }
        return getLastIndicatorByDims(orgId, modelId, Lists.list(dimId));
    }

    @Override
    public List<IndicatorDto> getLastIndicatorByDims(String orgId, String modelId, List<String> dimIds) {
        return sptalentsdFacade.getLastStageIndicatorByDmIds(orgId, modelId, dimIds);
    }

    @Override
    public Map<ModelBase4Facade, List<IndicatorDto>> dimIndicatorMapByDims(
        String orgId, String modelId,
        List<String> sdDimIds) {
        if (StringUtils.isEmpty(modelId) || CollectionUtils.isEmpty(sdDimIds)) {
            return new HashMap<>();
        }
        List<ModelBase4Facade> modelBaseInfoList = sptalentsdFacade.getModelBaseInfo(orgId, modelId, sdDimIds);
        Map<String, ModelBase4Facade> modelBaseMap = StreamUtil.list2map(modelBaseInfoList, ModelBase4Facade::getId);
        Map<ModelBase4Facade, List<String>> dimSubListMap = new HashMap<>();
        modelBaseInfoList.stream().filter(item -> sdDimIds.contains(item.getDmId()))
            .forEach(item -> dimSubListMap.put(item, Lists.newArrayList(item.getDmId())));
        for (ModelBase4Facade nodeBase : modelBaseInfoList) {
            ModelBase4Facade parentBase = nodeBase;
            for (int i = 0; i < modelBaseInfoList.size(); i++) {
                if (parentBase == null) {
                    break;
                }
                List<String> subDimList = dimSubListMap.get(parentBase);
                if (subDimList != null) {
                    subDimList.add(nodeBase.getDmId());
                    break;
                }
                parentBase = modelBaseMap.get(parentBase.getParentId());
            }
        }
        Map<String, ModelBase4Facade> sdDimRootMap = new HashMap<>();
        dimSubListMap.forEach(
            (base, subDimIdList) -> subDimIdList.forEach(subDimId -> sdDimRootMap.put(subDimId, base)));
        List<IndicatorDto> indicatorDtos = sptalentsdFacade.getLastStageIndicatorByDmIds(orgId, modelId, sdDimIds);
        Map<ModelBase4Facade, List<IndicatorDto>> ret = new HashMap<>();
        indicatorDtos.forEach(item -> {
            ModelBase4Facade modelBase4Facade = sdDimRootMap.get(item.getDmId());
            if (modelBase4Facade != null) {
                IArrayUtils.addMapList(ret, modelBase4Facade, item);
            }
        });
        return ret;
    }

    @Override
    public List<DimensionList4Get> getBaseDimDetail(String orgId, List<String> allDimIds) {
        if (CollectionUtils.isEmpty(allDimIds)) {
            return new ArrayList<>();
        }
        return sptalentsdFacade.getBaseDimDetail(orgId, allDimIds);
    }

    @Override
    public List<DimensionList4Get> allEnabledBaseDims(String orgId) {
        List<DimensionList4Get> dimList = sptalentsdFacade.getAllBaseDims(orgId);
        IArrayUtils.remove(dimList, item -> !Objects.equals(item.getState(), YesOrNo.YES.getValue()));
        return dimList;
    }

    @Override
    public Map<String, String> getIndIdsByIndNums(String orgId, List<String> indNums) {
        BaseSearch4Facade search = new BaseSearch4Facade();
        search.setOrgId(orgId);
        search.setNums(indNums);
        Base4Facade base4Facade = sptalentsdFacade.getBaseInfo(search);
        Map<String, String> result = new HashMap<>();
        if (null != base4Facade && CollectionUtils.isNotEmpty(base4Facade.getSkills())) {
            result.putAll(StreamUtil.list2map(base4Facade.getSkills(), BaseDict4Facade::getNum, BaseDict4Facade::getId));
        }

        if (null != base4Facade && CollectionUtils.isNotEmpty(base4Facade.getRts())) {
            result.putAll(StreamUtil.list2map(base4Facade.getRts(), BaseRt4Facade::getNum, BaseRt4Facade::getId));
        }

        if (null != base4Facade && CollectionUtils.isNotEmpty(base4Facade.getKngs())) {
            result.putAll(StreamUtil.list2map(base4Facade.getKngs(), BaseDict4Facade::getNum, BaseDict4Facade::getId));
        }

        if (null != base4Facade && CollectionUtils.isNotEmpty(base4Facade.getTechs())) {
            result.putAll(StreamUtil.list2map(base4Facade.getTechs(), BaseDict4Facade::getNum, BaseDict4Facade::getId));
        }
        return result;
    }

}
