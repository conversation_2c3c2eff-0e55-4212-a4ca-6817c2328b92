package com.yxt.talent.rv.infrastructure.service.remote.impl;

import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.common.IdsBean;
import com.yxt.udpfacade.bean.dept.*;
import com.yxt.udpfacade.bean.mq.org.OrgSyncRequest4MqBean;
import com.yxt.udpfacade.bean.mq.org.OrgSyncResponse4MqBean;
import com.yxt.udpfacade.bean.org.OrgBean;
import com.yxt.udpfacade.service.UdpEsUserSearchFacade;
import com.yxt.udpfacade.service.UdpFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service("udpAclService")
public class UdpAclServiceImpl implements UdpAclService {

    private final UdpFacade udpFacade;

    private final UdpEsUserSearchFacade udpEsUserSearchFacade;

    /**
     * check用戶ID列表是否为部门经理(包含用户的主管部门及兼管部门)
     * 或：根据机构id,用户id列表获取设置为部门经理管辖的所有的部门id列表(用户端调用)
     *
     * @param orgId 机构id
     *              用户ID
     * @return boolean
     */
    @Override
    public List<DeptManagerBean> checkIsDeptManagers(String orgId, IdsBean bean) {
        return Optional.ofNullable(udpFacade.checkIsDeptManagers(orgId, bean)).orElse(new ArrayList<>());
    }

    /**
     * 获取机构的根部门（默认只有一个,目前只返回id和name两个字段）
     *
     * @param orgId 需要获取一级部门的机构id
     * @return PagingList<DeptSimpleBean>
     */
    @Override
    public DeptSimpleBean findOrgRootDept(String orgId) {
        return udpFacade.findOrgRootDept(orgId);
    }

    /**
     * 获取多个部门的部门及其子部门的所有ids(包含虚拟部门)
     *
     * @param orgId
     * @return CommonList<String>
     */
    @Override
    public CommonList<DeptTreeId> getDeptTreeIds(String orgId, CommonList<String> bean) {
        return Optional.ofNullable(udpFacade.getDeptTreeIds(orgId, bean))
                .orElse(new CommonList<>());
    }

    /**
     * 获取指定用户的主管部门及兼管部门
     *
     * @param orgId  String
     * @param userId String 用户ID
     * @return CommonList<ManagedDeptBean>
     */
    @Override
    public List<ManagedDeptBean> findManagedDeptUsers(String orgId, String userId) {
        return Optional.ofNullable(udpFacade.findManagedDeptUsers(orgId, userId)).orElse(new ArrayList<>());
    }

    /**
     * 直属经理下属成员
     */
    @Override
    public CommonList<String> managerSubMember(String orgId, String managerUserId) {
        return Optional.ofNullable(udpFacade.managerSubMember(orgId, managerUserId)).orElse(new CommonList<>());
    }

    /**
     * 根据传入的部门,查询它们下属的子孙级部门id,最后包括它们自己本身一块组成集合返回出去,
     * 一般用于数据权限鉴权
     *
     * @param orgId
     * @param deptIds
     * @return
     */
    @Override
    public List<String> exchangeSubDeptIds(String orgId, List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }
        CommonList<String> deptList = new CommonList<>(new ArrayList<>(deptIds));
        CommonList<DeptTreeId> deptTree = udpFacade.getDeptTreeIds(orgId, deptList);
        if (CollectionUtils.isEmpty(deptTree.getDatas())) {
            return new ArrayList<>();
        }
        Set<String> targetDeptIds = new LinkedHashSet<>(deptIds);
        for (DeptTreeId data : deptTree.getDatas()) {
            targetDeptIds.add(data.getDeptId());
            targetDeptIds.addAll(data.getTreeIds());
        }
        return new ArrayList<>(targetDeptIds);
    }

    /**
     * 获取机构信息
     *
     * @param orgId
     * @return OrgBean
     */
    @Override
    public OrgBean getOrgInfo(String orgId) {
        return udpFacade.getOrgInfo(orgId);
    }

    /**
     * 管理员端使用： 同步机构用户，部门，岗位. orgId必须，根据OrgSyncRequest4MqBean注释使用，同一个机构每2h可以请求一次
     *
     * @param bean UserRequest4MqBean
     */
    @Override
    public OrgSyncResponse4MqBean syncOrgDatasRequest4Mq(OrgSyncRequest4MqBean bean) {
        return udpFacade.syncOrgDatasRequest4Mq(bean);
    }

    /**
     * 获取单个部门的部门及其子部门的所有ids(不包含虚拟部门)
     *
     * @param orgId
     * @param deptId
     * @return CommonList<String>
     */
    @Override
    public CommonList<String> getDeptTreeIds(String orgId, String deptId) {
        return Optional.ofNullable(udpFacade.getDeptTreeIds(orgId, deptId))
                .orElse(new CommonList<>());
    }

    @Override
    public List<IdName> getDeptInfoByIds(String orgId, List<String> deptIds) {
        try {
            return Optional.ofNullable(udpFacade.getDeptInfoByIds(orgId, deptIds))
                    .orElse(new ArrayList<>());
        }catch (Exception e){
            log.info("LOG13695:udpfacade getDeptInfoByIds error", e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getDeptManagers(String orgId, String userId) {
        CommonList<ParentDeptManagerBean> userParentDeptIdManagers =
            udpFacade.findUserParentDeptIdManagers(orgId, userId, 0);
        if (CollectionUtils.isNotEmpty(userParentDeptIdManagers.getDatas())) {
            return userParentDeptIdManagers.getDatas().stream().filter(e -> e.getLevel() == 0).map(ParentDeptManagerBean::getUserId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 是否为部门经理
     *
     * @param orgId  机构id
     * @param userId 用户ID
     * @return boolean
     */
    @Override
    public boolean checkIsDeptManager(String orgId, String userId) {
        return udpFacade.checkIsDeptManager(orgId, userId);
    }

}
