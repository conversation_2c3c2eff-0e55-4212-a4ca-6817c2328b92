/**
 * 此处封装所有外部依赖的调用,充当防腐层的作用 <br>
 * 任何外部依赖都需要通过这里调用,而不是在服务中到处直接调用外部接口.<br>
 * <br>
 * 在软件架构中，防腐层（Anti-Corruption Layer, ACL）是一种设计模式，用于隔离系统的不同部分，以防止一个领域模型的变化影响另一个领域模型。 <br>
 * 它充当两个系统或领域之间的一个翻译层或适配器，确保一个系统的变化不会直接破坏另一个系统的完整性。<br>
 * <br>
 * 使用防腐层的场景：<br>
 * - 集成遗留系统：当你的新系统需要与老旧的遗留系统交互时。<br>
 * - 微服务架构：在微服务架构中，各服务之间需要清晰的边界。<br>
 * - 外部服务集成：当你的系统需要集成外部API或第三方服务时。<br>
 * <br>
 * 实现防腐层的步骤：<br>
 * 1.定义接口：首先在两个系统或领域之间定义清晰的接口。这个接口应该用你的系统理解的术语和数据结构。<br>
 * 2.映射和转换：在防腐层中实现数据和方法的映射或转换逻辑。将外部模型转换为内部模型，并确保这一转换逻辑只存在于防腐层中。<br>
 * 3.调用和返回数据：在你的系统中调用防腐层提供的接口，而不是直接与外部系统通信。同样，处理来自外部系统的数据时，也应通过防腐层来进行必要的转换。
 */
package com.yxt.talent.rv.infrastructure.service.remote;
