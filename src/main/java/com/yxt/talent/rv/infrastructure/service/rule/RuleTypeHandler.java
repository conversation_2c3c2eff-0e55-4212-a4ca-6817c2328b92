package com.yxt.talent.rv.infrastructure.service.rule;

import com.yxt.spsdk.common.bean.*;
import com.yxt.spsdk.common.enums.SpCompareOperator;
import com.yxt.talent.rv.application.xpd.common.dto.QueryRuleColumnDto;
import com.yxt.talent.rv.application.xpd.common.dto.QueryRuleColumnValDto;

import java.util.List;

/**
 * 规则类型处理器基类
 * 负责处理特定规则类型的业务逻辑
 */
public abstract class RuleTypeHandler {
    
    /**
     * 获取规则类型
     */
    public abstract int getRuleType();
    
    /**
     * 获取支持的比较操作符
     */
    public abstract SpCompareOperator[] getSupportedOperators();
    
    /**
     * 获取列选项
     */
    public abstract List<RuleOptionBean> getColumnOptions(QueryRuleColumnDto queryDto);
    
    /**
     * 获取列值
     */
    public abstract List<RuleColumnValueBean> getColumnValues(QueryRuleColumnValDto queryDto);
    
    /**
     * 获取默认列值
     */
    public abstract Object getDefaultColumnValue();
    
    /**
     * 获取枚举值（可选）
     */
    public List<RuleEnumValueBean> getEnumValues(RuleMainBase mainData) {
        return null;
    }
    
    /**
     * 转换值（可选）
     */
    public Object convertValue(RuleMainBase mainData, SpRuleColumnBean columnBean, String valueStr) {
        return null;
    }
    
    /**
     * 获取演示复制配置
     */
    public SpRuleDemoCopyBean getDemoCopyConfig() {
        return null;
    }
    
    /**
     * 是否为单列
     */
    public boolean isSingleColumn() {
        return false;
    }
}
