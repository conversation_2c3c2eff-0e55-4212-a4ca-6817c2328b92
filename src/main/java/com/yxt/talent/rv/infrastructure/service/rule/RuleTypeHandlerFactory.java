package com.yxt.talent.rv.infrastructure.service.rule;

import com.yxt.common.component.SpringContextHolder;
import jakarta.annotation.Nullable;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则类型处理器工厂
 * 负责管理和获取各种规则类型处理器
 */
@Component
public class RuleTypeHandlerFactory {

    private final Map<Integer, RuleTypeHandler> handlerMap = new HashMap<>();

    /**
     * 静态实例，用于在Spring初始化阶段提供访问
     */
    private static RuleTypeHandlerFactory staticInstance;

    /**
     * 注册规则类型处理器
     */
    public void registerHandler(RuleTypeHandler handler) {
        handlerMap.put(handler.getRuleType(), handler);
    }

    /**
     * 注册多个规则类型处理器
     */
    public void registerHandlers(List<RuleTypeHandler> handlers) {
        for (RuleTypeHandler handler : handlers) {
            registerHandler(handler);
        }
    }

    /**
     * 获取规则类型处理器
     */
    @Nullable
    public RuleTypeHandler getHandler(int ruleType) {
        return handlerMap.get(ruleType);
    }

    /**
     * 设置静态实例（由RuleTypeHandlerRegistry调用）
     */
    @SuppressWarnings("LombokSetterMayBeUsed")
    public static void setStaticInstance(RuleTypeHandlerFactory instance) {
        staticInstance = instance;
    }

    /**
     * 获取工厂实例
     */
    @Nullable
    public static RuleTypeHandlerFactory getInstance() {
        // 优先使用静态实例，避免在Spring初始化阶段使用SpringContextHolder
        if (staticInstance != null) {
            return staticInstance;
        }

        try {
            return SpringContextHolder.getBean(RuleTypeHandlerFactory.class);
        } catch (Exception e) {
            // 如果SpringContextHolder还没有初始化，返回null
            return null;
        }
    }
}
