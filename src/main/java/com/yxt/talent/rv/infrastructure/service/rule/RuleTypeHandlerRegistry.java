package com.yxt.talent.rv.infrastructure.service.rule;

import com.yxt.talent.rv.application.authprj.handler.AuthPrjActivityResultHandler;
import com.yxt.talent.rv.application.authprj.handler.AuthPrjActivityScoreHandler;
import com.yxt.talent.rv.application.perf.handler.PerfFacadeHandler;
import com.yxt.talent.rv.application.perf.handler.PerfHandler;
import com.yxt.talent.rv.application.xpd.rule.handler.*;
import com.yxt.talent.rv.application.xpd.rule.RvRuleComponent;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 规则类型处理器注册器
 * 负责在应用启动时注册所有规则类型处理器
 */
@Component
@AllArgsConstructor
public class RuleTypeHandlerRegistry {

    private final RuleTypeHandlerFactory handlerFactory;
    private final ApplicationContext applicationContext;
    private final XpdDimCountHandler xpdDimCountHandler;
    private final XpdDimLevelHandler xpdDimLevelHandler;
    private final PerfHandler perfHandler;
    private final XpdTempDimCountHandler xpdTempDimCountHandler;
    private final XpdTempDimLevelHandler xpdTempDimLevelHandler;
    private final PerfFacadeHandler perfFacadeHandler;
    private final XpdJudgeRuleRatioHandler xpdJudgeRuleRatioHandler;
    private final XpdJudgeRuleScoreHandler xpdJudgeRuleScoreHandler;
    private final XpdJudgeRulePtgHandler xpdJudgeRulePtgHandler;
    private final AuthPrjActivityScoreHandler authPrjActivityScoreHandler;
    private final AuthPrjActivityResultHandler authPrjActivityResultHandler;
    
    @PostConstruct
    public void registerHandlers() {
        // 设置静态实例，确保在Spring初始化阶段可以访问
        RuleTypeHandlerFactory.setStaticInstance(handlerFactory);

        List<RuleTypeHandler> handlers = List.of(
            xpdDimCountHandler,
            xpdDimLevelHandler,
            perfHandler,
            xpdTempDimCountHandler,
            xpdTempDimLevelHandler,
            perfFacadeHandler,
            xpdJudgeRuleRatioHandler,
            xpdJudgeRuleScoreHandler,
            xpdJudgeRulePtgHandler,
            authPrjActivityScoreHandler,
            authPrjActivityResultHandler
        );

        handlerFactory.registerHandlers(handlers);

        // 在所有处理器注册完成后，注册规则列
        try {
            RvRuleComponent rvRuleComponent = applicationContext.getBean(RvRuleComponent.class);
            rvRuleComponent.initRuleColumns();
        } catch (Exception e) {
            // 如果获取失败，忽略错误，让Spring的正常初始化流程处理
        }
    }
}
