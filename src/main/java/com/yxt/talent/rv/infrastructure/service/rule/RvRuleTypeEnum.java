package com.yxt.talent.rv.infrastructure.service.rule;

import cn.hutool.core.lang.Pair;
import com.yxt.spsdk.common.bean.*;
import com.yxt.spsdk.common.enums.*;
import com.yxt.talent.rv.application.xpd.common.dto.QueryRuleColumnDto;
import com.yxt.talent.rv.application.xpd.common.dto.QueryRuleColumnValDto;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 规则类型枚举
 * 重构后的版本，使用处理器模式来处理具体的业务逻辑
 */
@Slf4j
public enum RvRuleTypeEnum implements RuleColTypeBase {

    /**
     * XPD维度数量规则 - mainData.bizId 为xpd项目id
     */
    XPD_DIM_COUNT(
        1, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.INPUT_AND_OPTION.getCode(), null),
        SpRuleInputValueTypeEnum.INT.getCode(), false),

    /**
     * XPD维度等级规则 - mainData.bizId 为xpd项目id
     */
    XPD_DIM_LEVEL(
        2, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.ENUM.getCode(), null),
        SpRuleInputValueTypeEnum.INT.getCode(), false),

    /**
     * 绩效规则 - bizId是rv_activity_perf.id
     */
    PERF(
        3, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.ENUM.getCode(), null),
        SpRuleInputValueTypeEnum.STRING.getCode(), false),

    /**
     * XPD临时维度数量规则 - bizId是gridId
     */
    XPD_TEMP_DIM_COUNT(
        4, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.INPUT_AND_OPTION.getCode(), null),
        SpRuleInputValueTypeEnum.INT.getCode(), false),

    /**
     * XPD临时维度等级规则 - bizId是gridId
     */
    XPD_TEMP_DIM_LEVEL(
        5, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.ENUM.getCode(), null),
        SpRuleInputValueTypeEnum.INT.getCode(), false),

    /**
     * 证书校验绩效规则
     */
    PERF_FACADE(
        6, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.ENUM.getCode(), null),
        SpRuleInputValueTypeEnum.STRING.getCode(), false),

    /**
     * XPD判断规则比例 - 盘点规则：综合判断
     */
    XPD_JUDGE_RULE_RATIO(
        7, SpConditionTypeEnum.DECIMAL,
        Pair.of(SpRuleValueTypeEnum.INPUT.getCode(), SpRuleValueShowTypeEnum.PTG.getCode()),
        SpRuleInputValueTypeEnum.STRING.getCode(), true),

    /**
     * XPD判断规则分数
     */
    XPD_JUDGE_RULE_SCORE(
        8, SpConditionTypeEnum.DECIMAL,
        Pair.of(SpRuleValueTypeEnum.INPUT.getCode(), null),
        SpRuleInputValueTypeEnum.FLOAT.getCode(), true),

    /**
     * XPD判断规则百分比
     */
    XPD_JUDGE_RULE_PTG(
        9, SpConditionTypeEnum.DECIMAL,
        Pair.of(SpRuleValueTypeEnum.INPUT.getCode(), SpRuleValueShowTypeEnum.PTG.getCode()),
        SpRuleInputValueTypeEnum.FLOAT.getCode(), true),

    /**
     * 认证项目活动得分规则 - bizId是认证项目ID
     */
    AUTH_PRJ_ACTIVITY_SCORE(
        10, SpConditionTypeEnum.DECIMAL,
        Pair.of(SpRuleValueTypeEnum.INPUT.getCode(), null),
        SpRuleInputValueTypeEnum.FLOAT.getCode(), false),

    /**
     * 认证项目活动结果规则 - bizId是认证项目ID
     */
    AUTH_PRJ_ACTIVITY_RESULT(
        11, SpConditionTypeEnum.INT,
        Pair.of(SpRuleValueTypeEnum.ENUM.getCode(), null),
        SpRuleInputValueTypeEnum.INT.getCode(), false);

    // 枚举字段
    private final int columnType;
    private final SpConditionTypeEnum conditionType;
    private final Pair<Integer, Integer> valueType;
    private final int inputValueType;
    private final boolean singleColumn;

    RvRuleTypeEnum(
        int columnType, SpConditionTypeEnum conditionType,
        Pair<Integer, Integer> valueType, int inputValueType, boolean singleColumn) {
        this.columnType = columnType;
        this.conditionType = conditionType;
        this.valueType = valueType;
        this.inputValueType = inputValueType;
        this.singleColumn = singleColumn;
    }

    /**
     * 获取对应的处理器
     */
    @jakarta.annotation.Nullable
    private RuleTypeHandler getHandler() {
        RuleTypeHandlerFactory factory = RuleTypeHandlerFactory.getInstance();
        if (factory == null) {
            return null;
        }
        return factory.getHandler(this.columnType);
    }

    // 实现 RuleColTypeBase 接口方法

    @Override
    public int columnType() {
        return columnType;
    }

    @Override
    public SpConditionTypeEnum conditionType() {
        return conditionType;
    }

    @Override
    public int valueType() {
        return valueType.getKey();
    }

    @Override
    public int valueShowType() {
        Integer showType = valueType.getValue();
        if (showType == null) {
            return RuleColTypeBase.super.valueShowType();
        }
        return showType;
    }

    @Override
    public int inputValueType() {
        return inputValueType;
    }

    @Override
    public List<RuleOptionBean> allColumns(RuleMainBase mainData) {
        RuleTypeHandler handler = getHandler();
        if (handler == null) {
            return List.of();
        }
        QueryRuleColumnDto queryDto = new QueryRuleColumnDto();
        queryDto.setMainData(mainData);
        queryDto.setQueryAll(true);
        return handler.getColumnOptions(queryDto);
    }

    @Override
    public List<RuleOptionBean> columns(RuleMainBase mainData, List<String> columnIds) {
        RuleTypeHandler handler = getHandler();
        if (handler == null) {
            return List.of();
        }
        QueryRuleColumnDto queryDto = new QueryRuleColumnDto();
        queryDto.setMainData(mainData);
        queryDto.setQueryAll(true);
        queryDto.setColumnIds(columnIds);
        return handler.getColumnOptions(queryDto);
    }

    @Override
    public SpCompareOperator[] acceptOperator() {
        RuleTypeHandler handler = getHandler();
        if (handler != null) {
            return handler.getSupportedOperators();
        }
        return RuleColTypeBase.super.acceptOperator();
    }

    @Override
    public Object defaultValue(String columnId) {
        RuleTypeHandler handler = getHandler();
        if (handler != null) {
            return handler.getDefaultColumnValue();
        }
        return conditionType.getDefaultDataVal();
    }

    @Override
    public Object convertValue(
        RuleMainBase mainData, SpRuleColumnBean columnBean, String valueStr) {
        RuleTypeHandler handler = getHandler();
        if (handler != null) {
            Object result = handler.convertValue(mainData, columnBean, valueStr);
            if (result != null) {
                return result;
            }
        }
        return RuleColTypeBase.super.convertValue(mainData, columnBean, valueStr);
    }

    @Override
    @SuppressWarnings("rawtypes")
    public List<RuleColumnValueBean> columnValue(RuleMainBase mainData, List<String> columnIds, List objectIds) {
        RuleTypeHandler handler = getHandler();
        if (handler == null) {
            return List.of();
        }
        QueryRuleColumnValDto queryDto = new QueryRuleColumnValDto();
        queryDto.setMainData(mainData);
        queryDto.setColumnIds(columnIds);
        queryDto.setObjectIds(objectIds);
        return handler.getColumnValues(queryDto);
    }

    @Override
    public List<RuleEnumValueBean> enumValues(RuleMainBase mainData) {
        RuleTypeHandler handler = getHandler();
        if (handler != null) {
            return handler.getEnumValues(mainData);
        }
        return null;
    }

    @Override
    public SpRuleDemoCopyBean demoCopyConfig() {
        RuleTypeHandler handler = getHandler();
        if (handler != null) {
            return handler.getDemoCopyConfig();
        }
        return null;
    }

    @Override
    public boolean singleColumn() {
        return singleColumn;
    }
}
