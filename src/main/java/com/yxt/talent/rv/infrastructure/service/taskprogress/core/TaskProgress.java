package com.yxt.talent.rv.infrastructure.service.taskprogress.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务进度实体
 * 记录任务的当前状态和相关信息
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskProgress {
    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 当前状态码
     */
    private String stateCode;

    /**
     * 状态描述信息
     */
    private String message;

    /**
     * 状态更新时间
     */
    private LocalDateTime timestamp;

    /**
     * 额外数据，用于存储业务相关的其他信息
     */
    private String extraData;
    
    /**
     * 设置状态
     * 
     * @param state 状态对象
     */
    public void setState(TaskState state) {
        this.stateCode = state.getCode();
    }
    
    /**
     * 获取状态对象
     * 
     * @param stateFactory 状态工厂
     * @return 状态对象
     */
    public TaskState getState(TaskStateFactory stateFactory) {
        return stateFactory.getStateByCode(stateCode);
    }
    
    /**
     * 判断当前状态是否为终态
     * 
     * @param stateFactory 状态工厂
     * @return 是否为终态
     */
    public boolean isInFinalState(TaskStateFactory stateFactory) {
        return getState(stateFactory).isFinalState();
    }
}
