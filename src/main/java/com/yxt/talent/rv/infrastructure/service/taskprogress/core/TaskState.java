package com.yxt.talent.rv.infrastructure.service.taskprogress.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务状态值对象
 * 表示任务的状态信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskState {
    /**
     * 状态码
     */
    private String code;
    
    /**
     * 状态名称
     */
    private String name;
    
    /**
     * 是否为终态（完成或失败等）
     */
    private boolean finalState;
}
