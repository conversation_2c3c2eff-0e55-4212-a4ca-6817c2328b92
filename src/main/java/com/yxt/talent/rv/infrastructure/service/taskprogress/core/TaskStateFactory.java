package com.yxt.talent.rv.infrastructure.service.taskprogress.core;

import java.util.Set;

/**
 * 任务状态工厂接口
 * 负责创建和管理任务状态对象
 */
public interface TaskStateFactory {
    /**
     * 根据状态码获取状态对象
     *
     * @param code 状态码
     * @return 状态对象
     */
    TaskState getStateByCode(String code);
    
    /**
     * 获取所有可能的状态
     *
     * @return 状态集合
     */
    Set<TaskState> getAllStates();
    
    /**
     * 获取初始状态
     *
     * @return 初始状态
     */
    TaskState getInitialState();
}
