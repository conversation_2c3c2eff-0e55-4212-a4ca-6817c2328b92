package com.yxt.talent.rv.infrastructure.service.taskprogress.event;

import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 任务进度事件基类
 */
@Getter
public abstract class TaskProgressEvent extends ApplicationEvent {
    private final TaskProgress taskProgress;

    public TaskProgressEvent(TaskProgress taskProgress) {
        super(taskProgress);
        this.taskProgress = taskProgress;
    }
}
