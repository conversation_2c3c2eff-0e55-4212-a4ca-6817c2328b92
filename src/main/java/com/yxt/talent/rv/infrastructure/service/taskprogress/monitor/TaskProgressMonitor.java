package com.yxt.talent.rv.infrastructure.service.taskprogress.monitor;

import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskLifecycle;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskState;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskStateFactory;
import com.yxt.talent.rv.infrastructure.service.taskprogress.event.TaskProgressEvent;
import com.yxt.talent.rv.infrastructure.service.taskprogress.event.TaskStartedEvent;
import com.yxt.talent.rv.infrastructure.service.taskprogress.event.TaskStateChangedEvent;
import com.yxt.talent.rv.infrastructure.service.taskprogress.storage.TaskProgressStorage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 任务进度监控器
 * 核心服务类，提供任务进度管理的主要功能
 */
@Slf4j
@Service
public class TaskProgressMonitor {
    private final Map<String, TaskLifecycle> lifecycles;
    private final TaskProgressStorage storage;
    private final ApplicationEventPublisher eventPublisher;

    public TaskProgressMonitor(List<TaskLifecycle> lifecycles,
                             TaskProgressStorage storage,
                             ApplicationEventPublisher eventPublisher) {
        this.lifecycles = lifecycles.stream()
                .collect(Collectors.toMap(TaskLifecycle::getType, Function.identity(), (u, v) -> v));
        this.storage = storage;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 开始任务
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     */
    public void start(String taskType, String taskId) {
        TaskLifecycle lifecycle = getLifecycle(taskType);
        TaskState initialState = lifecycle.getStateFactory().getInitialState();
        
        TaskProgress progress = new TaskProgress();
        progress.setTaskType(taskType);
        progress.setTaskId(taskId);
        progress.setState(initialState);
        progress.setTimestamp(LocalDateTime.now());

        storage.save(progress);
        publishEvent(new TaskStartedEvent(progress));
    }

    /**
     * 更新任务状态
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     * @param newState 新状态
     * @param message  状态描述
     */
    public void updateState(String taskType, String taskId, TaskState newState, String message) {
        TaskLifecycle lifecycle = getLifecycle(taskType);
        TaskStateFactory stateFactory = lifecycle.getStateFactory();
        TaskProgress current = getProgress(taskType, taskId)
                .orElseThrow(() -> new IllegalStateException("Task not found: " + taskType + ":" + taskId));

        // 验证状态转换
        TaskState currentState = current.getState(stateFactory);
        if (!lifecycle.getTransitionRule().canTransit(currentState, newState)) {
            throw new IllegalStateException(String.format(
                    "Invalid state transition from %s to %s for task %s:%s",
                    currentState.getCode(), newState.getCode(), taskType, taskId));
        }

        // 更新状态
        TaskProgress updated = new TaskProgress();
        updated.setTaskType(current.getTaskType());
        updated.setTaskId(current.getTaskId());
        updated.setState(newState);
        updated.setMessage(message);
        updated.setTimestamp(LocalDateTime.now());
        updated.setExtraData(current.getExtraData());

        storage.save(updated);
        publishEvent(new TaskStateChangedEvent(updated));
    }

    /**
     * 获取任务进度
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     * @return 任务进度
     */
    public Optional<TaskProgress> getProgress(String taskType, String taskId) {
        return storage.get(taskType, taskId);
    }

    /**
     * 获取指定类型的所有活动任务
     *
     * @param taskType 任务类型
     * @return 活动任务列表
     */
    public List<TaskProgress> getActiveTasks(String taskType) {
        return storage.getActiveByType(taskType);
    }

    /**
     * 删除任务进度
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     */
    public void removeProgress(String taskType, String taskId) {
        storage.remove(taskType, taskId);
    }

    private TaskLifecycle getLifecycle(String taskType) {
        TaskLifecycle lifecycle = lifecycles.get(taskType);
        if (lifecycle == null) {
            throw new IllegalArgumentException("Unknown task type: " + taskType);
        }
        return lifecycle;
    }

    private void publishEvent(TaskProgressEvent event) {
        try {
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("Failed to publish event: {}", event, e);
        }
    }
}
