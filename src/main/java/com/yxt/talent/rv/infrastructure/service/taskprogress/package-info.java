/**
 * 任务进度监控系统
 * 
 * <p>该包提供了一个通用的任务进度监控框架，用于跟踪和管理异步任务的状态和生命周期。
 * 系统的主要特点包括：</p>
 * 
 * <ul>
 *   <li>灵活的状态管理</li>
 *   <li>可配置的状态转换规则</li>
 *   <li>基于Redis的持久化存储</li>
 *   <li>事件驱动的状态变更通知</li>
 * </ul>
 * 
 * <h2>主要组件</h2>
 * 
 * <h3>1. 核心接口 (core包)</h3>
 * <pre>
 * - TaskState: 定义任务状态
 * - TaskLifecycle: 定义任务生命周期
 * - StateTransitionRule: 定义状态转换规则
 * - TaskProgress: 任务进度实体
 * </pre>
 * 
 * <h3>2. 监控器 (monitor包)</h3>
 * <pre>
 * - TaskProgressMonitor: 任务进度监控的核心服务类
 * </pre>
 * 
 * <h3>3. 存储 (storage包)</h3>
 * <pre>
 * - TaskProgressStorage: 存储接口
 * - RedisTaskProgressStorage: Redis实现
 * </pre>
 * 
 * <h3>4. 事件 (event包)</h3>
 * <pre>
 * - TaskProgressEvent: 事件基类
 * - TaskStartedEvent: 任务开始事件
 * - TaskStateChangedEvent: 状态变更事件
 * </pre>
 * 
 * <h2>使用示例</h2>
 * 
 * <h3>1. 定义业务特定的状态</h3>
 * <pre>{@code
 * public enum CustomTaskState implements TaskState {
 *     PENDING("pending", "等待中", false),
 *     PROCESSING("processing", "处理中", false),
 *     COMPLETED("completed", "已完成", true),
 *     FAILED("failed", "失败", true);
 *     
 *     private final String code;
 *     private final String name;
 *     private final boolean finalState;
 *     
 *     // 构造函数和getter方法...
 * }
 * }</pre>
 * 
 * <h3>2. 实现任务生命周期</h3>
 * <pre>{@code
 * @Component
 * public class CustomTaskLifecycle implements TaskLifecycle {
 *     public static final String TYPE = "CUSTOM_TASK";
 *     
 *     @Override
 *     public String getType() {
 *         return TYPE;
 *     }
 *     
 *     @Override
 *     public Set<TaskState> getAllStates() {
 *         return new HashSet<>(Set.of(CustomTaskState.values()));
 *     }
 *     
 *     @Override
 *     public TaskState getInitialState() {
 *         return CustomTaskState.PENDING;
 *     }
 *     
 *     @Override
 *     public StateTransitionRule getTransitionRule() {
 *         return new CustomTransitionRule();
 *     }
 * }
 * }</pre>
 * 
 * <h3>3. 在业务服务中使用</h3>
 * <pre>{@code
 * @Service
 * @RequiredArgsConstructor
 * public class CustomBusinessService {
 *     private final TaskProgressMonitor taskProgressMonitor;
 *     
 *     public void processTask(String taskId) {
 *         // 开始任务
 *         taskProgressMonitor.start(CustomTaskLifecycle.TYPE, taskId);
 *         
 *         try {
 *             // 更新为处理中状态
 *             taskProgressMonitor.updateState(
 *                 CustomTaskLifecycle.TYPE,
 *                 taskId,
 *                 CustomTaskState.PROCESSING,
 *                 "开始处理任务"
 *             );
 *             
 *             // 执行业务逻辑...
 *             
 *             // 更新为完成状态
 *             taskProgressMonitor.updateState(
 *                 CustomTaskLifecycle.TYPE,
 *                 taskId,
 *                 CustomTaskState.COMPLETED,
 *                 "任务处理完成"
 *             );
 *         } catch (Exception e) {
 *             // 更新为失败状态
 *             taskProgressMonitor.updateState(
 *                 CustomTaskLifecycle.TYPE,
 *                 taskId,
 *                 CustomTaskState.FAILED,
 *                 e.getMessage()
 *             );
 *             throw e;
 *         }
 *     }
 *     
 *     public TaskProgressVO getTaskProgress(String taskId) {
 *         return taskProgressMonitor.getProgress(CustomTaskLifecycle.TYPE, taskId)
 *             .map(progress -> new TaskProgressVO(
 *                 progress.getState().getCode(),
 *                 progress.getState().getName(),
 *                 progress.getMessage(),
 *                 progress.getState().isFinalState()
 *             ))
 *             .orElse(null);
 *     }
 * }
 * }</pre>
 * 
 * <h3>4. 监听任务状态变更</h3>
 * <pre>{@code
 * @Component
 * @RequiredArgsConstructor
 * public class TaskProgressEventListener {
 *     
 *     @EventListener
 *     public void onTaskStarted(TaskStartedEvent event) {
 *         TaskProgress progress = event.getProgress();
 *         // 处理任务开始事件...
 *     }
 *     
 *     @EventListener
 *     public void onTaskStateChanged(TaskStateChangedEvent event) {
 *         TaskProgress progress = event.getProgress();
 *         // 处理状态变更事件...
 *     }
 * }
 * }</pre>
 * 
 * <h2>最佳实践</h2>
 * <ul>
 *   <li>为每个业务场景创建独立的状态枚举和生命周期实现</li>
 *   <li>在状态转换规则中严格定义允许的状态转换</li>
 *   <li>使用事件监听器处理状态变更的副作用</li>
 *   <li>合理设置Redis存储的过期时间</li>
 * </ul>
 * 
 * @see com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskState
 * @see com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskLifecycle
 * @see com.yxt.talent.rv.infrastructure.service.taskprogress.monitor.TaskProgressMonitor
 */
package com.yxt.talent.rv.infrastructure.service.taskprogress;
