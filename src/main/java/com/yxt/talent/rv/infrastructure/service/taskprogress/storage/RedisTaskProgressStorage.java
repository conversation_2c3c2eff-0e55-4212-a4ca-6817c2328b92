package com.yxt.talent.rv.infrastructure.service.taskprogress.storage;

import com.alibaba.fastjson.JSON;
import com.yxt.common.repo.RedisRepository;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskLifecycle;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Redis实现的任务进度存储
 */
@Slf4j
@Component
public class RedisTaskProgressStorage implements TaskProgressStorage {
    private final RedisRepository talentRedisRepository;
    private final Map<String, TaskLifecycle> lifecycleMap;
    
    private static final String KEY_PATTERN = "sprv:common:task:progress:%s:%s";
    private static final String ACTIVE_TASKS_KEY = "sprv:common:task:active:%s";
    private static final long DEFAULT_EXPIRE_HOURS = 1;
    
    public RedisTaskProgressStorage(RedisRepository talentRedisRepository, List<TaskLifecycle> lifecycles) {
        this.talentRedisRepository = talentRedisRepository;
        this.lifecycleMap = lifecycles.stream()
                .collect(Collectors.toMap(TaskLifecycle::getType, Function.identity(), (u, v) -> v));
    }

    @Override
    public void save(TaskProgress progress) {
        String key = formatKey(progress.getTaskType(), progress.getTaskId());
        String json = JSON.toJSONString(progress);
        talentRedisRepository.opsForValue().set(key, json, DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS);

        // 如果不是终态，加入活动任务集合
        TaskLifecycle lifecycle = getLifecycleByType(progress.getTaskType());
        if (lifecycle != null) {
            boolean isFinalState = progress.isInFinalState(lifecycle.getStateFactory());
            String activeKey = String.format(ACTIVE_TASKS_KEY, progress.getTaskType());
            
            if (!isFinalState) {
                talentRedisRepository.opsForSet().add(activeKey, progress.getTaskId());
            } else {
                // 如果是终态，从活动任务集合中移除
                talentRedisRepository.opsForSet().remove(activeKey, progress.getTaskId());
            }
        } else {
            log.warn("Unknown task type: {}, cannot determine if it's in final state", progress.getTaskType());
        }
    }

    @Override
    public Optional<TaskProgress> get(String taskType, String taskId) {
        String key = formatKey(taskType, taskId);
        String json = talentRedisRepository.opsForValue().get(key);
        return Optional.ofNullable(json)
                .map(j -> JSON.parseObject(j, TaskProgress.class));
    }

    @Override
    public void remove(String taskType, String taskId) {
        String key = formatKey(taskType, taskId);
        String activeKey = String.format(ACTIVE_TASKS_KEY, taskType);

        talentRedisRepository.delete(key);
        talentRedisRepository.opsForSet().remove(activeKey, taskId);
    }

    @Override
    public List<TaskProgress> batchGet(String taskType, Collection<String> taskIds) {
        List<String> keys = taskIds.stream()
                .map(id -> formatKey(taskType, id))
                .collect(Collectors.toList());

        List<String> values = talentRedisRepository.opsForValue().multiGet(keys);

        return values != null ? values.stream()
            .filter(Objects::nonNull)
            .map(json -> JSON.parseObject(json, TaskProgress.class))
            .collect(Collectors.toList()) : new ArrayList<>();
    }

    @Override
    public List<TaskProgress> getActiveByType(String taskType) {
        String activeKey = String.format(ACTIVE_TASKS_KEY, taskType);
        Set<String> activeTaskIds = talentRedisRepository.opsForSet().members(activeKey);
        
        if (activeTaskIds == null || activeTaskIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return batchGet(taskType, activeTaskIds);
    }

    private String formatKey(String taskType, String taskId) {
        return String.format(KEY_PATTERN, taskType, taskId);
    }

    private TaskLifecycle getLifecycleByType(String taskType) {
        return lifecycleMap.get(taskType);
    }
}
