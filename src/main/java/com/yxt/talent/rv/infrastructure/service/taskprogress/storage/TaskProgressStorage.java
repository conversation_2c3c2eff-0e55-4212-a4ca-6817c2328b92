package com.yxt.talent.rv.infrastructure.service.taskprogress.storage;

import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskProgress;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 任务进度存储接口
 */
public interface TaskProgressStorage {
    /**
     * 保存任务进度
     *
     * @param progress 任务进度
     */
    void save(TaskProgress progress);

    /**
     * 获取任务进度
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     * @return 任务进度
     */
    Optional<TaskProgress> get(String taskType, String taskId);

    /**
     * 删除任务进度
     *
     * @param taskType 任务类型
     * @param taskId   任务ID
     */
    void remove(String taskType, String taskId);

    /**
     * 批量获取任务进度
     *
     * @param taskType 任务类型
     * @param taskIds  任务ID列表
     * @return 任务进度列表
     */
    List<TaskProgress> batchGet(String taskType, Collection<String> taskIds);

    /**
     * 获取指定类型的所有进行中任务
     *
     * @param taskType 任务类型
     * @return 进行中的任务列表
     */
    List<TaskProgress> getActiveByType(String taskType);
}
