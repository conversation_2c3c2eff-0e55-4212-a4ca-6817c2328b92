package com.yxt.talent.rv.infrastructure.service.userinfo;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要进行用户信息填充的字段
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface UserInfoFill {
    /**
     * 用户ID字段的名称，如果为空则使用字段本身的值作为用户ID
     */
    String userIdField() default "";

    /**
     * 填充类型，默认为BASIC（基础信息）
     */
    UserInfoFillType type();

    /**
     * 用户信息填充类型
     */
    enum UserInfoFillType {

        /**
         * 姓名
         */
        FULL_NAME,

        /**
         * 账号
         */
        USER_NAME,

        /**
         * 状态：0-禁用,1-启用, 2-已删除
         */
        STATUS,

        /**
         * 部门ID
         */
        DEPT_ID,
        FULL_DEPT_NAME,
        SHORT_DEPT_NAME,


        /**
         * 岗位
         */
        POSITION_ID,
        POSITION_NAME,

        /**
         * 职级
         */
        GRADE_ID,
        GRADE_NAME,

        /**
         * 工号
         */
        USER_NO,

        /**
         * 头像
         */
        IMG_URL;
    }

} 