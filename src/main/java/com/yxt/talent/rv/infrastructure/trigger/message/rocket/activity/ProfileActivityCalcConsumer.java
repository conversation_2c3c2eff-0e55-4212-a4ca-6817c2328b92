package com.yxt.talent.rv.infrastructure.trigger.message.rocket.activity;

import com.alibaba.fastjson.JSON;
import com.yxt.talent.rv.application.activity.ActivityProfileService;
import com.yxt.talent.rv.application.activity.dto.ProfileActivityCalcMqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PROFILE_ACTIVITY_CALC;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX         + TOPIC_PROFILE_ACTIVITY_CALC, topic = TOPIC_PROFILE_ACTIVITY_CALC, consumeThreadNumber = 1, consumeTimeout = 30)
public class ProfileActivityCalcConsumer implements RocketMQListener<ProfileActivityCalcMqDTO> {
    private final ActivityProfileService activityProfileService;

    @Override
    public void onMessage(ProfileActivityCalcMqDTO message) {
        log.info("ProfileActivityCalcConsumer param ={}", JSON.toJSONString(message));
        try {
            activityProfileService.computeUserMatchResult(message.getOrgId(), message.getActProfId(),
                    message.getOptUserId());
        } catch (Exception e) {
            log.warn("ProfileActivityCalcConsumer param ={},error=", JSON.toJSONString(message), e);
        }
    }
}
