package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 认证项目用户结果处理消息体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthPrjResultMsg implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 活动ID
     */
    private String authPrjId;

    /**
     * 链路追踪ID
     */
    private String traceId;
}
