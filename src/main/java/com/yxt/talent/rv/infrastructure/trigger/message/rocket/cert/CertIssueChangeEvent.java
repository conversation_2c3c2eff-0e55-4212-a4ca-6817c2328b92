package com.yxt.talent.rv.infrastructure.trigger.message.rocket.cert;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 证书状态变更事件
 */
@Data
public class CertIssueChangeEvent {

    @Schema(description = "人员证书事件类型 (0:颁发成功； 1:已吊销； 2:已删除； 3:已过期)")
    private Integer type;

    @Schema(description = "颁发记录id")
    private String issueId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "证书模板id")
    private String cerId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "来源类型（0：系统发证、1：项目发证、2：考试发证、3：岗位能力、4：面授中心、 5：认证中心）")
    private Integer sourceType;

    @Schema(description = "来源Id（考试Id、培训Id、岗位能力Id、面授Id）- 系统发证时为空值")
    private String sourceId;
}
