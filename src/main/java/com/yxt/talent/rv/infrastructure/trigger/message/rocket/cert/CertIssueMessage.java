package com.yxt.talent.rv.infrastructure.trigger.message.rocket.cert;

import com.yxt.cerapifacade.bean.CerIssue4Post;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 证书颁发MQ消息
 */
@Data
public class CertIssueMessage {

    @Schema(description = "业务类型（1300:证书发放）")
    private Integer businessType = 1300;

    @Schema(description = "批次id(业务唯一)")
    private String batchId;

    @Schema(description = "机构id")
    private String orgId;

    @Schema(description = "日志时间")
    private Date logTime;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "消息体")
    private CerIssue4Post msgBody;
}
