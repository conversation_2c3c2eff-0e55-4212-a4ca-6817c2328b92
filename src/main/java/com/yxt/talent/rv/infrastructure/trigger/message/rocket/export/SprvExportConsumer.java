package com.yxt.talent.rv.infrastructure.trigger.message.rocket.export;

import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.ubiz.export.core.AbstractExportMqCustomer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_SPTALENTRV_EXPORT_FILE;

@Component
@AllArgsConstructor
@Slf4j
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_SPTALENTRV_EXPORT_FILE, topic = TOPIC_SPTALENTRV_EXPORT_FILE, messageModel = MessageModel.CLUSTERING, consumeThreadNumber = 2, consumeTimeout = 30)
public class SprvExportConsumer extends AbstractExportMqCustomer implements RocketMQListener<String> {
    @Override
    public void onMessage(String message) {
        log.info("ExportMqConsumer {}", message);
        message(message);
    }
}