package com.yxt.talent.rv.infrastructure.trigger.message.rocket.org;

import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.org.event.DemoOrgInitStep1Event;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_SE_C_UDP_ORG_INITIALIZE;

/**
 * Demo机构初始化第一步：做一些初始化动作，完成之后机构被放入demo机构池中等待开通（目前demo机构只用于内部演示，不用于客户正式机构）
 */
@Component
@Slf4j
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_SE_C_UDP_ORG_INITIALIZE,         topic = TOPIC_SE_C_UDP_ORG_INITIALIZE,         consumeThreadNumber = 2, consumeTimeout = 30)
@RequiredArgsConstructor
public class DemoOrgInitStep1Consumer implements RocketMQListener<DemoOrgInitStep1Event> {

    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(DemoOrgInitStep1Event message) {
        try {
            log.debug("LOG12895:{}", bean2Json(message, ALWAYS));
            eventPublisher.publish(message);
        } catch (Exception e) {
            log.error("LOG20740:{} ", bean2Json(message, ALWAYS), e);
        }
    }

}
