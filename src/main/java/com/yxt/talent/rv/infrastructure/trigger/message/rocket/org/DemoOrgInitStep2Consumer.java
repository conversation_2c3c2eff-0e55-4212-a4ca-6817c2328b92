package com.yxt.talent.rv.infrastructure.trigger.message.rocket.org;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.udpfacade.bean.demo.DemoInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_SE_C_UDP_DEMO_INITIALIZE;

/**
 * Demo机构初始化第二步：正式开通机构，完成之后机构即为可用状态
 */
@Component
@Slf4j
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_SE_C_UDP_DEMO_INITIALIZE,         topic = TOPIC_SE_C_UDP_DEMO_INITIALIZE,         consumeThreadNumber = 2, consumeTimeout = 30)
@RequiredArgsConstructor
public class DemoOrgInitStep2Consumer implements RocketMQListener<DemoInit4Mq> {

    @Override
    public void onMessage(DemoInit4Mq message) {
        // 暂无逻辑需要在这一步处理
    }

}