package com.yxt.talent.rv.infrastructure.trigger.message.rocket.user;

import com.yxt.common.util.BeanHelper;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.domain.user.event.UserTransferResourceMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_CALI_MEET;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_DMP;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_PRJ;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE;

@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE,
    topic = TOPIC_PE_C_SHARE_TRANSFER_USER_RESOURCE,
    selectorExpression = TRANSFERABLE_RESOURCES_CODE_PRJ + "||" + TRANSFERABLE_RESOURCES_CODE_DMP + "||" + TRANSFERABLE_RESOURCES_CODE_CALI_MEET,
    consumeThreadNumber = 5, consumeTimeout = 30)
@RequiredArgsConstructor
public class UserTransferableResourceConsumer implements RocketMQListener<MessageExt> {

    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            // 获取消息的tag
            String resourceCode = messageExt.getTags();
            log.debug("LOG67500:{}", resourceCode);

            if (!ArrayUtils.contains(new String[]{
                    TRANSFERABLE_RESOURCES_CODE_PRJ,
                    TRANSFERABLE_RESOURCES_CODE_DMP,
                    TRANSFERABLE_RESOURCES_CODE_CALI_MEET}, resourceCode)) {
                return;
            }

            // 处理消息
            UserTransferResourceMessageEvent message = convertToCustomMessage(messageExt, resourceCode);
            log.debug("LOG67470:{}", BeanHelper.bean2Json(message, ALWAYS));

            if (StringUtils.isBlank(message.getFrom()) || StringUtils.isBlank(message.getTo()) ||
                StringUtils.isBlank(message.getOrgId())) {
                return;
            }

            if (Objects.equals(message.getFrom(), message.getTo())) {
                return;
            }

            eventPublisher.publish(message);
        } catch (Throwable e) {
            log.error("LOG67480:", e);
        }
    }

    private UserTransferResourceMessageEvent convertToCustomMessage(
            MessageExt messageExt, String resourceCode) {
        String str = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        UserTransferResourceMessageEvent messageEvent =
                BeanHelper.json2Bean(str, UserTransferResourceMessageEvent.class);
        messageEvent.setResourceCode(resourceCode);
        return messageEvent;
    }
}
