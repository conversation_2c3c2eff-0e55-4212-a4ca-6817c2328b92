package com.yxt.talent.rv.infrastructure.trigger.message.rocket.xpd;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.spsdk.common.bean.SpInitData4Mq;
import com.yxt.spsdk.common.constants.SpMqConstants;
import com.yxt.spsdk.logsave.LogRecorder;
import com.yxt.talent.rv.application.xpd.xpd.XpdInitDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;

/**
 * 人岗匹配项目-任务维度是否匹配-计算
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX + SpMqConstants.MQ_SP_INIT_DATA,         topic = SpMqConstants.MQ_SP_INIT_DATA, consumeTimeout = 30)
public class XpdIniDataConsumer implements RocketMQListener<SpInitData4Mq>{

    private final XpdInitDataService xpdInitDataService;

    @Override
    public void onMessage(SpInitData4Mq event) {
        try {
            if (SpMqConstants.SP_INIT_MODULE_SD.equals(event.getModuleCode())) {
                log.info("initGridInnerData start orgId {}", event.getOrgId());
                xpdInitDataService.initGridInnerData(event.getOrgId(), false);
                log.info("initGridInnerData end orgId {}", event.getOrgId());
            }
        } catch (Exception e) {
            LogRecorder.error(event.getOrgId(), log, "initGridInnerData fail", e);
        }
    }

}
