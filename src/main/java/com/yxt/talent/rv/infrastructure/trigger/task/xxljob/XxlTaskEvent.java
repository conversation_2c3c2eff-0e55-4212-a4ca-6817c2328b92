package com.yxt.talent.rv.infrastructure.trigger.task.xxljob;

import com.xxl.job.core.util.ShardingUtil;
import com.yxt.event.message.TaskEvent;
import com.yxt.task.Task;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Slf4j
@Setter
@Getter
@ToString(callSuper = true)
@SuperBuilder(builderMethodName = "xxlTaskEventBuilder")
public class XxlTaskEvent<T> extends TaskEvent {

    public XxlTaskEvent() {
        super();
    }

    public XxlTaskEvent(ShardingUtil.ShardingVO shardingVO, Task<T> task) {
        this.sharding = new TaskSharding(shardingVO);
        this.task = task;
    }

    /**
     * 任务分片信息
     */
    private TaskSharding sharding;

    /**
     * 任务具体信息
     */
    private Task<T> task;

    @Data
    public static class TaskSharding implements Serializable {

        // 当前执行器被分配的分片索引
        @Nullable
        private Integer index;

        // 当前可用执行器总数
        @Nullable
        private Integer total;

        public TaskSharding(ShardingUtil.ShardingVO shardingVO) {
            if (shardingVO != null) {
                this.index = shardingVO.getIndex();
                this.total = shardingVO.getTotal();
            }
        }
    }

}
