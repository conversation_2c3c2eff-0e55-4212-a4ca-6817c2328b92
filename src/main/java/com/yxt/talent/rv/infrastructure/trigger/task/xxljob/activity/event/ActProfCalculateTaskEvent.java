package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.activity.event;

import com.xxl.job.core.util.ShardingUtil;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.XxlTaskEvent;
import com.yxt.task.Task;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
@Slf4j
@Setter
@Getter
@SuperBuilder
@ToString(callSuper = true)
public class ActProfCalculateTaskEvent extends XxlTaskEvent<String> {

    public ActProfCalculateTaskEvent(ShardingUtil.ShardingVO shardingVO, Task<String> task) {
        super(shardingVO, task);
    }
}
