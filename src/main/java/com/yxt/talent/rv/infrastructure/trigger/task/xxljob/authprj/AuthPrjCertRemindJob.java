package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.authprj;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import com.yxt.criteria.Result;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.authprj.event.AuthPrjCertRemindTaskEvent;
import com.yxt.task.Task;
import com.yxt.task.TaskHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 认证项目-证书提醒任务，每天上午8点执行一次
 * 查询认证项目里每个模板证书的颁发记录，找出即将过期的人员，发送提醒
 *
 * <AUTHOR> Lastname
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthPrjCertRemindJob implements TaskHandler<Task<String>> {

    private final EventPublisher eventPublisher;

    @XxlJob(value = "authPrjCertRemindJobHandler")
    public ReturnT<String> execute(String param) {
        Result<Void> handle = doHandle(Task.of(param));
        return handle.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    @Override
    public Result<Void> handle(Task<String> task) {
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        AuthPrjCertRemindTaskEvent event = new AuthPrjCertRemindTaskEvent(shardingVo, task);
        eventPublisher.publish(event);
        return Result.success();
    }

}
