package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.authprj.event;

import com.xxl.job.core.util.ShardingUtil;
import com.yxt.event.message.MessageEvent;
import com.yxt.task.Task;
import lombok.*;

/**
 * 认证项目证书提醒任务事件
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class AuthPrjCertRemindTaskEvent extends MessageEvent {
    
    private ShardingUtil.ShardingVO shardingVo;
    
    private Task<String> task;
}
