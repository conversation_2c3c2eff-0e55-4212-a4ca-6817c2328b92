package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.sys;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import com.yxt.criteria.Result;
import com.yxt.event.EventPublisher;
import com.yxt.task.Task;
import com.yxt.task.TaskHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时触发系统日志通知
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SysLogNoticeJob implements TaskHandler<Task<String>> {

    private final EventPublisher eventPublisher;

    @XxlJob(value = "sysLogNoticeJobHandler")
    public ReturnT<String> execute(String param) {
        Result<Void> taskResult = doHandle(Task.of(param));
        return taskResult.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    @Override
    public Result<Void> handle(Task<String> task) {
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        SysLogNoticeTaskEvent event = new SysLogNoticeTaskEvent(shardingVo, task);
        eventPublisher.publish(event);
        return Result.success();
    }

}
