create table rv_activity
(
    id               char(36)         not null comment '主键id',
    org_id           char(36)         not null default '' comment '机构id',
    actv_reg_id      char(16)         not null default '' comment '活动/项目的具体类型(UACD注册表中定义)',
    actv_name        varchar(200)     not null comment '活动/项目名称',
    actv_type        tinyint unsigned not null comment '类型(1-活动, 2-项目)',
    actv_status      tinyint unsigned not null default '0' comment '状态(0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回)',
    time_model       tinyint unsigned not null default '0' comment '时间模式(0-固定, 1-相对)',
    start_time       datetime(3)               default null comment '开始时间',
    end_time         datetime(3)               default null comment '结束时间',
    start_day_offset int                       default null comment '相对开始天数',
    end_day_offset   int                       default null comment '相对截止天数',
    image_url        varchar(500)              default null comment '封面',
    description      text comment '简介',
    designer_id      bigint                    default null comment 'UACD设计器id',
    source_id        char(36)         not null default '' comment '来源id',
    source_name      varchar(200)     not null default '' comment '来源名称',
    source_reg_id    char(16)         not null default '' comment '来源的具体类型',
    model_id         char(36)         not null default '' comment '模型id',
    scene_id         char(36)         not null default '' comment '场景id',
    category_id      char(36)         not null default '' comment '分类id',
    auto_end         tinyint unsigned not null default '0' comment '是否自动结束(0-否, 1-是; 默认为0)',
    audit_enabled    tinyint unsigned not null default '0' comment '是否开启审核(0-未开启, 1-开启; 默认为0)',
    audit_status     tinyint unsigned not null default '0' comment '审核状态(0-待审核, 1-审核中, 2-已通过, 3-未通过, 4-已撤回; 默认为0)',
    create_user_id   char(36)         not null default '' comment '创建人id',
    update_user_id   char(36)         not null default '' comment '更新人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actv_ods (org_id, deleted, source_id),
    key idx_actv_cdo (category_id, deleted, org_id) using btree
) comment = '活动/项目基本信息表';

create table rv_activity_act
(
    id                 bigint unsigned  not null default '0' comment '主键id',
    org_id             char(36)         not null comment '机构id',
    actv_id            char(36)         not null default '' comment '活动/项目id',
    release_time       datetime(3)               default null comment '发布时间',
    first_release_time datetime(3)               default null comment '首次发布时间',
    release_user_id    char(36)         not null default '' comment '发布人 ID',
    real_end_time      datetime(3)               default null comment '实际结束时间',
    end_user_id        char(36)         not null default '' comment '结束的人 ID',
    archive_date       datetime(3)               default null comment '归档日期',
    archive_user_id    char(36)         not null default '' comment '归档人 ID',
    deleted            tinyint unsigned not null default '0' comment '是否删除',
    create_time        datetime(3)      not null comment '创建时间',
    create_user_id     char(36)         not null comment '创建人id',
    update_time        datetime(3)      not null comment '更新时间',
    update_user_id     char(36)         not null comment '更新人id',
    db_create_time     datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time     datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    primary key (id),
    key idx_actvact_oad (org_id, actv_id, deleted)
) comment = '活动/项目行为数据表';

create table rv_activity_arrange
(
    id               bigint unsigned  not null default '0' comment '主键id',
    org_id           char(36)         not null default '' comment '机构id',
    actv_reg_id      char(16)         not null default '' comment '活动/项目的具体类型(UACD注册表中定义)',
    actv_id          char(36)         not null default '' comment '活动/项目id',
    actv_type        tinyint unsigned          default '0' comment '类型(1-活动, 2-项目)',
    actv_name        varchar(200)     not null default '' comment '活动/项目名称（冗余）',
    designer_id      bigint unsigned  not null comment '设计器id',
    arrange_status   tinyint unsigned not null default '0' comment '生命周期:0-未发布（草稿）,1-已发布,2-撤回,3-结束,4-归档,5-删除',
    ext              json                      default null comment '业务扩展json',
    time_model       tinyint unsigned not null default '0' comment '时间模式(0-固定, 1-相对)',
    start_time       datetime(3)               default null comment '固定开始时间',
    end_time         datetime(3)               default null comment '固定截止时间',
    start_day_offset int                       default null comment '相对开始天数',
    end_day_offset   int                       default null comment '相对截止天数',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    create_user_id   char(36)         not null default '' comment '创建人id',
    update_user_id   char(36)         not null default '' comment '更新人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) comment '更新时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvarr_oad (org_id, actv_id, deleted)
) comment = '活动/项目编排表';

create table rv_activity_arrange_item
(
    id               bigint unsigned  not null default '0' comment '主键id',
    org_id           char(36)         not null default '' comment '机构id',
    actv_id          char(36)         not null default '' comment '活动/项目id',
    actv_org_id      bigint unsigned  not null comment '活动/项目OrganizationID',
    parent_id        bigint unsigned  not null default '0' comment '上一级ItemID(第一级为0)',
    ref_reg_id       char(16)                  default null comment '叶节点引用对象的具体类型(UACD注册表中定义)',
    ref_id           char(36)                  default null comment '叶节点引用对象的id',
    ref_name         varchar(255)              default '' comment '叶子节点引用对象的名称',
    item_name        varchar(255)              default null comment '业务自定义别名',
    item_type        tinyint unsigned not null default '0' comment '节点类型(0-叶节点(任务), 1-目录节点-章节/阶段, 2-目录节点-任务组)',
    type_name_key    varchar(100)              default '' comment '类型国际化key',
    actv_alias       varchar(100)              default null comment '活动任务类型自定义别名',
    node_level       tinyint unsigned not null comment '节点层级(从1开始)',
    description      text comment '节点描述',
    required         tinyint unsigned not null default '0' comment '是否必修(0-否, 1-是)',
    locked           tinyint unsigned not null default '0' comment '是否锁定:0-否,1-是',
    hidden           tinyint unsigned not null default '0' comment '是否隐藏:0-否,1-是',
    order_index      int unsigned     not null default '0' comment '排序',
    study_hours      int unsigned              default '0' comment '学习时长',
    ext              json                      default null comment '业务扩展json',
    time_model       tinyint                   default '0' comment '时间模式(0-固定, 1-相对)',
    start_time       datetime(3)               default null comment '固定开始时间',
    end_time         datetime(3)               default null comment '固定截止时间',
    start_day_offset int                       default null comment '相对开始天数',
    end_day_offset   int                       default null comment '相对截止天数',
    item_status      tinyint unsigned not null default '1' comment '数据状态(1-页面通过校验, 2-同步成功, 3-同步失败, 4-未同步; 默认为1)',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    create_user_id   char(36)         not null default '' comment '创建人id',
    update_user_id   char(36)         not null default '' comment '更新人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) comment '更新时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvarritem_oadp (org_id, actv_org_id, deleted, parent_id),
    key idx_actvarritem_aodr (actv_id, org_id, deleted, ref_id),
    key idx_actvarritem_paod (parent_id, actv_id, org_id, deleted)
) comment = '活动/项目编排item表';

create table rv_activity_config
(
    id             bigint unsigned  not null default '0' comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    actv_id        char(36)         not null default '' comment '活动/项目id',
    label_code     varchar(50)      not null default '' comment '类型code',
    label_value    json                      default null comment '配置信息',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    unique key uk_aomac_al (actv_id, label_code),
    key idx_aomac_oald (org_id, actv_id, label_code, deleted)
) comment = '活动/项目配置表';

create table rv_activity_draft
(
    id             bigint unsigned  not null default '0' comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    actv_id        char(36)         not null default '' comment '活动/项目id',
    ref_reg_id     char(16)         not null default '' comment '叶节点引用对象的具体类型(UACD注册表中定义)',
    ref_id         char(36)         not null default '' comment '叶节点引用对象的id',
    form_data      json                      default null comment '表单数据',
    item_id        bigint unsigned  not null default '0' comment 'UACD大纲节点id',
    draft_status   tinyint unsigned not null default '1' comment '草稿数据状态(1-当前草稿数据待转正, 2-当前草稿数据已转正)',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvdrft_oadi (org_id, actv_id, deleted, item_id),
    key idx_actvdrft_rod (ref_id, org_id, deleted)
) comment = '活动/SCO草稿表';

create table rv_activity_dynamic_group
(
    id               bigint unsigned  not null default '0' comment '主键',
    org_id           char(36)         not null comment '机构id',
    participation_id bigint           not null comment '参与id',
    actv_id          char(36)         not null default '' comment '活动/项目id',
    enabled          tinyint          not null default '1' comment '是否启用（0 不启用；1 启用；默认 1）',
    user_group_id    varchar(50)      not null default '' comment '动态用户组id',
    create_user_id   char(36)         not null comment '创建人id',
    update_user_id   char(36)         not null comment '更新人id',
    deleted          tinyint unsigned not null default '0' comment '是否删除 0未删除 1已删除',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '更新时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    primary key (id),
    key idx_actvdyngrp_oapd (org_id, actv_id, participation_id, deleted),
    key idx_actvdyngrp_uod (user_group_id, org_id, deleted)
) comment = '参与动态用户组配置';

create table rv_activity_group
(
    id               bigint           not null comment '主键id',
    org_id           char(36)         not null comment '机构id',
    actv_id          char(36)         not null comment '项目/活动id',
    participation_id bigint           not null comment '参与id',
    group_name       varchar(100)     not null comment '小组名称',
    group_icon       varchar(500)              default '' comment '小组图标',
    leader_user_id   char(36)                  default '' comment '组长id',
    open_im          tinyint unsigned          default '0' comment '是否开启群聊(0未开启,1已开启)',
    owner_user_id    char(36)                  default '' comment '群主id',
    im_id            varchar(50)               default '' comment '群聊id',
    im_name          varchar(255)              default '' comment '群聊名称',
    im_type_group    tinyint unsigned          default null comment '群聊类型 0-钉钉 1-飞书 2-企微 3-系统',
    ext              varchar(255)              default '' comment '开启小组群聊的时候回调的json参数值',
    deleted          tinyint unsigned not null default '0' comment '删除标志',
    create_user_id   char(36)         not null default '' comment '创建人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_user_id   char(36)         not null default '' comment '修改人id',
    update_time      datetime(3)      not null default current_timestamp(3) comment '修改时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    db_archived      tinyint unsigned not null default '0' comment '是否归档 0-未归档 1-已归档',
    primary key (id),
    key idx_actvgrp_oadp (org_id, actv_id, deleted, participation_id)
) comment = '小组表';

create table rv_activity_item_statistics
(
    id             bigint unsigned  not null default '0' comment '记录id',
    org_id         char(36)         not null comment '机构id',
    actv_id        char(36)         not null default '' comment '活动id',
    item_id        bigint unsigned  not null default '0' comment '叶节点id',
    item_type      tinyint unsigned not null default '0' comment '节点类型:0:任务,1:阶段',
    complete_count int unsigned              default '0' comment '学员完成数',
    total_count    int unsigned              default '0' comment '参与总学员数',
    deleted        tinyint unsigned not null default '0' comment '是否删除 0未删除 1已删除',
    create_time    datetime(3)      not null comment '创建时间',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_time    datetime(3)      not null comment '更细时间',
    update_user_id char(36)         not null default '' comment '更新人id',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    db_archived    tinyint          not null default '0' comment '0:未归档,1:已归档',
    primary key (id),
    key idx_actvitmstat_oid (org_id, item_id, deleted)
) comment = 'Item结果统计表';

create table rv_activity_member_statistics
(
    id                                bigint           not null comment '主键id',
    org_id                            char(36)         not null comment '机构id',
    user_id                           char(36)         not null comment '用户id',
    actv_id                           char(36)         not null default '0' comment '活动id',
    actv_completed_status             tinyint unsigned not null default '0' comment '活动完成状态:0-未完成,1-进行中,2-已完成',
    actv_completed_rate               decimal(6, 4)    not null default '0.0000' comment '活动完成进度',
    actv_completed_time               datetime(3)               default null comment '活动完成时间',
    period_completed_count            tinyint unsigned          default '0' comment '目录完成数',
    earned_point                      int              not null default '0' comment '学员已获得积分',
    earned_credit                     decimal(18, 2)   not null default '0.00' comment '学员已获得学分',
    first_study_time                  datetime(3)               default null comment '第一次学习时间',
    last_study_time                   datetime(3)               default null comment '最近学习时间',
    all_task_count                    int unsigned     not null default '0' comment '所有任务数量',
    all_task_completed_count          int unsigned     not null default '0' comment '所有任务完成数量',
    all_task_completed_rate           decimal(6, 4)    not null default '0.0000' comment '所有任务完成率',
    required_task_count               int unsigned     not null default '0' comment '必修任务数',
    required_task_completed_count     int unsigned     not null default '0' comment '必修任务完成数',
    required_task_completed_rate      decimal(6, 4)    not null default '0.0000' comment '必修任务完成率',
    ojt_task_count                    int unsigned              default '0' comment '带教任务数',
    ojt_task_completed_count          int unsigned              default '0' comment '带教任务完成数',
    ojt_required_task_count           int unsigned     not null default '0' comment '带教必修任务数',
    ojt_required_task_completed_count int unsigned     not null default '0' comment '带教必修任务完成数',
    ojt_required_task_completed_rate  decimal(6, 4)    not null default '0.0000' comment '带教必修任务完成率',
    last_required_task_completed_time datetime(3)               default null comment '最近一个必修任务完成时间点',
    last_all_task_completed_time      datetime(3)               default null comment '最近一个任务完成时间点',
    deleted                           tinyint unsigned not null default '0' comment '是否删除 0未删除 1已删除',
    create_time                       datetime(3)      not null comment '创建时间',
    create_user_id                    char(36)         not null comment '创建人id',
    update_time                       datetime(3)      not null comment '更新时间',
    update_user_id                    char(36)         not null comment '更新人id',
    db_create_time                    datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time                    datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    db_archived                       tinyint          not null default '0' comment '0:未归档,1:已归档',
    primary key (id),
    key idx_actvmebstat_oaud (org_id, actv_id, user_id, deleted)
) comment = '学员统计表';

create table rv_activity_objective
(
    id             bigint unsigned  not null default '0' comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    actv_id        char(36)         not null default '' comment '活动/项目id',
    index_model_id char(36)         not null default '' comment '指标模型id',
    dimension_id   char(36)         not null default '' comment '维度id',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvobj_oadd (org_id, actv_id, deleted, dimension_id)
) comment = '活动/项目Objective表';

create table rv_activity_objective_result
(
    id                    bigint unsigned  not null default '0' comment '记录id',
    org_id                char(36)         not null comment '机构id',
    actv_id               char(36)         not null default '' comment '活动/项目id',
    user_id               char(36)         not null comment '学员id',
    base_actv_result_id   bigint unsigned  not null default '0' comment '基础记录id',
    objective_id          varchar(50)      not null default '' comment '指标id',
    objective_mode_id     varchar(50)      not null default '' comment '指标模型id',
    objective_type        tinyint unsigned          default null comment '指标类型(1-知识点, 2-技能, 3-能力)',
    objective_score       decimal(18, 2)            default null comment '指标得分',
    objective_total_score decimal(18, 2)            default null comment '指标总分',
    objective_level       tinyint unsigned          default null comment '指标等级',
    objective_result      tinyint unsigned          default null comment '指标结果(1-优势, 2-待提升, 3-达标)',
    deleted               tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time           datetime(3)      not null comment '创建时间',
    create_user_id        char(36)         not null default '' comment '创建人id',
    update_time           datetime(3)      not null comment '更细时间',
    update_user_id        char(36)         not null default '' comment '更新人id',
    db_create_time        datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time        datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    db_archived           tinyint          not null default '0' comment '0:未归档,1:已归档',
    ext                   json                      default null comment '扩展字段（json格式)',
    primary key (id),
    key idx_actvobjrest_oaudb (org_id, actv_id, user_id, deleted, base_actv_result_id)
) comment = '活动指标结果表';

create table rv_activity_organization
(
    id               bigint unsigned  not null default '0' comment '主键id',
    org_id           char(36)         not null default '' comment '机构id',
    actv_id          char(36)         not null default '' comment '活动/项目id',
    arrange_id       bigint unsigned  not null default '0' comment '编排id',
    time_model       tinyint                   default '0' comment '时间模式(0-固定, 1-相对)',
    start_time       datetime(3)               default null comment '固定开始时间',
    end_time         datetime(3)               default null comment '固定截止时间',
    start_day_offset int                       default null comment '相对开始天数',
    end_day_offset   int                       default null comment '相对截止天数',
    leaf_type        tinyint unsigned not null default '0' comment '叶节点类型(0-Activity, 1-SCO)',
    has_folder       tinyint unsigned not null default '0' comment '有无阶段/章节(0-无, 1-有)',
    sn_id            bigint unsigned           default null comment '控制规则id',
    designed         tinyint unsigned not null default '0' comment '是否编排过:0-否,1-是',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    create_user_id   char(36)         not null default '' comment '创建人id',
    update_user_id   char(36)         not null default '' comment '更新人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) comment '更新时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvorg_oada (org_id, actv_id, deleted, arrange_id)
) comment = '活动/项目Organization表';

create table rv_activity_participation
(
    id                   bigint           not null comment '主键id',
    org_id               char(36)         not null comment '机构id',
    actv_id              char(36)         not null comment '项目/活动id',
    participation_method tinyint unsigned not null default '0' comment '参与方式 1-指派(包括手动加入和自动加入) 2-主动报名 3-指派、主动）',
    config_data          json                      default null comment '具体的报名id、动态用户组id',
    deleted              tinyint unsigned not null default '0' comment '删除标志',
    create_user_id       char(36)         not null default '' comment '创建人id',
    create_time          datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_user_id       char(36)         not null default '' comment '修改人id',
    update_time          datetime(3)      not null default current_timestamp(3) comment '修改时间',
    db_create_time       datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time       datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    db_archived          tinyint unsigned not null default '0' comment '是否归档 0-未归档 1-已归档',
    primary key (id),
    key idx_actvpart_oad (org_id, actv_id, deleted)
) comment = '参与表';

create table rv_activity_participation_member
(
    id               bigint           not null comment '主键id',
    org_id           char(36)         not null comment '机构id',
    actv_id          char(36)         not null comment '项目/活动id',
    participation_id bigint           not null comment '参与id',
    user_id          char(36)         not null comment '学员id',
    formal           tinyint unsigned not null default '1' comment '0-旁听学员 1-正式学员',
    join_method      tinyint unsigned not null default '0' comment '加入方式（1-手动加入 2-自动加入 3-通过报名加入）',
    group_id         bigint unsigned  not null default '0' comment '小组id',
    deleted          tinyint unsigned not null default '0' comment '删除标志',
    create_user_id   char(36)         not null default '' comment '创建人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_user_id   char(36)         not null default '' comment '修改人id',
    update_time      datetime(3)      not null default current_timestamp(3) comment '修改时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    db_archived      tinyint unsigned not null default '0' comment '是否归档 0-未归档 1-已归档',
    join_time        datetime(3)      not null default current_timestamp(3) comment '加入时间',
    effect_time      datetime(3)               default null comment '生效时间',
    start_time       datetime(3)               default null comment '学员活动开始时间',
    end_time         datetime(3)               default null comment '学员活动结束时间',
    delay_flag       tinyint unsigned not null default '0' comment '0:未被延期 1:被延期',
    primary key (id),
    key idx_actvpartmem_oaduf (org_id, actv_id, deleted, user_id, formal)
) comment = '活动/项目参与人员关系表';

create table rv_activity_participation_member_title
(
    id               bigint           not null comment '主键id',
    org_id           char(36)         not null comment '机构id',
    actv_id          char(36)         not null comment '项目/活动id',
    participation_id bigint           not null comment '参与id',
    title_id         bigint           not null comment '称号id',
    user_id          char(36)         not null comment '学员id',
    notify           tinyint unsigned          default '0' comment '是否消息通知学员 0否 1是',
    user_comment     varchar(250)              default '' comment '一句话点评',
    create_user_id   char(36)         not null default '' comment '创建人id',
    update_user_id   char(36)         not null default '' comment '更新人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time      datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id)
) comment = '称号人员关系表';

create table rv_activity_participation_relation
(
    id               bigint           not null comment '主键id',
    org_id           char(36)         not null comment '机构id',
    actv_id          char(36)         not null comment '项目/活动id',
    participation_id bigint           not null comment '参与id',
    user_id          char(36)         not null comment '用户id',
    relation_type    tinyint unsigned not null default '0' comment '类型 0-负责人 1-跟踪人 2-导师范围 3-培训计划负责人 4-预算负责人 5-辅导员',
    order_index      int unsigned     not null default '0' comment '排序号',
    target_id        bigint                    default null comment '辅导员所属小组id(rv_group主键id)',
    deleted          tinyint unsigned not null default '0' comment '删除标志',
    create_user_id   char(36)         not null default '' comment '创建人id',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_user_id   char(36)         not null default '' comment '修改人id',
    update_time      datetime(3)      not null default current_timestamp(3) comment '修改时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    db_archived      tinyint unsigned not null default '0' comment '是否归档 0-未归档 1-已归档',
    remind           tinyint unsigned          default '0' comment '消息是否提醒：1-已提醒 0-未提醒',
    primary key (id),
    key idx_actvpartrel_oapdu (org_id, actv_id, participation_id, deleted, user_id),
    key idx_actvpartrel_uodr (user_id, org_id, deleted, relation_type)
) comment = '参与干系人表';

create table rv_activity_perf
(
    id              char(36)         not null comment '主键id',
    aom_act_id      char(36)                  default null comment '项目活动ID, 指向rv_activity.id',
    model_id        char(36)                  default '' comment '模型id',
    org_id          char(36)         not null comment '机构id',
    actv_name       varchar(200)     not null comment '活动名称',
    period_ids      varchar(500)     not null comment '绩效周期ID, ;隔开',
    indicator_id    char(36)         not null comment '关联指标ID',
    eval_type       tinyint unsigned not null comment '评估方式(1-绩效等级, 2-绩效得分(分值))',
    eval_time_type  tinyint unsigned not null comment '1:动态评估,2:定时评估',
    eval_time       datetime(3)               default null comment '定时评估时使用,精确到小时',
    actv_desc       text comment '任务说明',
    score_qualified decimal(10, 2)            default null comment '达标得分',
    create_user_id  char(36)         not null default '' comment '创建人id',
    update_user_id  char(36)         not null default '' comment '更新人id',
    create_time     datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time     datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted         tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id),
    key idx_org_act (org_id, aom_act_id)
) comment = '活动模型-绩效评估活动表';

create table rv_activity_perf_conf
(
    id             char(36)         not null comment '主键id',
    actv_perf_id   char(36)         not null comment '绩效活动ID, rv_activity_perf.id',
    org_id         char(36)         not null comment '机构id',
    period_id      char(36)         not null comment '绩效周期ID',
    weight         decimal(6, 2)    not null comment '权重',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id),
    key idx_org_act (org_id, actv_perf_id)
) comment = '活动模型-绩效评估-绩效周期配置表';

create table rv_activity_perf_result
(
    id             char(36)         not null comment '主键',
    actv_perf_id   char(36)         not null comment '活动ID',
    org_id         char(36)         not null comment '机构id',
    user_id        char(36)         not null comment '用户id',
    result_conf_id char(36)                  default null comment '绩效结果, rv_activity_perf_result_conf.id',
    result_score   decimal(10, 2)            default null comment '绩效得分',
    qualified      tinyint unsigned not null default '0' comment '是否达标(0-不达标, 1-达标)',
    create_user_id char(36)         not null comment '创建人主键',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人主键',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id)
) row_format = dynamic comment = '活动模型-绩效评估结果表';

create table rv_activity_perf_result_conf
(
    id             char(36)         not null comment '主键id',
    actv_perf_id   char(36)         not null comment '活动ID,rv_activity_perf.id',
    org_id         char(36)         not null comment '机构id',
    result_name    varchar(200)     not null comment '结果等级',
    score          decimal(10, 2)   not null comment '得分',
    rule_score     decimal(10, 2)            default null comment '规则为绩效得分时，需要配置规则得分(废弃)',
    qualified      tinyint unsigned not null default '0' comment '是否达标(0-不达标, 1-达标)',
    rule_conf      text comment '规则JSON配置',
    rule_display   text comment '规则描述',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    order_num      int                       default '0' comment '排序',
    primary key (id),
    key idx_oid (org_id),
    key idx_org_act (org_id, actv_perf_id)
) comment = '活动模型-绩效评估配置表';

create table rv_activity_profile
(
    id                char(36)         not null comment '主键id',
    aom_actv_id       char(36)         not null comment '活动ID, rv_activity.id',
    model_id          char(36)         not null default '' comment '模型id',
    org_id            char(36)         not null comment '机构id',
    profile_name      varchar(200)     not null comment '任务名称',
    actv_desc         text comment '任务说明',
    eval_time_type    tinyint unsigned not null comment '1:动态评估,2:定时评估',
    eval_time         datetime(3)               default null comment '定时评估时使用,精确到小时',
    score_qualified   decimal(10, 2)            default null comment '达标得分',
    score_unqualified decimal(10, 2)            default null comment '未达标得分',
    create_user_id    char(36)         not null default '' comment '创建人id',
    update_user_id    char(36)         not null default '' comment '更新人id',
    create_time       datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time       datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted           tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time    datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time    datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id),
    key idx_org_act (org_id, aom_actv_id)
) comment = '活动模型-动态人才评估';

create table rv_activity_profile_indicator
(
    id              char(36)         not null comment '主键id',
    org_id          char(36)         not null comment '机构id',
    actv_profile_id char(36)         not null comment '活动ID, rv_activity_profile.id',
    sd_indicator_id char(36)         not null comment '关联人才标准的指标ID',
    rule_id         bigint           not null comment '指标规则ID',
    order_index     int                       default '0' comment '排序',
    create_user_id  char(36)         not null default '' comment '创建人id',
    update_user_id  char(36)         not null default '' comment '更新人id',
    create_time     datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time     datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted         tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id)
) comment = '活动模型-动态人才评估-指标表';

create table rv_activity_profile_result
(
    id              char(36)         not null comment '主键',
    actv_profile_id char(36)         not null comment '活动ID, rv_activity_profile.id',
    org_id          char(36)         not null comment '机构id',
    user_id         char(36)         not null comment '用户id',
    sd_indicator_id char(36)         not null comment '指标ID',
    qualified       tinyint unsigned not null default '0' comment '是否达标(0-不达标, 1-达标)',
    create_user_id  char(36)         not null comment '创建人主键',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人主键',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid_apid_uid (org_id, actv_profile_id, user_id)
) row_format = dynamic comment = '活动模型-动态人才评估-结果表';

create table rv_activity_profile_result_detail
(
    id              char(36)    not null comment '主键',
    org_id          char(36)    not null comment '机构id',
    actv_profile_id char(36)    not null comment '活动ID, rv_activity_profile.id',
    user_id         char(36)    not null comment '学员id',
    user_result_id  char(36)    not null comment '人才档案，学员指标结果ID，rv_activity_profile_result.id',
    condition_id    char(36)    not null comment '维度条件id,指向规则json中的某一个条件主键',
    label_type      tinyint     not null comment '标签类型:1-指标 2-标签',
    label_id        char(36)    not null comment '标签/指标id',
    label_value_id  varchar(1100)        default null comment '标签/指标值id,多个值之间使用半角逗号分隔',
    label_value     text comment '标签/指标值,多个值之间使用半角逗号分隔',
    qualified       tinyint     not null default '0' comment '是否达标:0-不达标 1-达标 2-异常不达标（异常,可能因维度在任职资格中被删除找不到了）',
    create_user_id  char(36)    not null comment '创建人主键',
    create_time     datetime(3) not null comment '创建时间',
    update_user_id  char(36)    not null comment '更新人主键',
    update_time     datetime(3) not null comment '更新时间',
    db_create_time  datetime(3) not null default current_timestamp(3) comment '数据创建时间（数据库专用,禁止用于业务）',
    db_update_time  datetime(3) not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间（数据库专用,禁止用于业务）',
    primary key (id),
    key idx_oid_apid_urid (org_id, actv_profile_id, user_result_id)
) comment = '活动模型-动态人才评估-结果明细表';

create table rv_activity_source
(
    id             bigint unsigned  not null comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    actv_id        char(36)         not null default '' comment '活动id',
    source_id      char(36)         not null default '' comment '来源id',
    source_name    varchar(200)     not null default '' comment '来源名称',
    source_reg_id  char(16)         not null default '' comment '来源的具体类型',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_actvsrc_ods (org_id, deleted, source_id),
    key idx_actvsrc_ado (actv_id, deleted, org_id)
) comment = '活动来源关系表';

create table rv_activity_title
(
    id             bigint           not null comment '主键id',
    org_id         char(36)         not null comment '机构id',
    title_name     varchar(200)     not null default '' comment '称号名称',
    outstanding    tinyint unsigned          default '0' comment '是否为优秀，0-否，1-是',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id)
) comment = '称号表';

create table rv_assessment_activity_result
(
    id                  bigint unsigned  not null default '0' comment '记录id',
    org_id              char(36)         not null comment '机构id',
    actv_id             char(36)         not null default '' comment '活动/项目id',
    user_id             char(36)         not null comment '学员id',
    base_actv_result_id bigint unsigned  not null default '0' comment '基础记录id',
    result_repeat_flag  tinyint          not null default '0' comment '任务重复类型 0:默认 1:多班次任务 2:指派重复学习',
    repeat_count        int              not null default '0' comment '指派重新学习次数',
    sub_task_result_id  bigint           not null default '0' comment '生效的子任务结果Id',
    passed              tinyint          not null default '-1' comment '活动是否通过 0否 1是',
    target_status       tinyint unsigned not null default '0' comment '活动批阅状态 0:未开始；1:考试中；2:已提交；3:批阅中；4:已完成',
    score               decimal(18, 2)            default null comment '活动得分',
    total_score         decimal(18, 2)            default null comment '活动总分',
    deleted             tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time         datetime(3)      not null comment '创建时间',
    create_user_id      char(36)         not null default '' comment '创建人id',
    update_time         datetime(3)      not null comment '更细时间',
    update_user_id      char(36)         not null default '' comment '更新人id',
    db_create_time      datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time      datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    db_archived         tinyint          not null default '0' comment '0:未归档,1:已归档',
    ext                 json                      default null comment '扩展信息',
    primary key (id),
    key idx_actvassrel_oaud (org_id, actv_id, user_id, deleted),
    key idx_actvassrel_bod (base_actv_result_id, org_id, deleted)
) comment = '评鉴活动结果表';

create table rv_base_activity_result
(
    id              bigint unsigned  not null default '0' comment '记录id',
    org_id          char(36)         not null comment '机构id',
    actv_id         char(36)         not null default '' comment '活动id',
    user_id         char(36)         not null comment '学员id',
    item_id         bigint unsigned  not null default '0' comment '叶节点id',
    ref_reg_id      char(16)                  default null comment '叶节点引用对象的具体类型(UACD注册表中定义)',
    required        tinyint unsigned not null default '0' comment '是否必修 0否 1是',
    result_status   tinyint unsigned not null default '0' comment '0未开始,1进行中,2已完成',
    start_time      datetime(3)               default null comment '开始学习时间',
    completed_time  datetime(3)               default null comment '完成时间',
    last_study_time datetime(3)               default null comment '最近学习时间',
    hand_completed  tinyint                   default '0' comment '是否手动标记完成0 否 1是',
    deleted         tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time     datetime(3)      not null comment '创建时间',
    create_user_id  char(36)         not null default '' comment '创建人id',
    update_time     datetime(3)      not null comment '更细时间',
    update_user_id  char(36)         not null default '' comment '更新人id',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    actv_type       tinyint unsigned not null comment '类型(1-活动, 2-项目)',
    sub_type        tinyint unsigned not null comment '子类型(1-Assessment, 2-Content, 3-Practice)',
    primary key (id),
    key idx_baseactvrel_oadui (org_id, actv_id, deleted, user_id, item_id),
    key idx_baseactvrel_idr (item_id, deleted, result_status)
) comment = '活动结果基础表';

create table rv_job_control
(
    id             bigint unsigned  not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    actv_id        char(36)         not null comment '活动/项目id',
    job_type       tinyint unsigned not null comment '任务类型(0-定时发布项目, 1-自动结束项目)',
    job_time       datetime         not null comment '定时时间',
    ext            json                      default null comment '任务扩展信息',
    executed       tinyint unsigned not null default '0' comment '是否已执行(0-否, 1-是; 默认为0)',
    enabled        tinyint unsigned not null default '0' comment '是否启用(0-否, 1-是; 默认为0)',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-否, 1-是; 默认为0)',
    create_time    datetime(3)      not null comment '创建时间',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_time    datetime(3)      not null comment '更细时间',
    update_user_id char(36)         not null default '' comment '更新人id',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_jobcontrol_jd (job_time, deleted),
    key idx_jobcontrol_oad (org_id, actv_id, deleted),
    key idx_jobcontrol_jeej (job_type, executed, enabled, job_time)
) comment = '定时任务控制表';

create table rv_sub_activity_objective_result
(
    id                    bigint unsigned  not null default '0' comment '记录id',
    org_id                char(36)         not null comment '机构id',
    actv_id               char(36)         not null default '' comment '活动/项目id',
    user_id               char(36)         not null comment '学员id',
    base_actv_result_id   bigint unsigned  not null default '0' comment '基础记录id',
    objective_id          varchar(50)      not null comment '指标id',
    objective_mode_id     varchar(50)      not null comment '指标模型id',
    objective_type        tinyint unsigned          default null comment '指标类型(1-知识点, 2-技能, 3-能力)',
    objective_score       decimal(18, 2)            default null comment '指标得分',
    objective_total_score decimal(18, 2)            default null comment '指标总分',
    objective_level       tinyint unsigned          default null comment '指标等级',
    objective_result      tinyint unsigned          default null comment '指标结果(1-优势, 2-待提升, 3-达标)',
    deleted               tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time           datetime(3)      not null comment '创建时间',
    create_user_id        char(36)         not null default '' comment '创建人id',
    update_time           datetime(3)      not null comment '更细时间',
    update_user_id        char(36)         not null default '' comment '更新人id',
    db_create_time        datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time        datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    db_archived           tinyint          not null default '0' comment '0:未归档,1:已归档',
    primary key (id),
    key idx_subactvobjrest_oaudb (org_id, actv_id, user_id, deleted, base_actv_result_id)
) comment = '子活动指标结果表';

create table rv_sub_assessment_activity_result
(
    id                  bigint unsigned  not null default '0' comment '记录id',
    org_id              char(36)         not null comment '机构id',
    actv_id             char(36)         not null default '' comment '活动/项目id',
    user_id             char(36)         not null comment '学员id',
    base_actv_result_id bigint unsigned  not null default '0' comment '基础记录id',
    result_repeat_flag  tinyint          not null default '0' comment '任务重复类型 0:默认 1:多班次任务 2:指派重复学习',
    repeat_count        int              not null default '0' comment '指派重新学习次数',
    sub_task_result_id  bigint           not null default '0' comment '生效的子任务结果Id',
    passed              tinyint          not null default '-1' comment '活动是否通过 0否 1是',
    target_status       tinyint unsigned not null default '0' comment '活动批阅状态 0:未开始；1:考试中；2:已提交；3:批阅中；4:已完成',
    score               decimal(18, 2)            default null comment '活动得分',
    total_score         decimal(18, 2)            default null comment '活动总分',
    order_index         tinyint          not null default '0' comment '学习结果排序',
    deleted             tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time         datetime(3)      not null comment '创建时间',
    create_user_id      char(36)         not null default '' comment '创建人id',
    update_time         datetime(3)      not null comment '更细时间',
    update_user_id      char(36)         not null default '' comment '更新人id',
    db_create_time      datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time      datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    db_archived         tinyint          not null default '0' comment '0:未归档,1:已归档',
    primary key (id),
    key idx_subactvassrel_oaud (org_id, actv_id, user_id, deleted),
    key idx_subactvassrel_bod (base_actv_result_id, org_id, deleted)
) comment = '子评鉴活动结果表';

create table rv_sub_base_activity_result
(
    id              bigint unsigned  not null default '0' comment '记录id',
    org_id          char(36)         not null comment '机构id',
    actv_id         char(36)         not null default '' comment '活动id',
    user_id         char(36)         not null comment '学员id',
    item_id         bigint unsigned  not null default '0' comment '叶节点id',
    ref_reg_id      char(16)                  default null comment '叶节点引用对象的具体类型(UACD注册表中定义)',
    required        tinyint unsigned not null default '0' comment '是否必修 0否 1是',
    result_status   tinyint unsigned not null default '0' comment '0未开始,1进行中,2已完成',
    start_time      datetime(3)               default null comment '开始学习时间',
    completed_time  datetime(3)               default null comment '完成时间',
    last_study_time datetime(3)               default null comment '最近学习时间',
    hand_completed  tinyint                   default '0' comment '是否手动标记完成0 否 1是',
    order_index     tinyint          not null default '0' comment '学习结果排序',
    deleted         tinyint unsigned not null default '0' comment '是否删除 0未删 1已删',
    create_time     datetime(3)      not null comment '创建时间',
    create_user_id  char(36)         not null default '' comment '创建人id',
    update_time     datetime(3)      not null comment '更细时间',
    update_user_id  char(36)         not null default '' comment '更新人id',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_subbaseactvrel_oadui (org_id, actv_id, deleted, user_id, item_id),
    key idx_subbaseactvrel_idr (item_id, deleted, result_status)
) comment = '子活动结果基础表';

create table rv_tree
(
    id             bigint unsigned  not null comment '主键',
    org_id         char(36)         not null comment '机构ID',
    tree_id        varchar(100)     not null comment '树ID',
    tree_name      varchar(100)     not null comment '树名称',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0:否;1:是)',
    removed        bigint unsigned  not null default '0' comment '是否删除(0:否;等于主键时代表删除)',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null default '' comment '创建人',
    update_time    datetime(3)      not null default current_timestamp(3) comment '修改时间',
    update_user_id char(36)         not null default '' comment '修改人',
    db_archived    tinyint unsigned not null default '0' comment '是否已归档(0:否;1:是;)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间 (数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间 (数据库专用，禁止用于业务)',
    config         varchar(2000)    not null default '' comment '树配置',
    primary key (id),
    unique key uk_tree_id (tree_id),
    unique key uk_tree_name (tree_name, org_id, removed),
    key idx_tree_id_org_id (tree_id, org_id)
) comment = '树';

create table rv_tree_action_permission
(
    id             bigint unsigned  not null comment '主键',
    org_id         char(36)         not null comment '机构ID',
    tree_id        varchar(36)      not null comment '树ID',
    action_code    varchar(30)      not null comment '动作编码',
    perm_code      varchar(100)     not null comment '权限编码',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0:否;1:是)',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null default '' comment '创建人',
    update_time    datetime(3)      not null default current_timestamp(3) comment '修改时间',
    update_user_id char(36)         not null default '' comment '修改人',
    db_archived    tinyint unsigned not null default '0' comment '是否已归档(0:否;1:是;)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间 (数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间 (数据库专用，禁止用于业务)',
    primary key (id),
    key idx_tree_id_org_id (tree_id, org_id)
) comment = '树操作权限配置';

create table rv_tree_node
(
    id               bigint unsigned  not null comment '主键',
    org_id           char(36)         not null comment '机构ID',
    tree_id          varchar(36)      not null comment '树ID',
    node_id          bigint unsigned  not null comment '节点ID(废弃)',
    pnode_id         bigint unsigned  not null comment '父节点ID(废弃)',
    nid              char(36)         not null comment '节点ID',
    pnid             char(36)         not null comment '父节点ID',
    node_name        varchar(200)     not null comment '节点名称',
    node_description varchar(400)     not null comment '节点描述',
    has_child        tinyint unsigned not null default '0' comment '是否有子节点(0:否;1:是)',
    hidden           tinyint unsigned not null default '0' comment '是否隐藏(0:否;1:是)',
    depth            int unsigned     not null comment '节点深度',
    sequence         bigint unsigned  not null comment '序号',
    deleted          tinyint unsigned not null default '0' comment '是否删除(0:否;1:是)',
    create_time      datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id   char(36)         not null default '' comment '创建人',
    update_time      datetime(3)      not null default current_timestamp(3) comment '修改时间',
    update_user_id   char(36)         not null default '' comment '修改人',
    db_archived      tinyint unsigned not null default '0' comment '是否已归档(0:否;1:是;)',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间 (数据库专用，禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间 (数据库专用，禁止用于业务)',
    primary key (id),
    unique key uk_nid (org_id, tree_id, nid),
    key idx_nid_org_id (tree_id, org_id, deleted, nid),
    key idx_pnid_org_id (pnid, tree_id, org_id, sequence)
) comment = '树节点';

create table rv_tree_node_relation
(
    id             bigint unsigned  not null comment '主键',
    org_id         char(36)         not null comment '机构ID',
    tree_id        varchar(36)      not null comment '树ID',
    ancestor_id    bigint unsigned  not null comment '祖先节点ID(废弃)',
    descendant_id  bigint unsigned  not null comment '后代节点ID(废弃)',
    ancr_id        char(36)         not null comment '祖先节点ID',
    descnt_id      char(36)         not null comment '后代节点ID',
    depth          int unsigned     not null comment '节点深度',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0:否;1:是)',
    removed        bigint unsigned  not null default '0' comment '是否删除(0:否;等于主键时代表删除)',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null default '' comment '创建人',
    update_time    datetime(3)      not null default current_timestamp(3) comment '修改时间',
    update_user_id char(36)         not null default '' comment '修改人',
    db_archived    tinyint unsigned not null default '0' comment '是否已归档(0:否;1:是;)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间 (数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间 (数据库专用，禁止用于业务)',
    primary key (id),
    unique key uk_node_rel_org_id (org_id, tree_id, removed, ancr_id, descnt_id),
    key idx_node_org_id_tree_id (org_id, tree_id, deleted),
    key idx_node_ancr_id (ancr_id, tree_id, org_id, deleted, descnt_id),
    key idx_node_descnt_id (descnt_id, tree_id, org_id, deleted, ancr_id)
) comment = '树节点关系表(闭包表)';

create table rv_xpd
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null comment '机构id',
    aom_prj_id     char(36)         not null comment '项目ID, 指向rv_activity.id',
    model_id       char(36)         not null default '' comment '模型id',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id)
) comment = '盘点项目表';

create table rv_xpd_action_plan
(
    id             char(36)    not null comment '主键',
    org_id         char(36)    not null comment '机构id',
    xpd_id         char(36)    not null comment '新盘点项目id',
    target_id      char(36)    not null comment '关联的外部行动计划id',
    target_type    tinyint unsigned     default '0' comment '激动计划类型：0-培训项目, 1-人才池',
    deleted        tinyint unsigned     default '0' comment '是否删除(0-否,1-是)',
    create_user_id char(36)    not null default '' comment '创建人id',
    create_time    datetime(3) not null default current_timestamp(3) comment '创建时间',
    update_user_id char(36)    not null default '' comment '修改人id',
    update_time    datetime(3) not null default current_timestamp(3) comment '修改时间',
    db_create_time datetime(3) not null default current_timestamp(3) comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time datetime(3) not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    primary key (id),
    key idx_oid_tid (org_id, xpd_id)
) comment = '行动计划表';

create table rv_xpd_calc_log
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    calc_type      tinyint          not null comment '计算类型',
    ref_id         varchar(36)      not null default '' comment '计算关联id',
    batch_no       int              not null default '0' comment '执行计算批次号',
    start_time     datetime(3)               default null comment '开始时间',
    end_time       datetime(3)               default null comment '结束时间',
    calc_status    tinyint          not null default '0' comment '0:开始，1：完成，2：失败',
    deleted        tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    primary key (id),
    key idx_oid_xid_uid (calc_type, ref_id, batch_no)
) comment = '执行计算日志';

create table rv_xpd_dim
(
    id             char(36)         not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    xpd_id         char(36)         not null comment '新盘点项目id',
    sd_dim_id      char(36)         not null comment '冗余人才标准的维度id',
    dim_type       tinyint unsigned not null default '0' comment '维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效',
    deleted        tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人主键',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人主键',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    score_total    decimal(8, 2)             default null comment '根据维度所设规则计算出来的总分',
    primary key (id),
    key idx_xpd_oid (org_id, xpd_id, deleted)
) comment = '盘点项目维度表';

create table rv_xpd_dim_comb
(
    id             char(36)         not null comment 'id',
    org_id         char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id         char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    comb_name      varchar(200)              default null comment '组合名称',
    comb_name_i18n varchar(200)              default null comment '组合名称国际化',
    x_sd_dim_id    char(36)         not null comment 'x轴维度id(人才标准侧)',
    y_sd_dim_id    char(36)         not null comment 'y轴维度id(人才标准侧)',
    comb_type      tinyint unsigned not null default '0' comment '类型:0-内置,1-自建',
    comb_desc      text comment '描述',
    deleted        tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null comment '创建人ID',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id char(36)         not null comment '更新人ID',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    primary key (id),
    key idx_oid (org_id, deleted)
) comment = '维度组合';

create table rv_xpd_dim_rule
(
    id              char(36)         not null comment '主键',
    org_id          char(36)         not null comment '机构id',
    xpd_id          char(36)         not null comment '新盘点项目id',
    parent_id       char(36)         not null default '0' comment '上级维度规则id, 指向rv_xpd_dim_rule.id',
    sd_dim_id       char(36)         not null comment '冗余人才标准的维度id',
    calc_type       tinyint unsigned not null default '0' comment '计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算',
    weight          decimal(6, 2)             default null comment '权重, 当计算方式为<按子维度结果计算>有效',
    calc_rule       tinyint                   default null comment '计算规则:0-快捷配置 1-高级公式',
    formula         text comment '计算规则表达式,当计算规则为<高级公式>时有效',
    formula_display text comment '可视化的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效',
    result_type     tinyint                   default null comment '结果类型:0-分值 1-达标率,非<绩效维度>下有效',
    aom_act_id      char(36)                  default null comment '活动模型-活动ID,当维度为绩效维度时有效,表示绩效维度关联的绩效活动,指向rv_activity.id',
    level_type      tinyint unsigned not null default '0' comment '分层方式:0-按比例 1-按固定值',
    level_priority  tinyint unsigned not null default '1' comment '分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效',
    level_rule      text comment '分层规则,json数组, 涉及到的维度分层规则id取自rv_xpd_grid_level.id',
    rule_desc       text comment '规则说明',
    enabled         tinyint unsigned not null default '0' comment '是否启用:0-未启用,1-启用',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人主键',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人主键',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_xpd_oid (org_id, xpd_id, sd_dim_id, deleted)
) comment = '盘点维度规则表';

create table rv_xpd_dim_rule_calc
(
    id              char(36)         not null comment '主键',
    org_id          char(36)         not null comment '机构id',
    xpd_id          char(36)         not null comment '新盘点项目id',
    sd_dim_id       char(36)         not null comment '冗余人才标准的维度id',
    dim_rule_id     char(36)         not null comment '维度规则ID(指向rv_xpd_rule_dim.id)',
    sd_indicator_id char(36)         not null comment '对应人才标准里的模型指标关系表id',
    ref_ids         varchar(800)              default null comment '数据来源:活动ID/导入活动ID/个人档案(id=0), json对象',
    calc_method     tinyint unsigned not null default '0' comment '计算逻辑:0-无 1-求平均 2-求和 3-全部来源中达标 4-任一来源中达标',
    weight          decimal(6, 2)    not null default '0.00' comment '权重',
    order_index     int              not null default '0' comment '排序',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    primary key (id),
    key idx_xpd_oid_drid (org_id, xpd_id, dim_rule_id, deleted)
) comment = '盘点维度计算规则表';

create table rv_xpd_grid
(
    id             char(36)         not null comment 'id',
    org_id         char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id         char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    template       tinyint unsigned not null default '0' comment '来源:0-模板,1-项目级',
    grid_name      varchar(200)     not null comment '宫格名称',
    grid_name_i18n varchar(200)              default null comment '宫格名称国际化code',
    grid_type      tinyint unsigned not null default '0' comment '宫格类型,0-四宫格,1-九宫格,2-十六宫格',
    config_type    tinyint unsigned not null default '0' comment '配置方式,0-统一配置,1-按维度组合配置',
    source_type    tinyint unsigned not null default '0' comment '来源,0-内置,1-自建',
    grid_state     tinyint unsigned not null default '0' comment '状态,0:未发布 1:已发布',
    grid_desc      text comment '宫格描述',
    deleted        tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null comment '创建人ID',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id char(36)         not null comment '更新人ID',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    source_grid_id char(36)                  default null comment '源宫格ID，宫格为<项目级>时有效，表示此宫格从哪个宫格模板复制而来',
    primary key (id),
    key idx_oid_xid (org_id, xpd_id, deleted)
) comment = '盘点宫格设置表';

create table rv_xpd_grid_cell
(
    id             char(36)         not null comment 'id',
    org_id         char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id         char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    grid_id        char(36)         not null comment '宫格ID, 指向rv_xpd_grid.id；',
    dim_comb_id    char(36)                  default null comment '维度组合id, 指向rv_xpd_dim_comb.id，如果宫格为统一配置，则存储为空字符串，代表所有宫格共享同一份格子配置',
    cell_name      varchar(200)     not null comment '格子名称',
    cell_name_i18n varchar(200)              default null comment '格子名称国际化code',
    cell_color     varchar(200)              default null comment '格子颜色',
    cell_index     int unsigned              default null comment '格子编号',
    deleted        tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null comment '创建人ID',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id char(36)         not null comment '更新人ID',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    x_index        tinyint unsigned          default null comment 'x坐标, 不同的宫格类型对应不同的x轴坐标，与rv_xpd_grid_level.order_index对应',
    y_index        tinyint unsigned          default null comment 'y坐标, 不同的宫格类型对应不同的x轴坐标，与rv_xpd_grid_level.order_index对应',
    primary key (id),
    key idx_oid_xid_gid (org_id, xpd_id, grid_id, deleted)
) comment = '盘点宫格-格子配置表';

create table rv_xpd_grid_dim_comb
(
    id             char(36)         not null comment 'id',
    org_id         char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id         char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    grid_id        char(36)         not null comment '宫格ID, 指向rv_xpd_grid.id',
    dim_comb_id    char(36)         not null comment '维度组合id, 指向rv_xpd_dim_comb.id,统一配置情况下维度组合为空字符串',
    show_type      tinyint unsigned not null default '0' comment '是否默认显示,0-否,1-是',
    deleted        tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    order_index    int unsigned     not null default '0' comment '排序',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id char(36)         not null comment '创建人ID',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id char(36)         not null comment '更新人ID',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    primary key (id),
    key idx_oid_gid (org_id, grid_id, deleted)
) comment = '盘点宫格-维度组关联关系表';

create table rv_xpd_grid_level
(
    id              char(36)         not null comment 'id',
    org_id          char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id          char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    grid_id         char(36)         not null comment '宫格ID, 指向rv_xpd_grid.id',
    level_name      varchar(200)     not null comment '分级名称',
    level_name_i18n varchar(200)              default null comment '分级名称国际化',
    order_index     int unsigned              default null comment '排序',
    deleted         tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time     datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id  char(36)         not null comment '创建人ID',
    update_time     datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id  char(36)         not null comment '更新人ID',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    primary key (id),
    key idx_oid_gid (org_id, grid_id, deleted)
) comment = '盘点宫格分级标准';

create table rv_xpd_grid_ratio
(
    id              char(36)         not null comment 'id',
    org_id          char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id          char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    grid_id         char(36)         not null comment '宫格ID, 指向rv_xpd_grid.id；',
    dim_comb_id     char(36)         not null comment '维度组合id,指向rv_xpd_dim_comb.id，如果宫格为统一配置，则存储为空字符串，代表所有宫格共享同一份落位占比',
    grid_cell_ids   varchar(2000)    not null comment '宫格中格子配置表的id集合,半角分号拼接,指向rv_xpd_grid_cell.id',
    grid_cell_index varchar(200)     not null comment '宫格中格子的编号集合,半角分号拼接,指向rv_xpd_grid_cell.cell_index',
    ratio           decimal(6, 2)    not null default '-1.00' comment '人员占比',
    order_index     int unsigned              default null comment '排序',
    deleted         tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time     datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id  char(36)         not null comment '创建人ID',
    update_time     datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id  char(36)         not null comment '更新人ID',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    primary key (id),
    key idx_oid_xid_gid (org_id, xpd_id, grid_id, deleted)
) comment = '盘点宫格落位比例';

create table rv_xpd_import
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null comment '机构id',
    xpd_id         char(36)         not null comment '盘点项目表id, rv_xpd.id',
    sd_dim_id      char(36)         not null comment '人才标准侧的维度ID',
    import_type    tinyint unsigned not null default '0' comment '导入类型(0-导入维度指标明细, 1-导入维度分层结果)',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    score_total    decimal(8, 2)    not null default '0.00' comment '指标总分',
    primary key (id)
) comment = '导入维度结果表';

create table rv_xpd_import_dim_user
(
    id             char(36)         not null comment '主键id',
    import_id      char(36)         not null comment '导入记录ID，rv_xpd_import.id',
    xpd_id         char(36)         not null comment '盘点项目表id',
    org_id         char(36)         not null comment '机构id',
    user_id        char(36)         not null comment '用户ID',
    sd_dim_id      char(36)         not null comment '人才标准侧的维度ID',
    grid_level_id  char(36)         not null comment '维度等级id, 取自rv_xpd_grid_level.id',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid_xpd_ipid (org_id, xpd_id, import_id, user_id, deleted)
) comment = '导入用户维度明细表';

create table rv_xpd_import_indicator_user
(
    id                 char(36)         not null comment '主键id',
    import_id          char(36)         not null comment '导入记录ID，rv_xpd_import.id',
    xpd_id             char(36)         not null comment '盘点项目表id',
    org_id             char(36)         not null comment '机构id',
    user_id            char(36)         not null comment '用户ID',
    score_total        decimal(8, 2)    not null default '0.00' comment '总分',
    sd_indicator_id    char(36)         not null comment '指标ID',
    sd_indicator_score decimal(8, 2)    not null default '0.00' comment '指标得分',
    qualified          tinyint unsigned not null default '0' comment '是否达标(0-不达标, 1-达标)',
    create_user_id     char(36)         not null default '' comment '创建人id',
    update_user_id     char(36)         not null default '' comment '更新人id',
    create_time        datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time        datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted            tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time     datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time     datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid_xpd_ipid (org_id, xpd_id, import_id, user_id, deleted)
) comment = '导入结果-导入用户指标明细表';

create table rv_xpd_import_log
(
    id             char(36)         not null comment '主键id',
    xpd_id         char(36)         not null comment '盘点项目表id',
    org_id         char(36)         not null comment '机构id',
    import_id      char(36)         not null comment '导入数据记录id，rv_xpd_import.id',
    import_time    datetime(3)      not null comment '导入时间',
    import_file    varchar(500)     not null comment '导入文件url或者fileId',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id)
) comment = '导入维度结果记录表';

create table rv_xpd_level
(
    id              char(36)         not null comment 'id',
    org_id          char(36)         not null comment '机构ID, 全局模板存********-0000-0000-0000-********0000',
    xpd_id          char(36)         not null comment '项目ID, 机构模板存********-0000-0000-0000-********0000',
    xpd_rule_id     char(36)         not null comment '项目规则ID,机构模板存********-0000-0000-0000-********0000。当xpd_id有具体值时有效(指向rv_xpd_rule.id,指的是盘点项目中设置的分层规则)',
    grid_id         char(36)         not null comment '宫格ID, 指向rv_xpd_grid.id',
    level_name      varchar(200)     not null comment '人才层级名称',
    level_name_i18n varchar(200)              default null comment '人才层级名称国际化code',
    level_value     decimal(8, 2)             default '0.00' comment '分层值:比例或固定值。xpd_id和xpd_rule_id都有具体值时有效。结果类型为<得分>时表示分数,结果类型为<达标率>时表示达标率',
    competent       tinyint                   default null comment '是否胜任:0-不胜任 1-胜任, 宫格模板设置时不设置，只在规则设置时设置',
    icon            varchar(500)              default null comment '层级icon地址',
    formula         text comment '计算规则表达式',
    formula_display text comment '可视化的计算规则表达式,用于页面渲染',
    order_index     int unsigned     not null default '0' comment '排序',
    deleted         tinyint unsigned not null default '0' comment '0:未删除 1:已删除',
    create_time     datetime(3)      not null default current_timestamp(3) comment '创建时间',
    create_user_id  char(36)         not null comment '创建人ID',
    update_time     datetime(3)      not null default current_timestamp(3) comment '更新时间',
    update_user_id  char(36)         not null comment '更新人ID',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据更新时间',
    primary key (id),
    key idx_oid (org_id, deleted)
) comment = '盘点人才层级';

create table rv_xpd_result_indicator
(
    id              char(36)         not null comment '主键id',
    org_id          char(36)         not null default '' comment '机构id',
    xpd_id          char(36)         not null comment '盘点项目id',
    sd_indicator_id char(36)         not null default '' comment '指标id',
    score_total     decimal(8, 2)    not null default '0.00' comment '总分',
    score_standard  decimal(8, 2)    comment '标准分',
    score_avg       decimal(8, 2)    not null default '0.00' comment '均分',
    qualified_ptg   decimal(6, 2)    not null default '0.00' comment '达标率',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    primary key (id),
    key idx_oid_xid_sid (org_id, xpd_id, sd_indicator_id)
) comment = '盘点指标结果';

create table rv_xpd_result_user
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    xpd_id         char(36)         not null comment '盘点项目id',
    user_id        char(36)         not null default '' comment '用户id',
    xpd_level_id   char(36)         not null default '' comment '分层id, rv_xpd_level.id',
    competent      tinyint unsigned not null default '0' comment '是否胜任:0-未胜任,1-胜任',
    deleted        tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    calc_batch_no  int              not null default '0' comment '执行计算批次号',
    score_value    decimal(8, 2)             default null comment '项目结果-分值',
    qualified_ptg  decimal(6, 2)             default null comment '项目结果-达标率',
    primary key (id),
    key idx_oid_xid_uid (org_id, xpd_id, user_id)
) comment = '盘点用户结果';

create table rv_xpd_result_user_dim
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    xpd_id         char(36)         not null comment '盘点项目id',
    user_id        char(36)         not null default '' comment '用户id',
    sd_dim_id      char(36)         not null comment '冗余的人才标准的维度id',
    grid_level_id  char(36)         not null default '' comment '宫格分层id, rv_xpd_grid_level.id',
    score_value    decimal(8, 2)    not null default '0.00' comment '分值',
    qualified_ptg  decimal(6, 2)    not null default '0.00' comment '达标率',
    deleted        tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    calc_batch_no  int              not null default '0' comment '执行计算批次号',
    perf_result_id char(36)                  default null comment '冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效',
    primary key (id),
    key idx_oid_xid_sdid (org_id, xpd_id, sd_dim_id)
) comment = '盘点用户维度结果';

create table rv_xpd_result_user_indicator
(
    id              char(36)         not null comment '主键id',
    org_id          char(36)         not null default '' comment '机构id',
    xpd_id          char(36)         not null comment '盘点项目id',
    sd_indicator_id char(36)         not null default '' comment '指标id',
    user_id         char(36)         not null default '' comment '用户id',
    score_value     decimal(8, 2)    not null default '0.00' comment '分值',
    result_detail   text comment '结果明细，json',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    calc_batch_no   int              not null default '0' comment '执行计算批次号',
    qualified       tinyint          not null default '0' comment '是否达标',
    perf_summary    varchar(800)              default null comment '冗余的绩效活动周期明细，格式：周期1：A;
周期2：C',
    perf_result_id  char(36)                  default null comment '冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效',
    result_dim_id   char(36)                  default null comment '维度结果id, rv_xpd_result_user_dim.id',
    primary key (id),
    key idx_oid_xid_sid_uid (org_id, xpd_id, sd_indicator_id, user_id)
) comment = '盘点用户指标结果';

create table rv_xpd_rule
(
    id              char(36)         not null comment '主键',
    org_id          char(36)         not null comment '机构id',
    xpd_id          char(36)         not null comment '新盘点项目id',
    calc_type       tinyint unsigned not null default '0' comment '计算方式:0-按维度结果计算 1-按指标结果计算',
    result_type     tinyint unsigned not null default '0' comment '结果类型:0-维度分层结果 1-(维度/指标)得分 2-(维度/指标)达标率',
    calc_rule       tinyint unsigned not null default '0' comment '计算规则:0-快捷配置 1-高级公式,当计算方式为<按指标结果计算>且结果类型为<指标得分>时有效',
    formula         text comment '计算规则表达式,当计算规则为<高级公式>时有效',
    formula_display text comment '可视化的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效',
    level_type      tinyint unsigned not null default '0' comment '分层方式:0-按比例 1-按固定值',
    rule_desc       text comment '规则说明',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人主键',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人主键',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    level_priority  tinyint unsigned not null default '1' comment '分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效',
    primary key (id),
    key idx_xpd_oid (org_id, xpd_id, deleted)
) comment = '盘点项目规则表';

create table rv_xpd_rule_calc_dim
(
    id             char(36)         not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    xpd_id         char(36)         not null comment '新盘点项目id',
    xpd_rule_id    char(36)         not null comment '项目规则ID(指向rv_xpd_rule.id)',
    sd_dim_id      char(36)         not null comment '冗余人才标准的维度id',
    weight         decimal(6, 2)    not null default '0.00' comment '权重',
    deleted        tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    order_index    int unsigned     not null default '0' comment '排序',
    primary key (id),
    key idx_xpd_oid (org_id, xpd_id, deleted)
) comment = '盘点项目计算规则(按维度结果计算,且结果类型是维度得分时)';

create table rv_xpd_rule_calc_indicator
(
    id              char(36)         not null comment '主键',
    org_id          char(36)         not null comment '机构id',
    xpd_id          char(36)         not null comment '新盘点项目id',
    xpd_rule_id     char(36)         not null comment '项目规则ID(指向rv_xpd_rule.id)',
    sd_indicator_id char(36)         not null comment '对应人才标准里的模型指标关系表id',
    ref_ids         varchar(800)              default null comment '数据来源:评鉴活动ID/导入活动ID/个人档案(id=0),json对象',
    calc_method     tinyint unsigned not null default '0' comment '计算逻辑:0-无 1-求平均 2-求和 3-全部来源中达标 4-任一来源中达标',
    weight          decimal(6, 2)             default null comment '权重',
    order_index     int unsigned     not null default '0' comment '排序',
    deleted         tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id  char(36)         not null comment '创建人',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time  datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    primary key (id),
    key idx_xpd_oid_xrid (org_id, xpd_id, xpd_rule_id, deleted)
) comment = '盘点项目计算规则(按指标结果计算时)';

create table rv_xpd_rule_conf
(
    id               char(36)         not null comment '主键',
    org_id           char(36)         not null comment '机构id',
    xpd_id           char(36)         not null comment '新盘点项目id',
    grid_id          char(36)         not null comment '宫格模板id项目级的宫格id, 从template_grid_id复制而来，指向rv_xpd_grid.id',
    result_type      tinyint unsigned not null default '0' comment '盘点结果类型:0-分值 1-达标率',
    snap             text comment '快速生成规则时选择的配置json快照',
    deleted          tinyint unsigned not null default '0' comment '是否删除:0-未删除,1-已删除',
    create_user_id   char(36)         not null comment '创建人主键',
    create_time      datetime(3)      not null comment '创建时间',
    update_user_id   char(36)         not null comment '更新人主键',
    update_time      datetime(3)      not null comment '更新时间',
    db_create_time   datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time   datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    version          int              not null default '0' comment '规则配置的版本号，主要用于乐观锁校验，防止同时修改规则配置导致的维度规则数据一致性问题',
    template_grid_id char(36)         not null comment '宫格模板id, 最初选的宫格模板, 指向rv_xpd_grid.id',
    primary key (id)
) comment = '盘点项目规则表';

create table rv_xpd_scene
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null comment '机构id',
    scene_name     varchar(500)     not null comment '场景名称',
    order_num      int              not null default '0' comment '排序',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid (org_id)
) comment = '盘点项目场景';

create table rv_xpd_user_ext
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null comment '机构id',
    xpd_id         char(36)         not null comment '项目id',
    user_id        char(36)         not null comment '人员id',
    actv_member_id bigint           not null comment '指向活动人员表，rv_activity_participation_member.id',
    suggestion     varchar(1000)             default '' comment '发展建议',
    create_user_id char(36)         not null default '' comment '创建人id',
    update_user_id char(36)         not null default '' comment '更新人id',
    create_time    datetime(3)      not null default current_timestamp(3) comment '创建时间',
    update_time    datetime(3)      not null default current_timestamp(3) comment '更新时间',
    deleted        tinyint unsigned not null default '0' comment '是否删除(0-未删除, 1-已删除)',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_xid_amid_uid (org_id, xpd_id, actv_member_id, user_id)
) comment = '新盘点项目人员扩展表（项目完整人员表以rv_activity_participation_member为准)';

alter table rv_performance_period
    add column score_total decimal(8, 2) null comment '绩效周期总分';

alter table rv_assessment_activity_result
    add item_id bigint unsigned default 0 null comment '叶节点id';

INSERT INTO rv_tree
(id, org_id, tree_id, tree_name, deleted, removed, create_time, create_user_id, update_time, update_user_id, db_archived, db_create_time, db_update_time, config)
VALUES(1859426836318371842, '********-0000-0000-0000-********0000', '1075876045620629505', '盘点项目分类', 0, 0, '2024-11-21 10:41:13.119', 'system', '2024-11-21 10:41:13.119', 'system', 0, '2024-11-21 10:41:19.214', '2024-12-23 11:01:25.940', '{}');

create or replace view udp_lite_user as
select *
from udp.udp_user_account
where main_platform = 1 and account_type in (1, 2);
    add item_id bigint unsigned default '0' null comment '叶节点id';

alter table `rv_activity_source` add column `item_id` bigint unsigned not null default 0 comment '任务id';
