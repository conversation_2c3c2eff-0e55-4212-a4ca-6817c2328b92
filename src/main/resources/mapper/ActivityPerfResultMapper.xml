<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_perf_result-->
    <id column="id" property="id" />
    <result column="actv_perf_id" property="actvPerfId" />
    <result column="org_id" property="orgId" />
    <result column="user_id" property="userId" />
    <result column="result_conf_id" property="resultConfId" />
    <result column="result_score" property="resultScore" />
    <result column="qualified" property="qualified" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, actv_perf_id, org_id, user_id, result_conf_id, result_score, qualified, create_user_id, 
    create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_perf_result
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_result (id, actv_perf_id, org_id, user_id, result_conf_id, result_score, 
      qualified, create_user_id, create_time, update_user_id, update_time)
    values (#{id}, #{actvPerfId}, #{orgId}, #{userId}, #{resultConfId}, #{resultScore}, 
      #{qualified}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_result
    (id, actv_perf_id, org_id, user_id, result_conf_id, result_score, qualified, create_user_id, 
      create_time, update_user_id, update_time)
    values
    (#{id}, #{actvPerfId}, #{orgId}, #{userId}, #{resultConfId}, #{resultScore}, #{qualified}, 
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
    on duplicate key update 
    id = #{id}, 
    actv_perf_id = #{actvPerfId}, 
    org_id = #{orgId}, 
    user_id = #{userId}, 
    result_conf_id = #{resultConfId}, 
    result_score = #{resultScore}, 
    qualified = #{qualified}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>

  <select id="selectByUserIdAndActId"  resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_activity_perf_result
    where org_id = #{orgId} and actv_perf_id = #{actId}
    <if test="(userIds != null and userIds.size()>0)">
        AND user_id in
        <foreach collection="userIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </if>
  </select>

  <select id="selectByUdp"  resultMap="BaseResultMap">
    select pr.qualified,
    pr.result_conf_id,
    pr.result_score
    from rv_activity_perf_result pr
    where pr.org_id = #{orgId} and pr.actv_perf_id = #{actId}
    <if test="(userIds != null and userIds.size()>0)">
      AND pr.user_id in
      <foreach collection="userIds" item="itemId" open="(" close=")" separator=",">
        #{itemId}
      </foreach>
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-12-11-->
  <insert id="insertList">
    INSERT INTO rv_activity_perf_result(
    id,
    actv_perf_id,
    org_id,
    user_id,
    result_conf_id,
    result_score,
    qualified,
    create_user_id,
    create_time,
    update_user_id,
    update_time
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id},
      #{element.actvPerfId},
      #{element.orgId},
      #{element.userId},
      #{element.resultConfId},
      #{element.resultScore},
      #{element.qualified},
      #{element.createUserId},
      #{element.createTime},
      #{element.updateUserId},
      #{element.updateTime}
      )
    </foreach>
  </insert>

  <update id="updateBatch" parameterType="java.util.List">
    UPDATE rv_activity_perf_result
    <trim prefix="SET" suffixOverrides=",">
      <trim prefix="actv_perf_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.actvPerfId}
        </foreach>
      </trim>
      <trim prefix="org_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="user_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.userId}
        </foreach>
      </trim>
      <trim prefix="result_conf_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.resultConfId}
        </foreach>
      </trim>
      <trim prefix="result_score = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.resultScore}
        </foreach>
      </trim>
      <trim prefix="qualified = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.qualified}
        </foreach>
      </trim>
      <trim prefix="create_user_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.updateTime}
        </foreach>
      </trim>
    </trim>
    WHERE id IN
    <foreach collection="list" item="item" open="(" separator=", " close=")">
      #{item.id}
    </foreach>
  </update>

  <select id="selectFinishCount"  resultType="java.lang.Long">
    select
    count(1)
    from rv_activity_perf_result pr
    join udp_lite_user u on u.id = pr.user_id and pr.org_id = u.org_id
    where pr.org_id = #{orgId} and pr.actv_perf_id = #{actId}
  </select>
    <select id="findByProfIdsAndUserIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf_result
        where org_id = #{orgId} and actv_perf_id in
        <foreach collection="actIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </select>
    <select id="selectFinishCountRange" resultType="java.lang.Long">
        select
            count(1)
        from rv_activity_perf_result pr
                 join udp_lite_user u on u.id = pr.user_id and pr.org_id = u.org_id
        where pr.org_id = #{orgId} and pr.actv_perf_id = #{actId}
        <if test="userIds != null and userIds.size() >0">
            and  pr.user_id in <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        </if>
    </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_perf_result a
    where a.org_id = #{orgId}
      -- 校验 actv_perf_id: 关联的绩效活动ID必须有效
      and exists(
      select 1 from rv_activity_perf b where b.org_id = a.org_id and b.id = a.actv_perf_id and b.deleted = 0
      )
      -- 校验 result_conf_id: 如果关联了绩效结果, 则该结果ID必须有效
      and (a.result_conf_id IS NULL OR exists(
      select 1 from rv_activity_perf_result_conf c where c.id = a.result_conf_id and c.deleted = 0
      ))
  </select>
</mapper>