<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileIndicatorMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO">
        <!--@mbg.generated-->
        <!--@Table rv_activity_profile_indicator-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="actv_profile_id" property="actvProfileId"/>
        <result column="sd_indicator_id" property="sdIndicatorId"/>
        <result column="rule_id" property="ruleId"/>
        <result column="order_index" property="orderIndex"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, actv_profile_id, sd_indicator_id, rule_id, order_index, create_user_id,
        update_user_id, create_time, update_time, deleted
    </sql>
    <update id="deleteByActvId">
        update rv_activity_profile_indicator
        set deleted        = 1,
            update_time    = now(),
            update_user_id = #{optUserId}
        where actv_profile_id = #{actProfileId}
          and org_id = #{orgId}
          and deleted = 0
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile_indicator
        where id = #{id}
    </select>
    <select id="findByActProfileId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile_indicator
        where actv_profile_id = #{actProfileId} and org_id = #{orgId} and deleted = 0
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_activity_profile_indicator (id, org_id, actv_profile_id, sd_indicator_id, rule_id, order_index,
        create_user_id, update_user_id, create_time, update_time, deleted)
        values (#{id}, #{orgId}, #{actvProfileId}, #{sdIndicatorId}, #{ruleId}, #{orderIndex},
        #{createUserId}, #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
    </insert>
    <insert id="batchInsert">
        insert into rv_activity_profile_indicator (id, org_id, actv_profile_id, sd_indicator_id, rule_id, order_index,
        create_user_id, update_user_id, create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.orgId}, #{item.actvProfileId}, #{item.sdIndicatorId}, #{item.ruleId},
            #{item.orderIndex},
            #{item.createUserId}, #{item.updateUserId}, #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_activity_profile_indicator
        (id, org_id, actv_profile_id, sd_indicator_id, rule_id, order_index, create_user_id,
        update_user_id, create_time, update_time, deleted)
        values
        (#{id}, #{orgId}, #{actvProfileId}, #{sdIndicatorId}, #{ruleId}, #{orderIndex}, #{createUserId},
        #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
        on duplicate key update
        id = #{id},
        org_id = #{orgId},
        actv_profile_id = #{actvProfileId},
        sd_indicator_id = #{sdIndicatorId},
        rule_id = #{ruleId},
        order_index = #{orderIndex},
        create_user_id = #{createUserId},
        update_user_id = #{updateUserId},
        create_time = #{createTime},
        update_time = #{updateTime},
        deleted = #{deleted}
    </insert>

<!--auto generated on 2025-06-06-->
    <select id="selectByOrgId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from rv_activity_profile_indicator a
      where org_id = #{orgId}
        and deleted = 0
      and exists (select 1 from rv_activity_profile b where b.org_id = a.org_id and b.id = a.actv_profile_id and b.deleted = 0)
    </select>
</mapper>