<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO">
        <!--@mbg.generated-->
        <!--@Table rv_activity_profile-->
        <id column="id" property="id"/>
        <result column="aom_actv_id" property="aomActvId"/>
        <result column="model_id" property="modelId"/>
        <result column="org_id" property="orgId"/>
        <result column="profile_name" property="profileName"/>
        <result column="actv_desc" property="actvDesc"/>
        <result column="eval_time_type" property="evalTimeType"/>
        <result column="eval_time" property="evalTime"/>
        <result column="score_qualified" property="scoreQualified"/>
        <result column="score_unqualified" property="scoreUnqualified"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, aom_actv_id, model_id, org_id, profile_name, actv_desc, eval_time_type, eval_time,
        score_qualified, score_unqualified, create_user_id, update_user_id, create_time,
        update_time, deleted
    </sql>
    <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO">
        UPDATE rv_activity_profile
        <set>
            <if test="aomActvId != null">aom_actv_id = #{aomActvId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="profileName != null">profile_name = #{profileName},</if>
            <if test="actvDesc != null">actv_desc = #{actvDesc},</if>
            <if test="evalTimeType != null">eval_time_type = #{evalTimeType},</if>
            <if test="evalTime != null">eval_time = #{evalTime},</if>
            <if test="scoreQualified != null">score_qualified = #{scoreQualified},</if>
            <if test="scoreUnqualified != null">score_unqualified = #{scoreUnqualified},</if>
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id} and deleted =0
    </update>
    <update id="deleteByPrimaryKey">
        update rv_activity_profile
        set deleted       = 1,
            update_user_id=#{optUserId},
            update_time=now()
        where id = #{id}
          and org_id = #{orgId}
          and deleted = 0
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile
        where id = #{id}
    </select>
    <select id="findByIdAndOrgId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile
        where id = #{id} and org_id = #{orgId} and deleted = 0
    </select>
    <select id="findAllValidActProfiles" resultMap="BaseResultMap">
        select ap.id,
               ap.aom_actv_id,
               ap.model_id,
               ap.org_id,
               ap.profile_name,
               ap.eval_time_type,
               ap.eval_time,
               ap.score_qualified,
               ap.score_unqualified
        from rv_activity_profile ap
                 join rv_activity ra on ra.org_id = ap.org_id and ra.id = ap.aom_actv_id
        where ap.deleted = 0
          and ra.actv_status = 2
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO">
        <!--@mbg.generated-->
        insert into rv_activity_profile (id, aom_actv_id, model_id, org_id, profile_name, actv_desc,
        eval_time_type, eval_time, score_qualified, score_unqualified, create_user_id,
        update_user_id, create_time, update_time, deleted)
        values (#{id}, #{aomActvId}, #{modelId}, #{orgId}, #{profileName}, #{actvDesc},
        #{evalTimeType}, #{evalTime}, #{scoreQualified}, #{scoreUnqualified}, #{createUserId},
        #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO">
        <!--@mbg.generated-->
        insert into rv_activity_profile
        (id, aom_actv_id, model_id, org_id, profile_name, actv_desc, eval_time_type, eval_time,
        score_qualified, score_unqualified, create_user_id, update_user_id, create_time,
        update_time, deleted)
        values
        (#{id}, #{aomActvId}, #{modelId}, #{orgId}, #{profileName}, #{actvDesc}, #{evalTimeType},
        #{evalTime}, #{scoreQualified}, #{scoreUnqualified}, #{createUserId}, #{updateUserId},
        #{createTime}, #{updateTime}, #{deleted})
        on duplicate key update
        id = #{id},
        aom_actv_id = #{aomActvId},
        model_id = #{modelId},
        org_id = #{orgId},
        profile_name = #{profileName},
        actv_desc = #{actvDesc},
        eval_time_type = #{evalTimeType},
        eval_time = #{evalTime},
        score_qualified = #{scoreQualified},
        score_unqualified = #{scoreUnqualified},
        create_user_id = #{createUserId},
        update_user_id = #{updateUserId},
        create_time = #{createTime},
        update_time = #{updateTime},
        deleted = #{deleted}
    </insert>

<!--auto generated on 2025-06-06-->
    <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_profile
    where org_id=#{orgId}
      and deleted = 0
  </select>
</mapper>