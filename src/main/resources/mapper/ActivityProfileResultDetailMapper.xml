<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileResultDetailMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_profile_result_detail-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="actv_profile_id" property="actvProfileId" />
    <result column="user_id" property="userId" />
    <result column="user_result_id" property="userResultId" />
    <result column="condition_id" property="conditionId" />
    <result column="label_type" property="labelType" />
    <result column="label_id" property="labelId" />
    <result column="label_value_id" property="labelValueId" />
    <result column="label_value" property="labelValue" />
    <result column="qualified" property="qualified" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, actv_profile_id, user_id, user_result_id, condition_id, label_type, label_id, 
    label_value_id, label_value, qualified, create_user_id, create_time, update_user_id, 
    update_time
  </sql>
    <delete id="deleteByIds">
        delete from rv_activity_profile_result_detail
        where org_id = #{orgId} and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_profile_result_detail
    where id = #{id}
  </select>
    <select id="findByActProfileIdAndResultIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile_result_detail
        where org_id = #{orgId}
        AND actv_profile_id = #{actProfileId}
        AND user_result_id in
        <foreach item="item" collection="userResultIds" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO">
    <!--@mbg.generated-->
    insert into rv_activity_profile_result_detail (id, org_id, actv_profile_id, user_id, user_result_id, condition_id, 
      label_type, label_id, label_value_id, label_value, qualified, create_user_id, 
      create_time, update_user_id, update_time)
    values (#{id}, #{orgId}, #{actvProfileId}, #{userId}, #{userResultId}, #{conditionId}, 
      #{labelType}, #{labelId}, #{labelValueId}, #{labelValue}, #{qualified}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO">
    <!--@mbg.generated-->
    insert into rv_activity_profile_result_detail
    (id, org_id, actv_profile_id, user_id, user_result_id, condition_id, label_type, 
      label_id, label_value_id, label_value, qualified, create_user_id, create_time, 
      update_user_id, update_time)
    values
    (#{id}, #{orgId}, #{actvProfileId}, #{userId}, #{userResultId}, #{conditionId}, #{labelType}, 
      #{labelId}, #{labelValueId}, #{labelValue}, #{qualified}, #{createUserId}, #{createTime}, 
      #{updateUserId}, #{updateTime})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    actv_profile_id = #{actvProfileId}, 
    user_id = #{userId}, 
    user_result_id = #{userResultId}, 
    condition_id = #{conditionId}, 
    label_type = #{labelType}, 
    label_id = #{labelId}, 
    label_value_id = #{labelValueId}, 
    label_value = #{labelValue}, 
    qualified = #{qualified}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>
    <insert id="insertBatch">
        insert into rv_activity_profile_result_detail (id, org_id, actv_profile_id, user_id, user_result_id,
        condition_id,
        label_type, label_id, label_value_id, label_value, qualified, create_user_id,
        create_time, update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.orgId}, #{item.actvProfileId}, #{item.userId}, #{item.userResultId},
            #{item.conditionId},
            #{item.labelType}, #{item.labelId}, #{item.labelValueId}, #{item.labelValue}, #{item.qualified},
            #{item.createUserId},
            #{item.createTime}, #{item.updateUserId}, #{item.updateTime})
        </foreach>

    </insert>

<!--auto generated on 2025-06-06-->
  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_profile_result_detail a
    where org_id=#{orgId}
    and exists (select 1 from rv_activity_profile b where b.org_id = a.org_id and b.id = a.actv_profile_id and b.deleted = 0)
  </select>
</mapper>