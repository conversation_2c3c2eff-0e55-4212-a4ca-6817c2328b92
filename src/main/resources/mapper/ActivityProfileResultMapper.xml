<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityProfileResultMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_profile_result-->
    <id column="id" property="id" />
    <result column="actv_profile_id" property="actvProfileId" />
    <result column="org_id" property="orgId" />
    <result column="user_id" property="userId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="qualified" property="qualified" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, actv_profile_id, org_id, user_id, sd_indicator_id, qualified, create_user_id, 
    create_time, update_user_id, update_time
  </sql>
    <delete id="deleteByIds">
        delete from rv_activity_profile_result
        where org_id =#{orgId} AND id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_profile_result
    where id = #{id}
  </select>
    <select id="findFinishedUserCount" resultType="java.lang.Long">
        select count(a.userCount)
        from (SELECT COUNT(DISTINCT r.user_id) AS userCount
              FROM rv_activity_profile_indicator i
                       JOIN rv_activity_profile_result r
                            ON i.actv_profile_id = r.actv_profile_id
                                AND i.sd_indicator_id = r.sd_indicator_id
              WHERE r.org_id = #{orgId}
                AND r.actv_profile_id = #{actProfileId}
                AND i.org_id = #{orgId}
                AND i.actv_profile_id = #{actProfileId}
                AND i.deleted = 0
              GROUP BY r.actv_profile_id, r.user_id
              HAVING COUNT(DISTINCT i.sd_indicator_id) = COUNT(DISTINCT r.sd_indicator_id)) a
    </select>
    <select id="findByActProfileIdAndUserIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_profile_result
        where org_id = #{orgId} AND actv_profile_id = #{actProfileId}
        <if test="userIds != null and userIds.size() > 0">
            AND user_id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="findFinishedUserCountRangeUser" resultType="java.lang.Long">
        select count(a.userCount)
        from (SELECT COUNT(DISTINCT r.user_id) AS userCount
              FROM rv_activity_profile_indicator i
                       JOIN rv_activity_profile_result r
                            ON i.actv_profile_id = r.actv_profile_id
                                AND i.sd_indicator_id = r.sd_indicator_id
              WHERE r.org_id = #{orgId}
                AND r.actv_profile_id = #{actProfileId}
                AND i.org_id = #{orgId}
                AND i.actv_profile_id = #{actProfileId}
                AND i.deleted = 0
                <if test="userIds != null and userIds.size() > 0">
                    AND r.user_id in <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
                </if>
              GROUP BY r.actv_profile_id, r.user_id
              HAVING COUNT(DISTINCT i.sd_indicator_id) = COUNT(DISTINCT r.sd_indicator_id)) a
    </select>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_profile_result (id, actv_profile_id, org_id, user_id, sd_indicator_id, qualified, 
      create_user_id, create_time, update_user_id, update_time)
    values (#{id}, #{actvProfileId}, #{orgId}, #{userId}, #{sdIndicatorId}, #{qualified}, 
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_profile_result
    (id, actv_profile_id, org_id, user_id, sd_indicator_id, qualified, create_user_id, 
      create_time, update_user_id, update_time)
    values
    (#{id}, #{actvProfileId}, #{orgId}, #{userId}, #{sdIndicatorId}, #{qualified}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
    on duplicate key update 
    id = #{id}, 
    actv_profile_id = #{actvProfileId}, 
    org_id = #{orgId}, 
    user_id = #{userId}, 
    sd_indicator_id = #{sdIndicatorId}, 
    qualified = #{qualified}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>
    <insert id="batchInsert">
        insert into rv_activity_profile_result (id, actv_profile_id, org_id, user_id, sd_indicator_id, qualified,
        create_user_id, create_time, update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.actvProfileId}, #{item.orgId}, #{item.userId}, #{item.sdIndicatorId}, #{item.qualified},
            #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, #{item.updateTime})
        </foreach>
    </insert>

<!--auto generated on 2025-06-06-->
  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_profile_result a
    where org_id=#{orgId}
    and exists (select 1 from rv_activity_profile b where b.org_id = a.org_id and b.id = a.actv_profile_id and b.deleted = 0)
  </select>
</mapper>