<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai.AiToolMessageMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO">
        <!--@mbg.generated-->
        <!--@Table rv_ai_tool_message-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="fullname" jdbcType="VARCHAR" property="fullname"/>
        <result column="qa_type" jdbcType="TINYINT" property="qaType"/>
        <result column="orig_qa_text" jdbcType="VARCHAR" property="origQaText"/>
        <result column="qa_text" jdbcType="VARCHAR" property="qaText"/>
        <result column="answer_text" jdbcType="LONGVARCHAR" property="answerText"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="checked" jdbcType="TINYINT" property="checked"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="useful" jdbcType="TINYINT" property="useful"/>
        <result column="curr_node_instance_id" jdbcType="VARCHAR" property="currNodeInstanceId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
        , org_id
        , user_id
        , user_name
        , fullname
        , qa_type
        , orig_qa_text
        , qa_text
        , answer_text
        , session_id
        , checked
        , create_time
        , update_time
        , deleted
        , useful
        , curr_node_instance_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_ai_tool_message
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO">
        <!--@mbg.generated-->
        insert into rv_ai_tool_message
        (id,
        org_id,
        user_id,
        user_name,
        fullname,
        qa_type,
        orig_qa_text,
        qa_text,
        answer_text,
        session_id,
        checked,
        create_time,
        update_time,
        deleted,
        useful,
        curr_node_instance_id)
        values
        (#{id,jdbcType=BIGINT},
        #{orgId,jdbcType=CHAR},
        #{userId,jdbcType=CHAR},
        #{userName,jdbcType=VARCHAR},
        #{fullname,jdbcType=VARCHAR},
        #{qaType,jdbcType=TINYINT},
        #{origQaText,jdbcType=VARCHAR},
        #{qaText,jdbcType=VARCHAR},
        #{answerText,jdbcType=LONGVARCHAR},
        #{sessionId,jdbcType=VARCHAR},
        #{checked,jdbcType=TINYINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{deleted,jdbcType=TINYINT},
        #{useful,jdbcType=TINYINT},
        #{currNodeInstanceId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolMessagePO">
        <!--@mbg.generated-->
        insert into rv_ai_tool_message
        (id,
        org_id,
        user_id,
        user_name,
        fullname,
        qa_type,
        orig_qa_text,
        qa_text,
        answer_text,
        session_id,
        checked,
        create_time,
        update_time,
        deleted,
        useful,
        curr_node_instance_id)
        values
        (#{id,jdbcType=BIGINT},
        #{orgId,jdbcType=CHAR},
        #{userId,jdbcType=CHAR},
        #{userName,jdbcType=VARCHAR},
        #{fullname,jdbcType=VARCHAR},
        #{qaType,jdbcType=TINYINT},
        #{origQaText,jdbcType=VARCHAR},
        #{qaText,jdbcType=VARCHAR},
        #{answerText,jdbcType=LONGVARCHAR},
        #{sessionId,jdbcType=VARCHAR},
        #{checked,jdbcType=TINYINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{deleted,jdbcType=TINYINT},
        #{useful,jdbcType=TINYINT},
        #{currNodeInstanceId,jdbcType=VARCHAR})
        on duplicate key update
        <trim suffixOverrides=",">
            id = #{id,jdbcType=BIGINT},
            org_id = #{orgId,jdbcType=CHAR},
            user_id = #{userId,jdbcType=CHAR},
            user_name = #{userName,jdbcType=VARCHAR},
            fullname = #{fullname,jdbcType=VARCHAR},
            qa_type = #{qaType,jdbcType=TINYINT},
            orig_qa_text = #{origQaText,jdbcType=VARCHAR},
            qa_text = #{qaText,jdbcType=VARCHAR},
            answer_text = #{answerText,jdbcType=LONGVARCHAR},
            session_id = #{sessionId,jdbcType=VARCHAR},
            checked = #{checked,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            deleted = #{deleted,jdbcType=TINYINT},
            useful = #{useful,jdbcType=TINYINT},
            curr_node_instance_id = #{currNodeInstanceId,jdbcType=VARCHAR}
        </trim>
    </insert>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_ai_tool_message
        where org_id = #{orgId,jdbcType=CHAR}
        and id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByOrgIdAndId">
        delete
        from rv_ai_tool_message
        where org_id = #{orgId,jdbcType=CHAR} and id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="batchInsertOrUpdate">
        insert into rv_ai_tool_message
        (id,
        org_id,
        user_id,
        user_name,
        fullname,
        qa_type,
        orig_qa_text,
        qa_text,
        answer_text,
        session_id,
        checked,
        create_time,
        update_time,
        useful,
        curr_node_instance_id,
        deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=CHAR},
            #{item.userId,jdbcType=CHAR},
            #{item.userName,jdbcType=VARCHAR},
            #{item.fullname,jdbcType=VARCHAR},
            #{item.qaType,jdbcType=TINYINT},
            #{item.origQaText,jdbcType=VARCHAR},
            #{item.qaText,jdbcType=VARCHAR},
            #{item.answerText,jdbcType=LONGVARCHAR},
            #{item.sessionId,jdbcType=VARCHAR},
            #{item.checked,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.useful,jdbcType=TINYINT},
            #{item.currNodeInstanceId,jdbcType=VARCHAR},
            #{item.deleted,jdbcType=TINYINT})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            fullname = values(fullname),
            qa_type = values(qa_type),
            orig_qa_text = values(orig_qa_text),
            qa_text = values(qa_text),
            answer_text = values(answer_text),
            session_id = values(session_id),
            checked = values(checked),
            update_time = values(update_time),
            useful = values(useful),
            curr_node_instance_id = values(curr_node_instance_id),
            deleted = values(deleted)
        </trim>
    </insert>

    <select id="selectByOrgIdAndSessionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_ai_tool_message
        where org_id = #{orgId,jdbcType=CHAR}
        and session_id = #{sessionId,jdbcType=VARCHAR}
        and deleted = 0
        order by create_time
    </select>

    <!--auto generated on 2024-11-06-->
    <update id="updateById">
        update rv_ai_tool_message
        <set>
            <if test="updated.userId != null">
                user_id = #{updated.userId,jdbcType=CHAR},
            </if>
            <if test="updated.userName != null">
                user_name = #{updated.userName,jdbcType=VARCHAR},
            </if>
            <if test="updated.fullname != null">
                fullname = #{updated.fullname,jdbcType=VARCHAR},
            </if>
            <if test="updated.qaType != null">
                qa_type = #{updated.qaType,jdbcType=TINYINT},
            </if>
            <if test="updated.origQaText != null">
                orig_qa_text = #{updated.origQaText,jdbcType=VARCHAR},
            </if>
            <if test="updated.qaText != null">
                qa_text = #{updated.qaText,jdbcType=VARCHAR},
            </if>
            <if test="updated.answerText != null">
                answer_text = #{updated.answerText,jdbcType=LONGVARCHAR},
            </if>
            <if test="updated.sessionId != null">
                session_id = #{updated.sessionId,jdbcType=VARCHAR},
            </if>
            <if test="updated.checked != null">
                checked = #{updated.checked,jdbcType=TINYINT},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.deleted != null">
                deleted = #{updated.deleted,jdbcType=TINYINT},
            </if>
            <if test="updated.useful != null">
                useful = #{updated.useful,jdbcType=TINYINT},
            </if>
            <if test="updated.currNodeInstanceId != null">
                curr_node_instance_id = #{updated.currNodeInstanceId,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <if test="updated.id != null">
                and id = #{updated.id,jdbcType=BIGINT}
            </if>
        </where>
    </update>
</mapper>