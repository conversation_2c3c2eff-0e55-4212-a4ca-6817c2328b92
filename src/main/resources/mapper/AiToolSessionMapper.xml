<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.ai.AiToolSessionMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolSessionPO">
        <!--@mbg.generated-->
        <!--@Table rv_ai_tool_session-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="session_title" jdbcType="VARCHAR" property="sessionTitle"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="active_status" jdbcType="TINYINT" property="activeStatus"/>
        <result column="useful" jdbcType="TINYINT" property="useful"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , user_id
             , session_id
             , session_title
             , start_time
             , end_time
             , active_status
             , useful
             , create_time
             , update_time
             , deleted
    </sql>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.ai.AiToolSessionPO">
        <!--@mbg.generated-->
        insert into rv_ai_tool_session
            (id,
             org_id,
             user_id,
             session_id,
             session_title,
             start_time,
             end_time,
             active_status,
             useful,
             create_time,
             update_time,
             deleted)
        values
            (#{id,jdbcType=BIGINT},
             #{orgId,jdbcType=CHAR},
             #{userId,jdbcType=CHAR},
             #{sessionId,jdbcType=VARCHAR},
             #{sessionTitle,jdbcType=VARCHAR},
             #{startTime,jdbcType=TIMESTAMP},
             #{endTime,jdbcType=TIMESTAMP},
             #{activeStatus,jdbcType=TINYINT},
             #{useful,jdbcType=TINYINT},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateTime,jdbcType=TIMESTAMP},
             #{deleted,jdbcType=TINYINT})
        on duplicate key update
        <trim suffixOverrides=",">
            id            = #{id,jdbcType=BIGINT},
            org_id        = #{orgId,jdbcType=CHAR},
            user_id       = #{userId,jdbcType=CHAR},
            session_id    = #{sessionId,jdbcType=VARCHAR},
            session_title = #{sessionTitle,jdbcType=VARCHAR},
            start_time    = #{startTime,jdbcType=TIMESTAMP},
            end_time      = #{endTime,jdbcType=TIMESTAMP},
            active_status = #{activeStatus,jdbcType=TINYINT},
            useful        = #{useful,jdbcType=TINYINT},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            deleted       = #{deleted,jdbcType=TINYINT}
        </trim>
    </insert>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
            <include refid="Base_Column_List"/>
        from rv_ai_tool_session
        where org_id = #{orgId,jdbcType=CHAR}
          and id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByOrgIdAndUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rv_ai_tool_session
        where org_id = #{orgId,jdbcType=CHAR}
          and user_id = #{userId,jdbcType=CHAR}
          and deleted = 0
          and session_title != ''
        order by create_time desc
    </select>

    <select id="selectByOrgIdAndSessionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rv_ai_tool_session
        where org_id = #{orgId,jdbcType=CHAR}
          and session_id = #{sessionId,jdbcType=VARCHAR}
          and deleted = 0
    </select>
</mapper>