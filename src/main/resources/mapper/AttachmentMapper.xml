<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.AttachmentMapper">
    <sql id="Base_Column_List">
        id
             , org_id
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , app_name
             , app_url
             , app_source
             , app_source_id
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="app_name" property="appName"/>
        <result column="app_url" property="appUrl"/>
        <result column="app_source" property="appSource"/>
        <result column="app_source_id" property="appSourceId"/>
    </resultMap>

    <delete id="deleteByAppSourceId">
        delete from rv_appendix where app_source_id = #{appSourceId} and org_id = #{orgId}
    </delete>

    <select id="listByAppSourceId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO">
        select
        <include refid="Base_Column_List"/>
        from rv_appendix
        where app_source_id = #{appSourceId}
          and org_id = #{orgId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-26-->
    <insert id="batchInsertOrUpdate">
        insert into rv_appendix
            (id,
             org_id,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             app_name,
             app_url,
             app_source,
             app_source_id)
        values
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.orgId},
             #{element.createUserId},
             #{element.createTime},
             #{element.updateUserId},
             #{element.updateTime},
             #{element.appName},
             #{element.appUrl},
             #{element.appSource},
             #{element.appSourceId})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            app_name       = values(app_name),
            app_url        = values(app_url),
            app_source     = values(app_source),
            app_source_id  = values(app_source_id),
            update_user_id = values(update_user_id),
            update_time    = values(update_time),
        </trim>
    </insert>

    <select id="selectByOrgIdAndId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO">
        select
        <include refid="Base_Column_List"/>
        from rv_appendix
        where org_id = #{orgId}
          and id = #{id}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-02-26-->
    <delete id="deleteById">
        delete from rv_appendix where id = #{id} and org_id = #{orgId}
    </delete>

    <insert id="insertOrUpdate">
        insert into rv_appendix
            (id,
             org_id,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             app_name,
             app_url,
             app_source,
             app_source_id)
        values
            (#{id},
             #{orgId},
             #{createUserId},
             #{createTime},
             #{updateUserId},
             #{updateTime},
             #{appName},
             #{appUrl},
             #{appSource},
             #{appSourceId})
        on duplicate key update
        <trim suffixOverrides=",">
            id             = values(id),
            org_id         = values(org_id),
            update_user_id = values(update_user_id),
            update_time    = values(update_time),
            app_name       = values(app_name),
            app_url        = values(app_url),
            app_source     = values(app_source),
            app_source_id  = values(app_source_id)
        </trim>
    </insert>

    <select id="listByOrgId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.AttachmentPO">
        select
        <include refid="Base_Column_List"/>
        from rv_appendix
        where org_id = #{orgId}
    </select>
</mapper>
