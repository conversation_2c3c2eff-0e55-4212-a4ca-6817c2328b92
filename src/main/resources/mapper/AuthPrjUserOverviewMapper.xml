<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthPrjUserOverviewMapper">

    <!-- 计算员工总得分 -->
    <select id="calculateUserTotalScore" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(arui.score), 0) AS totalScore
        FROM rv_authprj_result_user_indicator arui
        INNER JOIN rv_authprj ap ON arui.org_id = ap.org_id AND arui.authprj_id = ap.id
        WHERE arui.org_id = #{orgId}
          AND arui.authprj_id = #{authprjId}
          AND arui.user_id = #{userId}
          AND arui.deleted = 0
          AND ap.deleted = 0
    </select>

    <!-- 计算员工认证进度 -->
    <select id="calculateUserProgress" resultType="java.math.BigDecimal">
        SELECT COALESCE(rams.actv_completed_rate, 0)
        FROM rv_authprj ap
        LEFT JOIN rv_activity_member_statistics rams
            ON ap.org_id = rams.org_id
            AND ap.aom_prj_id = rams.actv_id
            AND rams.user_id = #{userId}
            AND rams.deleted = 0
        WHERE ap.org_id = #{orgId}
          AND ap.id = #{authprjId}
          AND ap.deleted = 0
        ORDER BY rams.actv_completed_time desc
        LIMIT 1
    </select>

    <!-- 获取员工认证分层结果名称 -->
    <select id="getUserLevelName" resultType="java.lang.String">
        SELECT arl.level_name
        FROM rv_authprj_result_user aru
        INNER JOIN rv_authprj ap ON aru.org_id = ap.org_id AND aru.authprj_id = ap.id
        INNER JOIN rv_authprj_rule_level arl 
            ON aru.org_id = arl.org_id 
            AND aru.authprj_id = arl.authprj_id 
            AND aru.level_id = arl.id
            AND arl.deleted = 0
        WHERE aru.org_id = #{orgId}
          AND aru.authprj_id = #{authprjId}
          AND aru.user_id = #{userId}
          AND aru.deleted = 0
          AND ap.deleted = 0
        LIMIT 1
    </select>

    <!-- 统计员工获得证书数量 -->
    <select id="countUserCertificates" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM rv_cert_log
        WHERE org_id = #{orgId}
          AND source_id = #{authprjId}
          AND user_id = #{userId}
          AND cert_status = 1
          AND deleted = 0
    </select>

  <select id="getUserStatisData" resultType="com.yxt.talent.rv.application.authprj.dto.AomUserStatisDataDTO">
    SELECT rams.actv_completed_status, rams.all_task_count, rams.all_task_completed_count, rams.all_task_completed_rate
    FROM rv_activity_member_statistics rams
    JOIN rv_activity ap
    ON ap.org_id = rams.org_id
    AND ap.id = rams.actv_id
    AND rams.user_id = #{userId}
    AND rams.deleted = 0
    WHERE ap.org_id = #{orgId}
    AND ap.id = #{aomId}
    AND ap.deleted = 0
    ORDER BY rams.actv_completed_time desc
    LIMIT 1
  </select>
</mapper>
