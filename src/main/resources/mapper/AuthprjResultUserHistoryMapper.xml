<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserHistoryMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserHistoryPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_result_user_history-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_id" property="authprjId" />
    <result column="user_id" property="userId" />
    <result column="level_id" property="levelId" />
    <result column="score_value" property="scoreValue" />
    <result column="auth_time" property="authTime" />
    <result column="manual_flag" property="manualFlag" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, deleted, 
    create_time, create_user_id, update_time, update_user_id
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user_history
    (id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, 
      deleted, create_time, create_user_id, update_time, update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjId}, #{item.userId}, #{item.levelId}, #{item.scoreValue}, 
        #{item.authTime}, #{item.manualFlag}, #{item.deleted}, #{item.createTime}, #{item.createUserId}, 
        #{item.updateTime}, #{item.updateUserId})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_id=values(authprj_id),
    user_id=values(user_id),
    level_id=values(level_id),
    score_value=values(score_value),
    auth_time=values(auth_time),
    manual_flag=values(manual_flag),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id)
  </insert>

  <select id="selectByAuthprjIdAndUserId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" />
      from rv_authprj_result_user_history
      where org_id = #{orgId}
      and authprj_id = #{authprjId}
      and user_id = #{userId}
      and deleted = 0
      order by create_time desc
  </select>

</mapper>