<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserIndicatorPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_result_user_indicator-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_id" property="authprjId" />
    <result column="user_id" property="userId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="source_id" property="sourceId" />
    <result column="score_total" property="scoreTotal" />
    <result column="score" property="score" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_id, user_id, sd_indicator_id, source_id, score_total, score, 
    deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user_indicator
    (id, org_id, authprj_id, user_id, sd_indicator_id, source_id, score_total, score, 
      deleted, create_user_id, create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjId}, #{item.userId}, #{item.sdIndicatorId}, 
        #{item.sourceId}, #{item.scoreTotal}, #{item.score}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_id=values(authprj_id),
    user_id=values(user_id),
    sd_indicator_id=values(sd_indicator_id),
    source_id=values(source_id),
    score_total=values(score_total),
    score=values(score),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <select id="selectByActvRefIdAndUserId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_authprj_result_user_indicator
    where authprj_id = #{authprjId}
      and user_id = #{userId}
      and deleted = 0
      and source_id = #{sourceId}
  </select>

  <select id="selectByAuthprjIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_authprj_result_user_indicator
    where org_id = #{orgId}
      and authprj_id = #{authprjId}
      and user_id = #{userId}
      and deleted = 0
  </select>

  <select id="selectByActvRefIdAndUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_authprj_result_user_indicator
    where org_id = #{orgId}
    and authprj_id = #{authprjId}
    <if test="(userIds != null and userIds.size()>0)">
        and user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </if>
    and deleted = 0
  </select>

  <update id="clearUserRecord">
    update rv_authprj_result_user_indicator set deleted = 1
    where org_id = #{orgId}
    and authprj_id = #{authPrjId}
    <if test="(userIds != null and userIds.size()>0)">
        AND user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </if>
    and deleted = 0
  </update>
  <delete id="deleteByAuthPrjAndUser">
    delete
    from rv_authprj_result_user_indicator
    where org_id = #{orgId} and authprj_id = #{authPrjId} and user_id = #{userId}
  </delete>
</mapper>