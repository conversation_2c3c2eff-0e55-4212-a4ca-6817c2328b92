<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_result_user-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="authprj_id" jdbcType="CHAR" property="authprjId" />
    <result column="user_id" jdbcType="CHAR" property="userId" />
    <result column="level_id" jdbcType="CHAR" property="levelId" />
    <result column="score_value" jdbcType="DECIMAL" property="scoreValue" />
    <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
    <result column="manual_flag" jdbcType="TINYINT" property="manualFlag" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="task_completed" jdbcType="TINYINT" property="taskCompleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, deleted, 
    create_time, create_user_id, update_time, update_user_id, task_completed
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_authprj_result_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="authprj_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.authprjId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="level_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.levelId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.scoreValue,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="auth_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.authTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="manual_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.manualFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="task_completed = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.taskCompleted,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=CHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user
    (id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, 
      deleted, create_time, create_user_id, update_time, update_user_id, task_completed
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.authprjId,jdbcType=CHAR}, 
        #{item.userId,jdbcType=CHAR}, #{item.levelId,jdbcType=CHAR}, #{item.scoreValue,jdbcType=DECIMAL}, 
        #{item.authTime,jdbcType=TIMESTAMP}, #{item.manualFlag,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.updateUserId,jdbcType=CHAR}, #{item.taskCompleted,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user
    (id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, 
      deleted, create_time, create_user_id, update_time, update_user_id, task_completed
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.authprjId,jdbcType=CHAR}, 
        #{item.userId,jdbcType=CHAR}, #{item.levelId,jdbcType=CHAR}, #{item.scoreValue,jdbcType=DECIMAL}, 
        #{item.authTime,jdbcType=TIMESTAMP}, #{item.manualFlag,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.updateUserId,jdbcType=CHAR}, #{item.taskCompleted,jdbcType=TINYINT})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_id=values(authprj_id),
    user_id=values(user_id),
    level_id=values(level_id),
    score_value=values(score_value),
    auth_time=values(auth_time),
    manual_flag=values(manual_flag),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id),
    task_completed=values(task_completed)
  </insert>
  <insert id="insertOnDuplicateUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user
    (id, org_id, authprj_id, user_id, level_id, score_value, auth_time, manual_flag, 
      deleted, create_time, create_user_id, update_time, update_user_id, task_completed
      )
    values
    (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{authprjId,jdbcType=CHAR}, #{userId,jdbcType=CHAR}, 
      #{levelId,jdbcType=CHAR}, #{scoreValue,jdbcType=DECIMAL}, #{authTime,jdbcType=TIMESTAMP}, 
      #{manualFlag,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, 
      #{taskCompleted,jdbcType=TINYINT})
    on duplicate key update 
    id = #{id,jdbcType=CHAR}, 
    org_id = #{orgId,jdbcType=CHAR}, 
    authprj_id = #{authprjId,jdbcType=CHAR}, 
    user_id = #{userId,jdbcType=CHAR}, 
    level_id = #{levelId,jdbcType=CHAR}, 
    score_value = #{scoreValue,jdbcType=DECIMAL}, 
    auth_time = #{authTime,jdbcType=TIMESTAMP}, 
    manual_flag = #{manualFlag,jdbcType=TINYINT}, 
    deleted = #{deleted,jdbcType=TINYINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    create_user_id = #{createUserId,jdbcType=CHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_user_id = #{updateUserId,jdbcType=CHAR}, 
    task_completed = #{taskCompleted,jdbcType=TINYINT}
  </insert>
  <insert id="insertOnDuplicateUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserPO">
    <!--@mbg.generated-->
    insert into rv_authprj_result_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="authprjId != null and authprjId != ''">
        authprj_id,
      </if>
      <if test="userId != null and userId != ''">
        user_id,
      </if>
      <if test="levelId != null and levelId != ''">
        level_id,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="authTime != null">
        auth_time,
      </if>
      <if test="manualFlag != null">
        manual_flag,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id,
      </if>
      <if test="taskCompleted != null">
        task_completed,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="authprjId != null and authprjId != ''">
        #{authprjId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        #{userId,jdbcType=CHAR},
      </if>
      <if test="levelId != null and levelId != ''">
        #{levelId,jdbcType=CHAR},
      </if>
      <if test="scoreValue != null">
        #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="authTime != null">
        #{authTime,jdbcType=TIMESTAMP},
      </if>
      <if test="manualFlag != null">
        #{manualFlag,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="taskCompleted != null">
        #{taskCompleted,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null and id != ''">
        id = #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="authprjId != null and authprjId != ''">
        authprj_id = #{authprjId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        user_id = #{userId,jdbcType=CHAR},
      </if>
      <if test="levelId != null and levelId != ''">
        level_id = #{levelId,jdbcType=CHAR},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="authTime != null">
        auth_time = #{authTime,jdbcType=TIMESTAMP},
      </if>
      <if test="manualFlag != null">
        manual_flag = #{manualFlag,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="taskCompleted != null">
        task_completed = #{taskCompleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <select id="selectByAuthprjIdAndUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_authprj_result_user
    where org_id = #{orgId}
      and authprj_id = #{authprjId}
      and user_id = #{userId}
      and deleted = 0
    limit 1
  </select>

  <!-- 获取员工指标详细统计数据 -->
  <select id="getUserIndicatorDetails" resultType="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjUserIndicatorDetailVO">
    select arui.sd_indicator_id                              as firstIndicatorId
         , arui.source_id                                    as refId
         , coalesce(aai.item_name, aai.ref_name, '未知活动') as refName
         , arui.score_total                                  as scoreTotal
         , arui.score                                        as score
    from rv_authprj_result_user_indicator arui
    inner join rv_authprj                 ap on arui.org_id = ap.org_id and arui.authprj_id = ap.id
    left join  rv_activity_arrange_item   aai
               on arui.org_id = aai.org_id and arui.source_id = aai.ref_id and aai.deleted = 0
    where arui.org_id = #{orgId}
      and arui.authprj_id = #{authprjId}
      and arui.user_id = #{userId}
      and arui.deleted = 0
      and ap.deleted = 0
    <if test="indicatorIds != null and indicatorIds.size() &gt; 0">
      AND arui.sd_indicator_id IN
      <foreach close=")" collection="indicatorIds" item="indicatorId" open="(" separator=",">
        #{indicatorId}
      </foreach>
    </if>
    ORDER BY arui.sd_indicator_id
  </select>

  <select id="selectByAuthprjIdAndUserIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_authprj_result_user
    where org_id = #{orgId}
    and authprj_id = #{authprjId}
    and deleted = 0
    <if test="(userIds != null and userIds.size()&gt;0)">
        AND user_id in
        <foreach close=")" collection="userIds" item="userId" open="(" separator=",">
            #{userId}
        </foreach>
    </if>
  </select>

  <select id="selectByAuthprjId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_authprj_result_user
    where org_id = #{orgId}
    and authprj_id = #{authprjId}
    and deleted = 0
  </select>

  <delete id="deleteByAuthPrjAndUser">
    delete
    from rv_authprj_result_user
    where org_id = #{orgId}
      and authprj_id = #{authPrjId}
      and user_id = #{userId}
      and manual_flag = 0
  </delete>
</mapper>