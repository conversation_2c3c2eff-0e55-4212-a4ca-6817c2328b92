<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimcombMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO">
    <!--@mbg.generated-->
    <!--@Table rv_calimeet_result_user_dimcomb-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="user_id" property="userId" />
    <result column="dim_comb_id" property="dimCombId" />
    <result column="cell_index" property="cellIndex" />
    <result column="cell_id" property="cellId" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calimeet_id" property="calimeetId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, user_id, dim_comb_id, cell_index, cell_id, calc_batch_no, cali_flag, 
    original_snap, deleted, create_user_id, create_time, update_user_id, update_time, 
    calimeet_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_calimeet_result_user_dimcomb
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_calimeet_result_user_dimcomb
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dimcomb (id, org_id, xpd_id, user_id, dim_comb_id, cell_index, cell_id, 
      calc_batch_no, cali_flag, original_snap, deleted, create_user_id, create_time, 
      update_user_id, update_time, calimeet_id)
    values (#{id}, #{orgId}, #{xpdId}, #{userId}, #{dimCombId}, #{cellIndex}, #{cellId}, 
      #{calcBatchNo}, #{caliFlag}, #{originalSnap}, #{deleted}, #{createUserId}, #{createTime}, 
      #{updateUserId}, #{updateTime}, #{calimeetId})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_dimcomb
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      user_id = #{userId},
      dim_comb_id = #{dimCombId},
      cell_index = #{cellIndex},
      cell_id = #{cellId},
      calc_batch_no = #{calcBatchNo},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calimeet_id = #{calimeetId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_dimcomb
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="dim_comb_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.dimCombId}
        </foreach>
      </trim>
      <trim prefix="cell_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellIndex}
        </foreach>
      </trim>
      <trim prefix="cell_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellId}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calimeet_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calimeetId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dimcomb
    (id, org_id, xpd_id, user_id, dim_comb_id, cell_index, cell_id, calc_batch_no, cali_flag, 
      original_snap, deleted, create_user_id, create_time, update_user_id, update_time, 
      calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.dimCombId}, #{item.cellIndex}, 
        #{item.cellId}, #{item.calcBatchNo}, #{item.caliFlag}, #{item.originalSnap}, #{item.deleted}, 
        #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, 
        #{item.calimeetId})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dimcomb
    (id, org_id, xpd_id, user_id, dim_comb_id, cell_index, cell_id, calc_batch_no, cali_flag, 
      original_snap, deleted, create_user_id, create_time, update_user_id, update_time, 
      calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.dimCombId}, #{item.cellIndex}, 
        #{item.cellId}, #{item.calcBatchNo}, #{item.caliFlag}, #{item.originalSnap}, #{item.deleted}, 
        #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, 
        #{item.calimeetId})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    user_id=values(user_id),
    dim_comb_id=values(dim_comb_id),
    cell_index=values(cell_index),
    cell_id=values(cell_id),
    calc_batch_no=values(calc_batch_no),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calimeet_id=values(calimeet_id)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dimcomb
    (id, org_id, xpd_id, user_id, dim_comb_id, cell_index, cell_id, calc_batch_no, cali_flag, 
      original_snap, deleted, create_user_id, create_time, update_user_id, update_time, 
      calimeet_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{userId}, #{dimCombId}, #{cellIndex}, #{cellId}, #{calcBatchNo}, 
      #{caliFlag}, #{originalSnap}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, 
      #{updateTime}, #{calimeetId})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    user_id = #{userId}, 
    dim_comb_id = #{dimCombId}, 
    cell_index = #{cellIndex}, 
    cell_id = #{cellId}, 
    calc_batch_no = #{calcBatchNo}, 
    cali_flag = #{caliFlag}, 
    original_snap = #{originalSnap}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}, 
    calimeet_id = #{calimeetId}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimcombPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_dimcomb
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="dimCombId != null">
        dim_comb_id,
      </if>
      <if test="cellIndex != null">
        cell_index,
      </if>
      <if test="cellId != null">
        cell_id,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calimeetId != null">
        calimeet_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="dimCombId != null">
        #{dimCombId},
      </if>
      <if test="cellIndex != null">
        #{cellIndex},
      </if>
      <if test="cellId != null">
        #{cellId},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calimeetId != null">
        #{calimeetId},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="dimCombId != null">
        dim_comb_id = #{dimCombId},
      </if>
      <if test="cellIndex != null">
        cell_index = #{cellIndex},
      </if>
      <if test="cellId != null">
        cell_id = #{cellId},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calimeetId != null">
        calimeet_id = #{calimeetId},
      </if>
    </trim>
  </insert>

  <delete id="deleteUserDimcombResults">
    delete from rv_calimeet_result_user_dimcomb a
    where a.org_id = #{orgId}
    and a.calimeet_id = #{caliMeetId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </delete>
</mapper>