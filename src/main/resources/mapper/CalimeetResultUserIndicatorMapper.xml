<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    <!--@mbg.generated-->
    <!--@Table rv_calimeet_result_user_indicator-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="user_id" property="userId" />
    <result column="score_value" property="scoreValue" />
    <result column="result_detail" property="resultDetail" />
    <result column="qualified" property="qualified" />
    <result column="perf_summary" property="perfSummary" />
    <result column="perf_result_id" property="perfResultId" />
    <result column="result_dim_id" property="resultDimId" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calimeet_id" property="calimeetId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, qualified, 
    perf_summary, perf_result_id, result_dim_id, calc_batch_no, cali_flag, original_snap, 
    deleted, create_user_id, create_time, update_user_id, update_time, calimeet_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_calimeet_result_user_indicator
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_calimeet_result_user_indicator
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_indicator (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, 
      qualified, perf_summary, perf_result_id, result_dim_id, calc_batch_no, 
      cali_flag, original_snap, deleted, create_user_id, create_time, update_user_id, 
      update_time, calimeet_id)
    values (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{userId}, #{scoreValue}, #{resultDetail}, 
      #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDimId}, #{calcBatchNo}, 
      #{caliFlag}, #{originalSnap}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, 
      #{updateTime}, #{calimeetId})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_indicator
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      sd_indicator_id = #{sdIndicatorId},
      user_id = #{userId},
      score_value = #{scoreValue},
      result_detail = #{resultDetail},
      qualified = #{qualified},
      perf_summary = #{perfSummary},
      perf_result_id = #{perfResultId},
      result_dim_id = #{resultDimId},
      calc_batch_no = #{calcBatchNo},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calimeet_id = #{calimeetId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_calimeet_result_user_indicator
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="sd_indicator_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdIndicatorId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreValue}
        </foreach>
      </trim>
      <trim prefix="result_detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultDetail}
        </foreach>
      </trim>
      <trim prefix="qualified = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.qualified}
        </foreach>
      </trim>
      <trim prefix="perf_summary = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfSummary}
        </foreach>
      </trim>
      <trim prefix="perf_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfResultId}
        </foreach>
      </trim>
      <trim prefix="result_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultDimId}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calimeet_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calimeetId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, qualified, 
      perf_summary, perf_result_id, result_dim_id, calc_batch_no, cali_flag, original_snap, 
      deleted, create_user_id, create_time, update_user_id, update_time, calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.sdIndicatorId}, #{item.userId}, 
        #{item.scoreValue}, #{item.resultDetail}, #{item.qualified}, #{item.perfSummary}, 
        #{item.perfResultId}, #{item.resultDimId}, #{item.calcBatchNo}, #{item.caliFlag}, 
        #{item.originalSnap}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, 
        #{item.updateUserId}, #{item.updateTime}, #{item.calimeetId})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, qualified, 
      perf_summary, perf_result_id, result_dim_id, calc_batch_no, cali_flag, original_snap, 
      deleted, create_user_id, create_time, update_user_id, update_time, calimeet_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.sdIndicatorId}, #{item.userId}, 
        #{item.scoreValue}, #{item.resultDetail}, #{item.qualified}, #{item.perfSummary}, 
        #{item.perfResultId}, #{item.resultDimId}, #{item.calcBatchNo}, #{item.caliFlag}, 
        #{item.originalSnap}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, 
        #{item.updateUserId}, #{item.updateTime}, #{item.calimeetId})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    sd_indicator_id=values(sd_indicator_id),
    user_id=values(user_id),
    score_value=values(score_value),
    result_detail=values(result_detail),
    qualified=values(qualified),
    perf_summary=values(perf_summary),
    perf_result_id=values(perf_result_id),
    result_dim_id=values(result_dim_id),
    calc_batch_no=values(calc_batch_no),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calimeet_id=values(calimeet_id)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, qualified, 
      perf_summary, perf_result_id, result_dim_id, calc_batch_no, cali_flag, original_snap, 
      deleted, create_user_id, create_time, update_user_id, update_time, calimeet_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{userId}, #{scoreValue}, #{resultDetail}, 
      #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDimId}, #{calcBatchNo}, 
      #{caliFlag}, #{originalSnap}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, 
      #{updateTime}, #{calimeetId})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    sd_indicator_id = #{sdIndicatorId}, 
    user_id = #{userId}, 
    score_value = #{scoreValue}, 
    result_detail = #{resultDetail}, 
    qualified = #{qualified}, 
    perf_summary = #{perfSummary}, 
    perf_result_id = #{perfResultId}, 
    result_dim_id = #{resultDimId}, 
    calc_batch_no = #{calcBatchNo}, 
    cali_flag = #{caliFlag}, 
    original_snap = #{originalSnap}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}, 
    calimeet_id = #{calimeetId}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_result_user_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="resultDetail != null">
        result_detail,
      </if>
      <if test="qualified != null">
        qualified,
      </if>
      <if test="perfSummary != null">
        perf_summary,
      </if>
      <if test="perfResultId != null">
        perf_result_id,
      </if>
      <if test="resultDimId != null">
        result_dim_id,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calimeetId != null">
        calimeet_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="sdIndicatorId != null">
        #{sdIndicatorId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="resultDetail != null">
        #{resultDetail},
      </if>
      <if test="qualified != null">
        #{qualified},
      </if>
      <if test="perfSummary != null">
        #{perfSummary},
      </if>
      <if test="perfResultId != null">
        #{perfResultId},
      </if>
      <if test="resultDimId != null">
        #{resultDimId},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calimeetId != null">
        #{calimeetId},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id = #{sdIndicatorId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="resultDetail != null">
        result_detail = #{resultDetail},
      </if>
      <if test="qualified != null">
        qualified = #{qualified},
      </if>
      <if test="perfSummary != null">
        perf_summary = #{perfSummary},
      </if>
      <if test="perfResultId != null">
        perf_result_id = #{perfResultId},
      </if>
      <if test="resultDimId != null">
        result_dim_id = #{resultDimId},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calimeetId != null">
        calimeet_id = #{calimeetId},
      </if>
    </trim>
  </insert>

  <delete id="deleteUserIndicatorResults">
    delete from rv_calimeet_result_user_indicator a
    where a.org_id = #{orgId}
    and a.calimeet_id = #{caliMeetId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </delete>

  <select id="getByUserIdIndicatorIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO">
    select id,
    user_id,
    sd_indicator_id,
    score_value,
    qualified,
    perf_result_id from rv_calimeet_result_user_indicator
    where org_id = #{orgId} and calimeet_id = #{caliMeetId} and user_id in
    <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and deleted = 0
    <choose>
      <when test="indicatorIds != null and indicatorIds.size() != 0">
        and sd_indicator_id in
        <foreach collection="indicatorIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        and 1 = 2
      </otherwise>
    </choose>
  </select>

  <select id="selectByCaliMeetIdAndUserIds" resultMap="BaseResultMap">
    select *
    from rv_calimeet_result_user_indicator
    where org_id = #{orgId} and calimeet_id = #{caliMeetId}
    and deleted = 0
    <if test="(userIds != null and userIds.size()>0)">
      AND user_id in
      <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
  </select>

  <select id="selectByOrgIdAndCalimeetId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from rv_calimeet_result_user_indicator
      where
      org_id = #{orgId,jdbcType=VARCHAR}
      AND calimeet_id = #{calimeetId,jdbcType=VARCHAR}
      and deleted = 0
  </select>
</mapper>