<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.career.CareerHistoryMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.CareerHistoryPO">
    <!--@mbg.generated-->
    <!--@Table rv_career_history-->
    <id column="id" property="id"/>
    <result column="third_user_id" property="thirdUserId"/>
    <result column="org_id" property="orgId"/>
    <result column="third_dept_name" property="thirdDeptName"/>
    <result column="third_position_name" property="thirdPositionName"/>
    <result column="third_jobgrade_name" property="thirdJobgradeName"/>
    <result column="action_name" property="actionName"/>
    <result column="occurrence_time" property="occurrenceTime"/>
    <result column="deleted" property="deleted"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="third_career_history_id" property="thirdCareerHistoryId"/>
    <result column="user_id" property="userId"/>
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id
         , third_user_id
         , org_id
         , third_dept_name
         , third_position_name
         , third_jobgrade_name
         , action_name
         , occurrence_time
         , deleted
         , create_time
         , update_time
         , third_career_history_id
         , user_id
  </sql>

  <insert id="batchInsertOrUpdate">
    <!--@mbg.generated-->
    insert into rv_career_history
      (id,
       third_user_id,
       org_id,
       third_dept_name,
       third_position_name,
       third_jobgrade_name,
       action_name,
       occurrence_time,
       third_career_history_id,
       deleted,
       create_time,
       update_time,
       user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id},
       #{item.thirdUserId},
       #{item.orgId},
       #{item.thirdDeptName},
       #{item.thirdPositionName},
       #{item.thirdJobgradeName},
       #{item.actionName},
       #{item.occurrenceTime},
       #{item.thirdCareerHistoryId},
       #{item.deleted},
       #{item.createTime},
       #{item.updateTime},
       #{item.userId})
    </foreach>
    on duplicate key update
    <trim suffixOverrides=",">
      third_user_id           = values(third_user_id),
      org_id                  = values(org_id),
      third_dept_name         = values(third_dept_name),
      third_position_name     = values(third_position_name),
      third_jobgrade_name     = values(third_jobgrade_name),
      action_name             = values(action_name),
      occurrence_time         = values(occurrence_time),
      third_career_history_id = values(third_career_history_id),
      deleted                 = values(deleted),
      update_time             = values(update_time),
      user_id                 = values(user_id)
    </trim>
  </insert>

  <select id="selectByThirdUserIds" resultType="java.lang.String">
    select distinct third_user_id from rv_career_history
    where org_id = #{orgId}
    <choose>
      <when test="thirdUserIds != null and thirdUserIds.size() != 0">
        and third_user_id in
        <foreach collection="thirdUserIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>
  <select id="selectByThirdUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_career_history
    where org_id = #{orgId,jdbcType=VARCHAR}
      and third_user_id = #{thirdUserId,jdbcType=VARCHAR}
      and deleted = 0
  </select>


  <update id="deleteByThirdUserIdAndThirdCareerHistoryIds">
    update rv_career_history
    set deleted     = 1,
        update_time = now()
    where org_id = #{orgId}
      and third_career_history_id in
    <foreach collection="thirdCareerHistoryIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and deleted = 0
  </update>
  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_career_history
    where org_id = #{orgId,jdbcType=VARCHAR}
      and user_id = #{userId,jdbcType=VARCHAR}
      and deleted = 0
  </select>

  <update id="deleteByUserId">
    update rv_career_history set deleted = 1, update_time = now()
    where org_id = #{orgId} and deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_career_history
    where org_id = #{orgId}
      and deleted = 0
  </select>


</mapper>