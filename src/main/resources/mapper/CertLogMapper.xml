<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.CertLogMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.CertLogPO">
    <!--@mbg.generated-->
    <!--@Table rv_cert_log-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="source_id" property="sourceId" />
    <result column="cert_temp_id" property="certTempId" />
    <result column="user_id" property="userId" />
    <result column="cert_status" property="certStatus" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, source_id, cert_temp_id, user_id, cert_status, deleted, create_user_id, 
    create_time, update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_cert_log
    (id, org_id, source_id, cert_temp_id, user_id, cert_status, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.sourceId}, #{item.certTempId}, #{item.userId}, 
        #{item.certStatus}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, 
        #{item.updateUserId}, #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    source_id=values(source_id),
    cert_temp_id=values(cert_temp_id),
    user_id=values(user_id),
    cert_status=values(cert_status),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <!-- 根据颁发记录ID列表更新证书状态 -->
  <update id="updateCertStatusByIssueIds">
    UPDATE rv_cert_log
    SET cert_status = #{certStatus}, update_time = NOW()
    WHERE id IN
    <foreach collection="issueIds" item="issueId" open="(" separator="," close=")">
      #{issueId}
    </foreach>
    AND deleted = 0
  </update>

  <select id="selectByCertTempIdAndUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_cert_log
    where cert_temp_id = #{certTempId}
      and user_id = #{userId}
      and source_id = #{sourceId}
      and deleted = 0
  </select>

  <update id="updateCertStatus">
    update rv_cert_log
    set cert_status = #{certStatus},
        update_time = now()
    where org_id = #{orgId}
      and source_id = #{sourceId}
      and cert_temp_id = #{certTempId}
      and deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>
</mapper>