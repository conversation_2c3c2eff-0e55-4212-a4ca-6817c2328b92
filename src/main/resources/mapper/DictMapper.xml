<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.DictMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO">
        <!--@mbg.generated-->
        <!--@Table rv_dict-->
        <id column="id" property="id"/>
        <result column="dict_type" property="dictType"/>
        <result column="dict_name" property="dictName"/>
        <result column="dict_value" property="dictValue"/>
        <result column="order_index" property="orderIndex"/>
        <result column="enabled" property="enabled"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , dict_type
             , dict_name
             , dict_value
             , order_index
             , enabled
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <delete id="deleteById" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from rv_dict where id = #{id}
    </delete>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO">
        <!--@mbg.generated-->
        insert into rv_dict
            (id,
             dict_type,
             dict_name,
             dict_value,
             order_index,
             enabled,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id},
             #{dictType},
             #{dictName},
             #{dictValue},
             #{orderIndex},
             #{enabled},
             #{createUserId},
             #{createTime},
             #{updateUserId},
             #{updateTime})
        on duplicate key update
        <trim suffixOverrides=",">
            dict_type      = #{dictType},
            dict_name      = #{dictName},
            dict_value     = #{dictValue},
            order_index    = #{orderIndex},
            enabled        = #{enabled},
            update_user_id = #{updateUserId},
            update_time    = #{updateTime}
        </trim>
    </insert>
    <select id="selectByType"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO">
        select id
             , id
             , dict_type
             , dict_name
             , dict_value
             , order_index
             , enabled
             , create_user_id
             , create_time
             , update_user_id
             , update_time
        from rv_dict
        where dict_type = #{type}
          and dict_value = #{value}
    </select>

    <select id="listByType"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.DictPO">
        select id
             , dict_type
             , dict_name
             , dict_value
             , order_index
             , enabled
             , create_user_id
             , create_time
             , update_user_id
             , update_time
        from rv_dict
        where dict_type = #{type}
          and enabled = 1
        order by order_index
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from rv_dict
        where id = #{id}
    </select>
</mapper>