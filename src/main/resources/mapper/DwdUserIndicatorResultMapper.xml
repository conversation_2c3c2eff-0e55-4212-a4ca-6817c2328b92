<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.spmodel.DwdUserIndicatorResultMapper">
  <select id="listUserIndicator" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto">
    select third_user_id as user_id,indicator_id as sd_indicator_id,max(score_ten) as score,max(qualified) as qualified
    from dwd_user_indicator_result where org_id = '${orgId}' and deleted = 0
    and third_user_id in
      <foreach collection="thirdUserIds" item="item" open="(" separator="," close=")">
        '${item}'
      </foreach>
    and indicator_id in
    <foreach collection="indicatorIds" item="item" open="(" separator="," close=")">
      '${item}'
    </foreach>
    group by third_user_id,indicator_id
  </select>
</mapper>