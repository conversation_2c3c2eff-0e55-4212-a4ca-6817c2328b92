<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertIndicatorPO">
    <!--@mbg.generated-->
    <!--@Table rv_expert_indicator-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="expert_id" property="expertId" />
    <result column="ind_id" property="indId" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, expert_id, ind_id, deleted, create_time, create_user_id, update_time, 
    update_user_id
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_expert_indicator
    (id, org_id, expert_id, ind_id, deleted, create_time, create_user_id, update_time, 
      update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.expertId}, #{item.indId}, #{item.deleted}, #{item.createTime}, 
        #{item.createUserId}, #{item.updateTime}, #{item.updateUserId})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    expert_id=values(expert_id),
    ind_id=values(ind_id),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id)
  </insert>

  <select id="findByOrgIdAndExpertIdIn" resultMap="BaseResultMap">
    select a.id,a.ind_id,a.expert_id from rv_expert_indicator a where a.deleted=0 and a.org_id=#{orgId} and
    a.expert_id in
    <foreach collection="list" item="itemId" open="(" close=")" separator=",">
      #{itemId}
    </foreach>
    order by a.create_time asc,a.id
    </select>

  <update id="deleteByExpertId">
    update rv_expert_indicator a set a.deleted=1,a.update_time=now() where a.expert_id=#{expertId} and a.org_id=#{orgId}
    </update>
</mapper>