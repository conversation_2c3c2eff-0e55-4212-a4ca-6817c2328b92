<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO">
        <!--@mbg.generated-->
        <!--@Table rv_performance_grade-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="grade_name" property="gradeName"/>
        <result column="grade_value" property="gradeValue"/>
        <result column="order_index" property="orderIndex"/>
        <result column="deleted" property="deleted"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="state" property="state"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , grade_name
             , grade_value
             , order_index
             , deleted
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , state
    </sql>

    <insert id="batchInsertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO">
        <!--@mbg.generated-->
        insert into rv_performance_grade
            (id,
             org_id,
             grade_name,
             grade_value,
             order_index,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             state)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.orgId},
             #{item.gradeName},
             #{item.gradeValue},
             #{item.orderIndex},
             #{item.deleted},
             #{item.createUserId},
             #{item.createTime},
             #{item.updateUserId},
             #{item.updateTime},
             #{item.state})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = values(org_id),
            grade_name     = values(grade_name),
            grade_value    = values(grade_value),
            order_index    = values(order_index),
            deleted        = values(deleted),
            update_user_id = values(update_user_id),
            update_time    = values(update_time),
            state          = values(state)
        </trim>
    </insert>

    <select id="currentMaxSort" resultType="int">
        select ifnull(max(grade_value), 99) from rv_performance_grade where org_id = #{orgId}
    </select>

    <select id="selectMaxOrderIndex" resultType="int">
        select ifnull(max(order_index), 0) from rv_performance_grade where org_id = #{orgId}
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
          and deleted = 0
        order by order_index
    </select>

    <select id="selectByOrgIdAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
        order by order_index
    </select>

    <select id="selectByOrgIdInState" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
        and deleted = 0
        and state = 1
        order by order_index
    </select>

    <select id="selectByOrgIdIncludeDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
        order by order_index
    </select>

    <select id="countByOrgId" resultType="long">
        select count(1) from rv_performance_grade where org_id = #{orgId} and deleted = 0
    </select>

    <select id="countByGradeName" resultType="long">
        select count(1)
        from rv_performance_grade
        where org_id = #{orgId}
          and grade_name = #{gradeName}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
        and deleted = 0
    </select>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
          and id = #{id}
          and deleted = 0
    </select>

    <select id="selectByOrgIdAndOrderIndexBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
        and deleted = 0
        and order_index between #{minOrderIndex} and #{maxOrderIndex}
        order by order_index
    </select>

    <update id="updateStateById">
        update
            rv_performance_grade
        set
            state = #{state},
            update_user_id = #{userId},
            update_time = now()
        where
            org_id = #{orgId}
          and id = #{id}
    </update>

    <select id="findGradeOrgId" resultType="java.lang.String">
        select
            distinct org_id
        from rv_performance_grade
    </select>

    <select id="findGradeValueNum" resultType="java.lang.Integer">
        select
            count(*)
        from rv_performance_grade
        where org_id = #{orgId}
        and grade_value =#{gradeValue}
    </select>
    <select id="findGradeByOrgIdAndLevel"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_grade
        where org_id = #{orgId}
        and deleted = 0
        and grade_value in
        <foreach collection="gradeValues" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="deleteByOrgId">
        update rv_performance_grade set deleted = 1 where org_id = #{orgId}
    </update>

</mapper>