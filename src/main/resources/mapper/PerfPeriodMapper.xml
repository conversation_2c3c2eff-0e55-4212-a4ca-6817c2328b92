<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO">
        <!--@mbg.generated-->
        <!--@Table rv_performance_period-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="period_name" property="periodName"/>
        <result column="order_index" property="orderIndex"/>
        <result column="update_time" property="updateTime"/>
        <result column="cycle" property="cycle"/>
        <result column="period" property="period"/>
        <result column="yearly" property="yearly"/>
        <result column="deleted" property="deleted"/>
        <result column="score_total" property="scoreTotal"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , create_user_id
             , create_time
             , update_user_id
             , period_name
             , order_index
             , update_time
             , cycle
             , period
             , yearly
             , deleted
             , score_total
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_performance_period
        where id = #{id}
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO">
        <!--@mbg.generated-->
        insert into rv_performance_period
            (id,
             org_id,
             create_user_id,
             create_time,
             update_user_id,
             period_name,
             order_index,
             update_time,
             cycle,
             period,
             yearly,
             deleted,
             score_total)
        values
            (#{id},
             #{orgId},
             #{createUserId},
             #{createTime},
             #{updateUserId},
             #{periodName},
             #{orderIndex},
             #{updateTime},
             #{cycle},
             #{period},
             #{yearly},
             #{deleted},
             #{scoreTotal})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO">
      <!--@mbg.generated-->
      insert into rv_performance_period
        (id,
         org_id,
         create_user_id,
         create_time,
         update_user_id,
         period_name,
         order_index,
         update_time,
         cycle,
         period,
         yearly,
         deleted,
         score_total)
      values
        (#{id},
         #{orgId},
         #{createUserId},
         #{createTime},
         #{updateUserId},
         #{periodName},
         #{orderIndex},
         #{updateTime},
         #{cycle},
         #{period},
         #{yearly},
         #{deleted},
         #{scoreTotal})
      on duplicate key update
      <trim suffixOverrides=",">
        id             = #{id},
        org_id         = #{orgId},
        create_user_id = #{createUserId},
        create_time    = #{createTime},
        update_user_id = #{updateUserId},
        period_name    = #{periodName},
        order_index    = #{orderIndex},
        update_time    = #{updateTime},
        cycle          = #{cycle},
        period         = #{period},
        yearly         = #{yearly},
        deleted        = #{deleted},
        score_total    = #{scoreTotal}
      </trim>
    </insert>

  <insert id="batchInsertOrUpdate">
    insert into rv_performance_period
      (id,
       org_id,
       create_user_id,
       create_time,
       update_user_id,
       period_name,
       order_index,
       update_time,
       cycle,
       period,
       yearly,
       score_total,
       deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id},
       #{item.orgId},
       #{item.createUserId},
       #{item.createTime},
       #{item.updateUserId},
       #{item.periodName},
       #{item.orderIndex},
       #{item.updateTime},
       #{item.cycle},
       #{item.period},
       #{item.yearly},
       #{item.scoreTotal},
       #{item.deleted})
    </foreach>
    on duplicate key update
    <trim suffixOverrides=",">
      org_id         = values(org_id),
      period_name    = values(period_name),
      order_index    = values(order_index),
      cycle          = values(cycle),
      period         = values(period),
      yearly         = values(yearly),
      score_total    = values(score_total),
      deleted        = values(deleted),
      update_user_id = values(update_user_id),
      update_time    = values(update_time)
    </trim>
  </insert>

  <select id="currentMaxSort" resultType="int">
        select ifnull(max(order_index), 0) from rv_performance_period where org_id = #{orgId}
    </select>

    <select id="selectByOrgIdAndId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_period
        where id = #{id}
          and org_id = #{orgId}
          and deleted = 0
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_period
        where org_id = #{orgId}
          and deleted = 0
        order by order_index
    </select>

    <select id="countByOrgId" resultType="long">
        select count(1) from rv_performance_period where org_id = #{orgId} and deleted = 0
    </select>

    <select id="selectByOrgIdAndPeriodNameAndIdNeIfHas" resultType="long">
        select count(1)
        from rv_performance_period
        where org_id = #{orgId}
          and period_name = #{periodName}
          and deleted = 0
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <select id="selectByOrgIdAndOrderIndexBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_period
        where org_id = #{orgId}
          and order_index between #{start} and #{end}
          and deleted = 0
        order by order_index
    </select>

    <select id="selectByOrgIdAndIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance_period
        where org_id = #{orgId}
          and deleted = 0
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach close=")" collection="ids" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        order by order_index
    </select>

  <select id="selectByOrgIdAndIdsIncludeDeleted" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_performance_period
    where org_id = #{orgId}
    <choose>
      <when test="ids != null and ids.size() != 0">
        and id in
        <foreach close=")" collection="ids" index="index" item="item" open="("
                 separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
    order by order_index
  </select>

  <select id="selectByOrgIdAndNames" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_performance_period
    where org_id = #{orgId}
    and deleted = 0
    <choose>
      <when test="names != null and names.size() != 0">
        and period_name in
        <foreach close=")" collection="names" index="index" item="item" open="("
                 separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
    order by order_index
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_performance_period
    where org_id = #{orgId}
      and deleted = 0
    <choose>
      <when test="ids != null and ids.size() != 0">
        and id in
        <foreach close=")" collection="ids" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
    order by order_index
  </select>
</mapper>