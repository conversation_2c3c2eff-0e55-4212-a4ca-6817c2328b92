<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.reward.RewardPunishmentHistoryMapper">
  <resultMap id="BaseResultMap"
             type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.RewardPunishmentHistoryPO">
    <!--@mbg.generated-->
    <!--@Table rv_reward_punishment_history-->
    <id column="id" property="id"/>
    <result column="third_user_id" property="thirdUserId"/>
    <result column="org_id" property="orgId"/>
    <result column="rp_type" property="rpType"/>
    <result column="rp_name" property="rpName"/>
    <result column="acq_time" property="acqTime"/>
    <result column="pub_from" property="pubFrom"/>
    <result column="deleted" property="deleted"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="user_id" property="userId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id
         , third_user_id
         , org_id
         , rp_type
         , rp_name
         , acq_time
         , pub_from
         , deleted
         , create_time
         , update_time
         , user_id
  </sql>
  <insert id="batchInsertOrUpdate">
    <!--@mbg.generated-->
    insert into rv_reward_punishment_history
      (id, third_user_id, org_id, rp_type, rp_name, acq_time, pub_from, deleted, create_time, update_time, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id},
       #{item.thirdUserId},
       #{item.orgId},
       #{item.rpType},
       #{item.rpName},
       #{item.acqTime},
       #{item.pubFrom},
       #{item.deleted},
       #{item.createTime},
       #{item.updateTime},
       #{item.userId})
    </foreach>
    on duplicate key update
    <trim suffixOverrides=",">
      third_user_id = values(third_user_id),
      org_id        = values(org_id),
      rp_type       = values(rp_type),
      rp_name       = values(rp_name),
      acq_time      = values(acq_time),
      pub_from      = values(pub_from),
      deleted       = values(deleted),
      update_time   = values(update_time),
      user_id       = values(user_id)
    </trim>
  </insert>

  <select id="selectByThirdUserIds" resultType="java.lang.String">
    select distinct third_user_id from rv_reward_punishment_history
    where org_id = #{orgId}
    <choose>
      <when test="thirdUserIds != null and thirdUserIds.size() != 0">
        and third_user_id in
        <foreach collection="thirdUserIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>
  <select id="selectByOrgIdAndThirdUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_reward_punishment_history
    where org_id = #{orgId,jdbcType=VARCHAR}
      and third_user_id = #{thirdUserId,jdbcType=VARCHAR}
      and deleted = 0
  </select>
  <update id="deleteByIds">
    update rv_reward_punishment_history
    set deleted     = 1,
        update_time = now()
    where org_id = #{orgId}
      and id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and deleted = 0
  </update>
  <select id="selectByOrgIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_reward_punishment_history
    where org_id = #{orgId,jdbcType=VARCHAR}
      and user_id = #{userId,jdbcType=VARCHAR}
      and deleted = 0
  </select>

  <update id="deleteByUserId">
    update rv_reward_punishment_history
    set deleted     = 1,
        update_time = now()
    where org_id = #{orgId}
      and deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_reward_punishment_history
    where org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
  </select>
</mapper>