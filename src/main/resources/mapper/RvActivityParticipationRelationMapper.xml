<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationRelationMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationRelationPO">
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="actv_id" property="actvId"/>
    <result column="participation_id" property="participationId"/>
    <result column="user_id" property="userId"/>
    <result column="relation_type" property="relationType"/>
    <result column="order_index" property="orderIndex"/>
    <result column="target_id" property="targetId"/>
    <result column="deleted" property="deleted"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="create_time" property="createTime"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="update_time" property="updateTime"/>
    <result column="fullname" property="fullname"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, org_id, actv_id, participation_id, user_id, relation_type, order_index, target_id, deleted,
    create_user_id, create_time, update_user_id, update_time
  </sql>

  <select id="findManagersByActvId" resultMap="BaseResultMap">
    select r.*, u.fullname
    from rv_activity_participation_relation r
    left join udp_lite_user u on r.user_id = u.id and u.org_id = r.org_id
    where r.org_id = #{orgId}
    and r.actv_id = #{actvId}
    and r.relation_type = 0
    and r.deleted = 0
    order by r.order_index
  </select>

  <select id="findManagersByActvIds" resultMap="BaseResultMap">
    select r.*, u.fullname
    from rv_activity_participation_relation r
    left join udp_lite_user u on r.user_id = u.id and u.org_id = r.org_id
    where r.org_id = #{orgId}
    and r.actv_id in
    <foreach collection="actvIds" item="actvId" open="(" separator="," close=")">
      #{actvId}
    </foreach>
    and r.relation_type = 0
    and r.deleted = 0
    order by r.actv_id, r.order_index
  </select>
</mapper>
