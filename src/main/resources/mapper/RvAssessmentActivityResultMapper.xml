<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvAssessmentActivityResultMapper">
  <resultMap id="BaseResultMap"
             type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvAssessmentActivityResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_assessment_activity_result-->
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="actv_id" property="actvId"/>
    <result column="user_id" property="userId"/>
    <result column="base_actv_result_id" property="baseActvResultId"/>
    <result column="result_repeat_flag" property="resultRepeatFlag"/>
    <result column="repeat_count" property="repeatCount"/>
    <result column="sub_task_result_id" property="subTaskResultId"/>
    <result column="passed" property="passed"/>
    <result column="target_status" property="targetStatus"/>
    <result column="score" property="score"/>
    <result column="total_score" property="totalScore"/>
    <result column="deleted" property="deleted"/>
    <result column="create_time" property="createTime"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="update_time" property="updateTime"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="db_archived" property="dbArchived"/>
    <result column="ext" property="ext"/>
    <result column="item_id" property="itemId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id
         , org_id
         , actv_id
         , user_id
         , base_actv_result_id
         , result_repeat_flag
         , repeat_count
         , sub_task_result_id
         , passed
         , target_status
         , score
         , total_score
         , deleted
         , create_time
         , create_user_id
         , update_time
         , update_user_id
         , db_archived
         , ext
         , item_id
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_assessment_activity_result
      (id,
       org_id,
       actv_id,
       user_id,
       base_actv_result_id,
       result_repeat_flag,
       repeat_count,
       sub_task_result_id,
       passed,
       target_status,
       score,
       total_score,
       deleted,
       create_time,
       create_user_id,
       update_time,
       update_user_id,
       db_archived,
       ext,
       item_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id},
       #{item.orgId},
       #{item.actvId},
       #{item.userId},
       #{item.baseActvResultId},
       #{item.resultRepeatFlag},
       #{item.repeatCount},
       #{item.subTaskResultId},
       #{item.passed},
       #{item.targetStatus},
       #{item.score},
       #{item.totalScore},
       #{item.deleted},
       #{item.createTime},
       #{item.createUserId},
       #{item.updateTime},
       #{item.updateUserId},
       #{item.dbArchived},
       #{item.ext},
       #{item.itemId})
    </foreach>
    on duplicate key update id=values(id),
                            org_id=values(org_id),
                            actv_id=values(actv_id),
                            user_id=values(user_id),
                            base_actv_result_id=values(base_actv_result_id),
                            result_repeat_flag=values(result_repeat_flag),
                            repeat_count=values(repeat_count),
                            sub_task_result_id=values(sub_task_result_id),
                            passed=values(passed),
                            target_status=values(target_status),
                            score=values(score),
                            total_score=values(total_score),
                            deleted=values(deleted),
                            create_time=values(create_time),
                            create_user_id=values(create_user_id),
                            update_time=values(update_time),
                            update_user_id=values(update_user_id),
                            db_archived=values(db_archived),
                            ext=values(ext),
                            item_id=values(item_id)
  </insert>

  <select id="selectByActvRefIdAndUserId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from rv_assessment_activity_result a
    where a.actv_id = #{actvId}
      and a.user_id = #{userId}
      and a.org_id = #{orgId}
      and a.deleted = 0
      and exists(
        select 1
        from rv_base_activity_result  b
        join rv_activity_arrange_item c on c.id = b.item_id and c.deleted = 0 and c.ref_id = #{actvRefId} and c.org_id = #{orgId}
        where b.id = a.base_actv_result_id
          and b.deleted = 0
          and b.org_id = a.org_id
          and b.actv_id = a.actv_id
      )
  </select>
</mapper>