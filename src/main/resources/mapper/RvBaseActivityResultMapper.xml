<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvBaseActivityResultMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.BaseActivityResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_base_activity_result-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="actv_id" property="actvId" />
    <result column="user_id" property="userId" />
    <result column="item_id" property="itemId" />
    <result column="ref_reg_id" property="refRegId" />
    <result column="required" property="required" />
    <result column="result_status" property="resultStatus" />
    <result column="start_time" property="startTime" />
    <result column="completed_time" property="completedTime" />
    <result column="last_study_time" property="lastStudyTime" />
    <result column="hand_completed" property="handCompleted" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="actv_type" property="actvType" />
    <result column="sub_type" property="subType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, actv_id, user_id, item_id, ref_reg_id, required, result_status, start_time, 
    completed_time, last_study_time, hand_completed, deleted, create_time, create_user_id, 
    update_time, update_user_id, actv_type, sub_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_base_activity_result
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.BaseActivityResultPO">
    <!--@mbg.generated-->
    insert into rv_base_activity_result (id, org_id, actv_id, user_id, item_id, ref_reg_id, required, 
      result_status, start_time, completed_time, last_study_time, hand_completed, 
      deleted, create_time, create_user_id, update_time, update_user_id, actv_type, 
      sub_type)
    values (#{id}, #{orgId}, #{actvId}, #{userId}, #{itemId}, #{refRegId}, #{required}, 
      #{resultStatus}, #{startTime}, #{completedTime}, #{lastStudyTime}, #{handCompleted}, 
      #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId}, #{actvType}, 
      #{subType})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.BaseActivityResultPO">
    <!--@mbg.generated-->
    update rv_base_activity_result
    set org_id = #{orgId},
      actv_id = #{actvId},
      user_id = #{userId},
      item_id = #{itemId},
      ref_reg_id = #{refRegId},
      required = #{required},
      result_status = #{resultStatus},
      start_time = #{startTime},
      completed_time = #{completedTime},
      last_study_time = #{lastStudyTime},
      hand_completed = #{handCompleted},
      deleted = #{deleted},
      create_time = #{createTime},
      create_user_id = #{createUserId},
      update_time = #{updateTime},
      update_user_id = #{updateUserId},
      actv_type = #{actvType},
      sub_type = #{subType}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_base_activity_result
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="actv_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.actvId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="item_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.itemId}
        </foreach>
      </trim>
      <trim prefix="ref_reg_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.refRegId}
        </foreach>
      </trim>
      <trim prefix="required = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.required}
        </foreach>
      </trim>
      <trim prefix="result_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultStatus}
        </foreach>
      </trim>
      <trim prefix="start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.startTime}
        </foreach>
      </trim>
      <trim prefix="completed_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.completedTime}
        </foreach>
      </trim>
      <trim prefix="last_study_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.lastStudyTime}
        </foreach>
      </trim>
      <trim prefix="hand_completed = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.handCompleted}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="actv_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.actvType}
        </foreach>
      </trim>
      <trim prefix="sub_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.subType}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_base_activity_result
    (id, org_id, actv_id, user_id, item_id, ref_reg_id, required, result_status, start_time, 
      completed_time, last_study_time, hand_completed, deleted, create_time, create_user_id, 
      update_time, update_user_id, actv_type, sub_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.actvId}, #{item.userId}, #{item.itemId}, #{item.refRegId}, 
        #{item.required}, #{item.resultStatus}, #{item.startTime}, #{item.completedTime}, 
        #{item.lastStudyTime}, #{item.handCompleted}, #{item.deleted}, #{item.createTime}, 
        #{item.createUserId}, #{item.updateTime}, #{item.updateUserId}, #{item.actvType}, 
        #{item.subType})
    </foreach>
  </insert>

  <select id="doneUserIdsByItemId" resultType="string">
    select user_id from rv_base_activity_result
    where org_id = #{orgId} and actv_id = #{actvId} and item_id = #{itemId}
    and deleted = 0 and result_status = 2
    and user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="doneRefIdsByUserId" resultType="com.yxt.talent.rv.application.xpd.common.dto.AomUserRefIdDto">
    select bar.user_id,ai.ref_id from rv_base_activity_result bar
    join rv_activity_arrange_item ai on ai.id = bar.item_id and ai.deleted = 0
    where bar.org_id = #{orgId} and bar.actv_id = #{actvId}
    and bar.deleted = 0 and bar.result_status = 2
    and bar.user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and ai.ref_id in
    <foreach collection="refIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="doneUserIds" resultType="string">
    select user_id
    from rv_base_activity_result
    where org_id = #{orgId}
      and actv_id = #{actvId}
      and deleted = 0
      and result_status = 2
      and actv_type = #{actvType}
      and user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>