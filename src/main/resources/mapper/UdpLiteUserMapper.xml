<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
        <!--@mbg.generated-->
        <!--@Table udp_lite_user_sp-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="fullname" jdbcType="VARCHAR" property="fullname"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="user_no" jdbcType="VARCHAR" property="userNo"/>
        <result column="third_user_id" jdbcType="VARCHAR" property="thirdUserId"/>
        <result column="sex" jdbcType="TINYINT" property="sex"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="dept_id" jdbcType="CHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="manager_id" jdbcType="CHAR" property="managerId"/>
        <result column="manager_fullname" jdbcType="VARCHAR" property="managerFullname"/>
        <result column="grade_id" jdbcType="CHAR" property="gradeId"/>
        <result column="position_id" jdbcType="CHAR" property="positionId"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName"/>
        <result column="dept_manager_id" jdbcType="CHAR" property="deptManagerId"/>
        <result column="dept_manager_fullname" jdbcType="VARCHAR" property="deptManagerFullname"/>
        <result column="mobile_validated" jdbcType="TINYINT" property="mobileValidated"/>
        <result column="email_validated" jdbcType="TINYINT" property="emailValidated"/>
        <result column="locale" jdbcType="VARCHAR" property="locale"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="timezone" jdbcType="VARCHAR" property="timezone"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="hire_date" jdbcType="TIMESTAMP" property="hireDate"/>
        <result column="area_code" jdbcType="VARCHAR" property="areaCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , username
             , fullname
             , img_url
             , email
             , mobile
             , user_no
             , third_user_id
             , sex
             , `status`
             , dept_id
             , dept_name
             , manager_id
             , manager_fullname
             , grade_id
             , position_id
             , position_name
             , grade_name
             , dept_manager_id
             , dept_manager_fullname
             , mobile_validated
             , email_validated
             , `locale`
             , create_time
             , update_time
             , deleted
             , timezone
             , region
             , hire_date
             , area_code
    </sql>

    <sql id="Base_Column_List2">
        <!--@sql select -->
               a.id
             , a.org_id
             , a.username
             , a.fullname
             , a.img_url
             , a.email
             , a.mobile
             , a.user_no
             , a.third_user_id
             , a.sex
             , a.`status`
             , a.dept_id
             , a.dept_name
             , a.manager_id
             , a.manager_fullname
             , a.grade_id
             , a.position_id
             , a.position_name
             , a.grade_name
             , a.dept_manager_id
             , a.dept_manager_fullname
             , a.mobile_validated
             , a.email_validated
             , a.`locale`
             , a.create_time
             , a.update_time
             , a.deleted
             , a.timezone
             , a.region
             , a.hire_date
             , a.area_code
             , ud.id_full_path as deptidfullpath
        <!--@sql from udp_lite_user_sp a join udp_dept         ud -->
    </sql>

    <select id="selectByUserId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
        select
        <include refid="Base_Column_List2"/>
        from udp_lite_user_sp a
        join udp_dept         ud on a.dept_id = ud.id
        where a.id = #{userId}
          and a.deleted = 0
          and a.org_id = #{orgId}
    </select>

    <select id="selectByUserIdWithDeleted"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
        select
        <include refid="Base_Column_List2"/>
        from udp_lite_user_sp a
        join udp_dept         ud on a.dept_id = ud.id
        where a.id = #{userId}
          and a.org_id = #{orgId}
    </select>

    <select id="selectByUserNames" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
      select
      <include refid="Base_Column_List"/>
      from udp_lite_user_sp a
      where a.org_id = #{orgId}
        and a.deleted = 0
      <choose>
        <when test="usernames != null and usernames.size() != 0">
          and a.username in
          <foreach collection="usernames" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </select>

    <select id="selectByUserNamesIncludeDeleted" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
      select
      <include refid="Base_Column_List"/>
      from udp_lite_user_sp a
      where a.org_id = #{orgId}
      <choose>
        <when test="usernames != null and usernames.size() != 0">
          and a.username in
          <foreach collection="usernames" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </select>

    <select id="selectByPosIdAndUserIds"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
      select
      <include refid="Base_Column_List"/>
      from udp_lite_user_sp a
      where a.org_id = #{orgId}
        and a.deleted = 0
        and a.status = 1
      <choose>
        <when test="list != null and list.size() != 0">
          and a.id in
          <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </select>

    <select id="selectByUserIds"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="countByPosIdAndGradeId" resultType="long">
        select count(1)
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
          and a.position_id = #{positionId}
        <if test="gradeIdList != null and gradeIdList.size() != 0">
            and a.grade_id in
            <foreach collection="gradeIdList" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByThirdUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
        <choose>
            <when test="thirdUserIds != null and thirdUserIds.size() != 0">
                and third_user_id in
                <foreach collection="thirdUserIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

  <select id="selectByThirdUserIdsAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from udp_lite_user_sp a
    where a.org_id = #{orgId}
    and a.deleted = 0
    <choose>
      <when test="thirdUserIds != null and thirdUserIds.size() != 0">
        and third_user_id in
        <foreach collection="thirdUserIds" item="item" index="index" open="(" separator=","
                 close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

    <select id="listByOrgId" resultType="com.yxt.talent.rv.application.user.dto.UdpUserSimpleDTO">
        select id, username as objname, status as status
        from udp_lite_user_sp
        where org_id = #{orgId}
          and deleted = 0
    </select>

    <select id="selectTeamPrjUserResults"
            resultType="com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamPrjUserResultClientVO">
        select ru.id            as userid
             , ru.fullname      as fullname
             , ru.username      as username
             , ru.status        as status
             , ru.dept_id       as deptid
             , ru.dept_name     as deptname
             , ru.position_id   as positionid
             , ru.position_name as positionname
        from udp_lite_user_sp ru
        <!--perf：只查看盘点项目下的人员，不查询我的团队下管辖的所有人-->
        join (
            select distinct b.user_id from rv_prj_user b
            join rv_project c on c.id = b.project_id and c.org_id = b.org_id
            where c.org_id = #{orgId}
        ) t on t.user_id = ru.id
        where ru.org_id = #{orgId}
          and ru.deleted = 0
        <if test="search.userStatus != null and search.userStatus != -1">
            and ru.status = #{search.userStatus}
        </if>
        <if test="search.admin == null or search.admin == 0">
            <!--如果是admin,可以查询所有,如果不是管理员,才考虑数据权限, 查询满足部门 ID 列表或用户 ID 列表中任一条件的记录-->
            <choose>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()
                and search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and (
                    ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    or ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()">
                    and ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    and 1 != 1
                </otherwise>
            </choose>
        </if>
        <if test="(qwUserIds != null and qwUserIds.size() != 0) or (search.keyword != null and search.keyword != '')">
            and(
            1 = 0
            <if test="qwUserIds != null and qwUserIds.size() != 0">
                or ru.id in
                <foreach collection="qwUserIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="search.keyword != null and search.keyword != ''">
                or (ru.username like concat('%', #{search.escapedKeyword}, '%') escape
                    '\\' or ru.fullname like concat('%', #{search.escapedKeyword}, '%') escape '\\')
            </if>
            )
        </if>
    </select>

    <select id="selectTeamXpdUserResults" resultType="com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamPrjUserResultClientVO">
        select ru.id            as userid,
               ru.fullname      as fullname,
               ru.username      as username,
               ru.status        as status,
               ru.dept_id       as deptid,
               ru.dept_name     as deptname,
               ru.position_id   as positionid,
               ru.position_name as positionname
        from udp_lite_user_sp ru
                 inner join (
            select distinct m.user_id
            from rv_activity_participation_member m
                     inner join rv_xpd actv on actv.aom_prj_id = m.actv_id and m.org_id = actv.org_id
            where actv.org_id = #{orgId}
        ) t on t.user_id = ru.id
        where ru.org_id = #{orgId}
          and ru.deleted = 0
        <if test="search.userStatus != null and search.userStatus != -1">
            and ru.status = #{search.userStatus}
        </if>
        <if test="search.admin == null or search.admin == 0">
            <!--如果是admin,可以查询所有,如果不是管理员,才考虑数据权限, 查询满足部门 ID 列表或用户 ID 列表中任一条件的记录-->
            <choose>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()
                and search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and (
                        ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    or ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()">
                    and ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    and 1 != 1
                </otherwise>
            </choose>
        </if>
        <if test="(qwUserIds != null and qwUserIds.size() != 0) or (search.keyword != null and search.keyword != '')">
            and (1 = 0
            <if test="qwUserIds != null and qwUserIds.size() != 0">
                or ru.id in
                <foreach collection="qwUserIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="search.keyword != null and search.keyword != ''">
                or (ru.username like concat('%', #{search.escapedKeyword}, '%') escape
                    '\\' or ru.fullname like concat('%', #{search.escapedKeyword}, '%') escape '\\')
            </if>
            )
        </if>
    </select>

  <select id="selectPrjClientPage" resultType="com.yxt.talent.rv.controller.client.general.xpd.viewobj.XpdClientVO">
    select actv.id          as id,
           x.id             as xpdId,
           actv.actv_name   as projectname,
           actv.start_time  as starttime,
           actv.end_time    as endtime,
           actv.actv_status as projectstatus,
           count(actv.id)   as membercount
    from rv_activity actv
    join rv_activity_participation_member m on m.org_id = #{orgId} and m.actv_id = actv.id
    join udp_lite_user_sp user on user.org_id = #{orgId} and user.id = m.user_id
    join rv_xpd x on x.aom_prj_id = actv.id and x.org_id = #{orgId}
    where actv.org_id = #{orgId}
      and actv.deleted = 0
      and actv.actv_status in
    <foreach collection="prjStatusList" item="prjStatus" open="(" separator="," close=")">
      #{prjStatus}
    </foreach>
    <if test="search.projectName != null and search.projectName != ''">
      and actv.actv_name like concat('%', #{search.escapedProjectName}, '%') escape '\\'
    </if>
    <if test="search.admin == null or search.admin == 0">
      <!--如果是admin,可以查询所有,如果不是管理员,才考虑数据权限, 查询满足部门 ID 列表或用户 ID 列表中任一条件的记录-->
      <choose>
        <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()
        and search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
          and (
          user.dept_id in
          <foreach collection="search.scopeDeptIds" item="item" separator="," open="(" close=")">
            #{item}
          </foreach>
          or user.id in
          <foreach collection="search.scopeUserIds" item="item" separator="," open="(" close=")">
            #{item}
          </foreach>
          )
        </when>
        <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()">
          and user.dept_id in
          <foreach collection="search.scopeDeptIds" item="item" separator="," open="(" close=")">
            #{item}
          </foreach>
        </when>
        <when test="search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
          and user.id in
          <foreach collection="search.scopeUserIds" item="item" separator="," open="(" close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </if>
    group by actv.id, x.id, actv.create_time
    order by actv.create_time desc
  </select>

  <select id="selectUserSimple"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO">
        select a.id, a.username, a.fullname from udp_lite_user_sp a where a.id = #{id}
    </select>

    <select id="pageQuery"
            resultType="com.yxt.talent.rv.controller.manage.udp.viewobj.UdpUserFrontVO">
        select a.id
             , a.username
             , a.user_no       as userNo
             , a.fullname
             , a.dept_id       as deptId
             , a.dept_name     as deptName
             , a.position_id   as positionid
             , a.position_name as positionName
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
        <if test="(searchParam.searchKey != null and searchParam.searchKey != '') or (searchParam.userIds != null and searchParam.userIds.size() != 0)">
            and(
           <!--@ignoreSql-->
            1 = 0
            <if test="searchParam.userIds != null and searchParam.userIds.size() != 0">
                or a.id in
                <foreach collection="searchParam.userIds" item="id" open="(" separator=","
                         close=")">
                    #{id}
                </foreach>
            </if>

            <if test="searchParam.searchKey != null and searchParam.searchKey != ''">
                or ((a.username like concat('%', #{searchParam.escapedSearchKey}, '%') escape
                     '\\') or
                    (a.fullname like concat('%', #{searchParam.escapedSearchKey}, '%') escape '\\'))
            </if>
            )
        </if>
        <choose>
            <when test="posInfos != null and posInfos.size() != 0">
                and a.id in (select b.id
                             from udp_lite_user_sp b where b.org_id = #{orgId}
                                                       and
                <foreach collection="posInfos" item="param" open="(" close=")" separator="or">
                    (
                    b.position_id = #{param.positionId}
                    <if test="param.gradeList != null and param.gradeList.size() > 0">
                        and b.grade_id in
                        <foreach collection="param.gradeList" item="gradeId" open="(" close=")"
                                 separator=",">
                            #{gradeId}
                        </foreach>
                    </if>
                    )
                </foreach>
                )
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        <!--<if test="searchParam.positionIds != null and searchParam.positionIds.size() > 0">
            and a.position_id in
            <foreach collection="searchParam.positionIds" item="positionId" open="(" close=")" separator=",">
                #{positionId}
            </foreach>
        </if>
        <if test="searchParam.gradeIds != null and searchParam.gradeIds.size() > 0">
            and a.grade_id in
            <foreach collection="searchParam.gradeIds" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>-->
    </select>

    <select id="countUserByPosIdsAndGradeIds" resultType="java.lang.Integer">
        select count(1)
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
        <if test="positionIds != null and positionIds.size() != 0">
            and a.position_id in
            <foreach collection="positionIds" item="positionId" index="index" open="(" separator=","
                     close=")">
                #{positionId}
            </foreach>
        </if>
        <if test="gradeIdList != null and gradeIdList.size() != 0">
            and a.grade_id in
            <foreach collection="gradeIdList" item="item" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByThirdUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.third_user_id = #{thirdUserId}
    </select>

    <select id="selectActiveUsers" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List2"/>
        from udp_lite_user_sp a
        join udp_dept         ud on a.dept_id = ud.id
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
        <choose>
            <when test="ids != null and ids.size() != 0">
                and a.id in
                <foreach collection="ids" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectActiveUsersByUserNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.status = 1
        <choose>
            <when test="userNames != null and userNames.size() != 0">
                and a.username in
                <foreach collection="userNames" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="countByOrgId" resultType="long">
        select count(1) from udp_lite_user_sp a where a.org_id = #{orgId} and a.deleted = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.id = #{id}
    </select>

    <select id="selectByUserIdsIncludeDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from udp_lite_user_sp a
        where a.org_id = #{orgId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>
</mapper>
