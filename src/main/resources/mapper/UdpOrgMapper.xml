<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpOrgMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpOrgPO">
        <!--@mbg.generated-->
        <!--@Table udp_org-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="org_type" jdbcType="TINYINT" property="orgType"/>
        <result column="logo_url" jdbcType="VARCHAR" property="logoUrl"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="domain" jdbcType="VARCHAR" property="domain"/>
        <result column="site_name" jdbcType="VARCHAR" property="siteName"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="official_org" jdbcType="TINYINT" property="officialOrg"/>
        <result column="templated_org" jdbcType="TINYINT" property="templatedOrg"/>
        <result column="reserved_org" jdbcType="TINYINT" property="reservedOrg"/>
        <result column="sales_org" jdbcType="TINYINT" property="salesOrg"/>
        <result column="sales_org_template" jdbcType="TINYINT" property="salesOrgTemplate"/>
        <result column="sales_org_template_id" jdbcType="CHAR" property="salesOrgTemplateId"/>
        <result column="edition_code" jdbcType="CHAR" property="editionCode"/>
        <result column="source_code" jdbcType="CHAR" property="sourceCode"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="org_status" jdbcType="TINYINT" property="orgStatus"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="name_i18n" jdbcType="VARCHAR" property="nameI18n"/>
        <result column="site_name_i18n" jdbcType="VARCHAR" property="siteNameI18n"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
          , `name`
          , code
          , org_type
          , logo_url
          , description
          , `domain`
          , site_name
          , start_date
          , end_date
          , official_org
          , templated_org
          , reserved_org
          , sales_org
          , sales_org_template
          , sales_org_template_id
          , edition_code
          , source_code
          , deleted
          , create_user_id
          , create_time
          , update_user_id
          , update_time
          , org_status
          , icon_url
          , name_i18n
          , site_name_i18n
          , org_name
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from udp_org
        where
            id = #{id,jdbcType=CHAR}
    </select>
</mapper>
