<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpPosGradeMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpPosGradePO">
        <!--@mbg.generated-->
        <!--@Table udp_position_grade-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="third_id" jdbcType="VARCHAR" property="thirdId"/>
        <result column="order_index" jdbcType="INTEGER" property="orderIndex"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="corp_refer_id" jdbcType="CHAR" property="corpReferId"/>
        <result column="third_sync_time" jdbcType="TIMESTAMP" property="thirdSyncTime"/>
        <result column="mgt_sync_time" jdbcType="TIMESTAMP" property="mgtSyncTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
          , org_id
          , name
          , third_id
          , order_index
          , create_user_id
          , create_time
          , update_user_id
          , update_time
          , deleted
          , corp_refer_id
          , third_sync_time
          , mgt_sync_time
    </sql>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from udp_position_grade
        where
              id = #{id,jdbcType=CHAR}
          and org_id = #{orgId,jdbcType=CHAR}
    </select>
</mapper>
