<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpPosMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpPosPO">
        <!--@mbg.generated-->
        <!--@Table udp_position-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="catalog_id" jdbcType="CHAR" property="catalogId"/>
        <result column="grade_id" jdbcType="CHAR" property="gradeId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="third_id" jdbcType="VARCHAR" property="thirdId"/>
        <result column="order_index" jdbcType="INTEGER" property="orderIndex"/>
        <result column="key_position" jdbcType="TINYINT" property="keyPosition"/>
        <result column="responsibility" jdbcType="LONGVARCHAR" property="responsibility"/>
        <result column="requirement" jdbcType="LONGVARCHAR" property="requirement"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="spare1" jdbcType="VARCHAR" property="spare1"/>
        <result column="spare2" jdbcType="VARCHAR" property="spare2"/>
        <result column="spare3" jdbcType="VARCHAR" property="spare3"/>
        <result column="corp_refer_id" jdbcType="CHAR" property="corpReferId"/>
        <result column="third_sync_time" jdbcType="TIMESTAMP" property="thirdSyncTime"/>
        <result column="mgt_sync_time" jdbcType="TIMESTAMP" property="mgtSyncTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
          , org_id
          , catalog_id
          , grade_id
          , name
          , code
          , description
          , third_id
          , order_index
          , key_position
          , responsibility
          , requirement
          , create_user_id
          , create_time
          , update_user_id
          , update_time
          , deleted
          , spare1
          , spare2
          , spare3
          , corp_refer_id
          , third_sync_time
          , mgt_sync_time
    </sql>

    <select id="selectByOrgIdAndId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from udp_position
        where
              id = #{id,jdbcType=CHAR}
          and org_id = #{orgId,jdbcType=CHAR}
    </select>

    <select id="selectByOrgIdAndIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from udp_position
        where
         id in
        <foreach collection="ids" item="item" index="index" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
        and org_id = #{orgId,jdbcType=CHAR}
    </select>
</mapper>
