<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpPosUserMapMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpPosUserMapPO">
        <!--@mbg.generated-->
        <!--@Table udp_position_user_map-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="position_id" jdbcType="CHAR" property="positionId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="parttime" jdbcType="TINYINT" property="parttime"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="um_status" jdbcType="TINYINT" property="umStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
          , org_id
          , position_id
          , user_id
          , parttime
          , create_user_id
          , create_time
          , update_user_id
          , update_time
          , um_status
    </sql>
</mapper>
