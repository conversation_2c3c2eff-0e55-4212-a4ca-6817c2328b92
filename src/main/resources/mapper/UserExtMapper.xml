<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.UserExtMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserExtPO">
        <!--@mbg.generated-->
        <!--@Table rv_user_ext-->
        <id column="id" property="id"/>
        <result column="third_user_id" property="thirdUserId"/>
        <result column="org_id" property="orgId"/>
        <result column="enabled" property="enabled"/>
        <result column="user_id" property="userId"/>
        <result column="manager" property="manager"/>
        <result column="key_position" property="keyPosition"/>
        <result column="grade_level" property="gradeLevel"/>
        <result column="residence_address" property="residenceAddress"/>
        <result column="prof_certs" property="profCerts"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , third_user_id
             , org_id
             , enabled
             , user_id
             , manager
             , key_position
             , grade_level
             , residence_address
             , prof_certs
             , deleted
             , create_time
             , update_time
    </sql>
    <insert id="batchInsertOrUpdate">
        <!--@mbg.generated-->
        insert into rv_user_ext
            (id,
             third_user_id,
             org_id,
             enabled,
             user_id,
             manager,
             key_position,
             grade_level,
             residence_address,
             prof_certs,
             deleted,
             create_time,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.thirdUserId},
             #{item.orgId},
             #{item.enabled},
             #{item.userId},
             #{item.manager},
             #{item.keyPosition},
             #{item.gradeLevel},
             #{item.residenceAddress},
             #{item.profCerts},
             #{item.deleted},
             #{item.createTime},
             #{item.updateTime})
        </foreach>
        on duplicate key update
        <trim prefix="" suffix="" suffixOverrides=",">
            third_user_id     = values(third_user_id),
            org_id            = values(org_id),
            enabled           = values(enabled),
            user_id           = values(user_id),
            manager           = values(manager),
            key_position      = values(key_position),
            grade_level       = values(grade_level),
            residence_address = values(residence_address),
            prof_certs        = values(prof_certs),
            deleted           = values(deleted),
            update_time       = values(update_time)
        </trim>
    </insert>

    <select id="selectByThirdUserIds" resultType="java.lang.String">
        select distinct a.third_user_id
        from rv_user_ext a
        where org_id = #{orgId}
        <choose>
            <when test="thirdUserIds != null and thirdUserIds.size() != 0">
                and third_user_id in
                <foreach collection="thirdUserIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>
    <select id="selectByThirdUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_user_ext
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        AND third_user_id = #{thirdUserId,jdbcType=VARCHAR}
    </select>
  <select id="selectByOrgIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_user_ext
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    AND user_id = #{userId,jdbcType=VARCHAR}
  </select>

  <select id="selectByAndUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_user_ext
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    AND user_id in
    <foreach collection="userIds" item="item" index="index" open="(" separator=","
             close=")">
      #{item}
    </foreach>
    and deleted = 0
  </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from rv_user_ext
      where org_id = #{orgId}
        and deleted = 0
    </select>
</mapper>