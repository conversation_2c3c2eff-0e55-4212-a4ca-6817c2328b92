<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.bizmgr.UserFocusMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        <!--@mbg.generated-->
        <!--@Table rv_user_focus-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="target_id" jdbcType="CHAR" property="targetId"/>
        <result column="target_type" jdbcType="TINYINT" property="targetType"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , user_id
             , target_id
             , target_type
             , deleted
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        <!--@mbg.generated-->
        insert into rv_user_focus
            (id,
             org_id,
             user_id,
             target_id,
             target_type,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id,jdbcType=CHAR},
             #{orgId,jdbcType=CHAR},
             #{userId,jdbcType=CHAR},
             #{targetId,jdbcType=CHAR},
             #{targetType,jdbcType=TINYINT},
             #{deleted,jdbcType=TINYINT},
             #{createUserId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateUserId,jdbcType=CHAR},
             #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateById"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        <!--@mbg.generated-->
        update rv_user_focus
        set org_id         = #{orgId,jdbcType=CHAR},
            user_id        = #{userId,jdbcType=CHAR},
            target_id      = #{targetId,jdbcType=CHAR},
            target_type    = #{targetType,jdbcType=TINYINT},
            deleted        = #{deleted,jdbcType=TINYINT},
            create_user_id = #{createUserId,jdbcType=CHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_user_id = #{updateUserId,jdbcType=CHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        <!--@mbg.generated-->
        insert into rv_user_focus
            (id,
             org_id,
             user_id,
             target_id,
             target_type,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
            (#{id,jdbcType=CHAR},
             #{orgId,jdbcType=CHAR},
             #{userId,jdbcType=CHAR},
             #{targetId,jdbcType=CHAR},
             #{targetType,jdbcType=TINYINT},
             #{deleted,jdbcType=TINYINT},
             #{createUserId,jdbcType=CHAR},
             #{createTime,jdbcType=TIMESTAMP},
             #{updateUserId,jdbcType=CHAR},
             #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = #{orgId,jdbcType=CHAR},
            user_id        = #{userId,jdbcType=CHAR},
            target_id      = #{targetId,jdbcType=CHAR},
            target_type    = #{targetType,jdbcType=TINYINT},
            deleted        = #{deleted,jdbcType=TINYINT},
            update_user_id = #{updateUserId,jdbcType=CHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP}
        </trim>
    </insert>

    <select id="selectByOrgIdAndUserIdAndTargetId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_user_focus
        where org_id = #{orgId,jdbcType=CHAR}
          and user_id = #{userId,jdbcType=CHAR}
          and target_id = #{targetId,jdbcType=CHAR}
          and deleted = 0
    </select>

    <select id="selectByOrgIdAndUserIdAndTargetIds"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        select
        <include refid="Base_Column_List"/>
        from rv_user_focus a
        where a.org_id = #{orgId}
          and a.deleted = 0
          and a.user_id = #{userId}
        <choose>
            <when test="prjIds != null and prjIds.size() != 0">
                and a.target_id in
                <foreach collection="prjIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndUserId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        select
        <include refid="Base_Column_List"/>
        from rv_user_focus
        where org_id = #{orgId,jdbcType=CHAR}
          and user_id = #{userId,jdbcType=CHAR}
          and deleted = 0
    </select>

    <select id="selectByOrgIdAndId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO">
        select
        <include refid="Base_Column_List"/>
        from rv_user_focus
        where org_id = #{orgId,jdbcType=CHAR}
          and id = #{id,jdbcType=CHAR}
          and deleted = 0
    </select>
</mapper>
