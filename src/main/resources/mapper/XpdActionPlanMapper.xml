<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdActionPlanMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_action_plan-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="target_id" property="targetId" />
    <result column="target_type" property="targetType" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, target_id, target_type, deleted, create_user_id, create_time, 
    update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_action_plan
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO">
    <!--@mbg.generated-->
    insert into rv_xpd_action_plan (id, org_id, xpd_id, target_id, target_type, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{targetId}, #{targetType}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdActionPlanPO">
    <!--@mbg.generated-->
    update rv_xpd_action_plan
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      target_id = #{targetId},
      target_type = #{targetType},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_action_plan
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="target_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.targetId}
        </foreach>
      </trim>
      <trim prefix="target_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.targetType}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_action_plan
    (id, org_id, xpd_id, target_id, target_type, deleted, create_user_id, create_time, 
      update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.targetId}, #{item.targetType}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime})
    </foreach>
  </insert>

  <select id="selectByOrgIdAndXpdIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_action_plan a
        where a.deleted = 0
          and a.org_id = #{orgId}
          and a.xpd_id = #{xpdId}
          and a.target_type = #{targetType}
    </select>

    <select id="pagingByOrgIdAndXpdIdAndType"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_action_plan a
        where a.deleted = 0
          and a.org_id = #{orgId}
          and a.xpd_id = #{xpdId}
          and a.target_type = #{targetType}
        order by a.create_time
    </select>

  <select id="countByOrgIdAndXpdIdAndType" resultType="int">
    select count(1)
    from rv_xpd_action_plan a
    where a.deleted = 0
    and a.org_id = #{orgId}
    and a.target_id = #{trainingId}
    and a.target_type = #{targetType}
  </select>

  <select id="selectByTargetIdsAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_action_plan a
    where a.deleted = 0
    and a.org_id = #{orgId}
    and a.target_type = #{targetType}
    <choose>
      <when test="targetIds != null and targetIds.size() != 0">
        and a.target_id in
        <foreach collection="targetIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    <!-- 根据组织ID查询行动计划列表 -->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_action_plan a
    where org_id = #{orgId}
      and deleted = 0
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
  </select>

  <insert id="insertOrUpdate">
    insert into rv_xpd_action_plan
    (id, org_id, xpd_id, target_id, target_type, deleted, create_user_id, create_time,
      update_user_id, update_time)
    values
    (#{id}, #{orgId}, #{xpdId}, #{targetId}, #{targetType}, #{deleted}, #{createUserId}, #{createTime},
      #{updateUserId}, #{updateTime})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    target_id = #{targetId},
    target_type = #{targetType},
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime}
  </insert>
</mapper>