<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdActivityMemberStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    <!--@Table rv_activity_member_statistics-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="user_id" jdbcType="CHAR" property="userId" />
    <result column="actv_id" jdbcType="CHAR" property="actvId" />
    <result column="actv_completed_status" jdbcType="TINYINT" property="actvCompletedStatus" />
    <result column="actv_completed_rate" jdbcType="DECIMAL" property="actvCompletedRate" />
    <result column="actv_completed_time" jdbcType="TIMESTAMP" property="actvCompletedTime" />
    <result column="period_completed_count" jdbcType="TINYINT" property="periodCompletedCount" />
    <result column="earned_point" jdbcType="INTEGER" property="earnedPoint" />
    <result column="earned_credit" jdbcType="DECIMAL" property="earnedCredit" />
    <result column="first_study_time" jdbcType="TIMESTAMP" property="firstStudyTime" />
    <result column="last_study_time" jdbcType="TIMESTAMP" property="lastStudyTime" />
    <result column="all_task_count" jdbcType="INTEGER" property="allTaskCount" />
    <result column="all_task_completed_count" jdbcType="INTEGER" property="allTaskCompletedCount" />
    <result column="all_task_completed_rate" jdbcType="DECIMAL" property="allTaskCompletedRate" />
    <result column="required_task_count" jdbcType="INTEGER" property="requiredTaskCount" />
    <result column="required_task_completed_count" jdbcType="INTEGER" property="requiredTaskCompletedCount" />
    <result column="required_task_completed_rate" jdbcType="DECIMAL" property="requiredTaskCompletedRate" />
    <result column="ojt_task_count" jdbcType="INTEGER" property="ojtTaskCount" />
    <result column="ojt_task_completed_count" jdbcType="INTEGER" property="ojtTaskCompletedCount" />
    <result column="ojt_required_task_count" jdbcType="INTEGER" property="ojtRequiredTaskCount" />
    <result column="ojt_required_task_completed_count" jdbcType="INTEGER" property="ojtRequiredTaskCompletedCount" />
    <result column="ojt_required_task_completed_rate" jdbcType="DECIMAL" property="ojtRequiredTaskCompletedRate" />
    <result column="last_required_task_completed_time" jdbcType="TIMESTAMP" property="lastRequiredTaskCompletedTime" />
    <result column="last_all_task_completed_time" jdbcType="TIMESTAMP" property="lastAllTaskCompletedTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="db_archived" jdbcType="TINYINT" property="dbArchived" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, user_id, actv_id, actv_completed_status, actv_completed_rate, actv_completed_time, 
    period_completed_count, earned_point, earned_credit, first_study_time, last_study_time, 
    all_task_count, all_task_completed_count, all_task_completed_rate, required_task_count, 
    required_task_completed_count, required_task_completed_rate, ojt_task_count, ojt_task_completed_count, 
    ojt_required_task_count, ojt_required_task_completed_count, ojt_required_task_completed_rate, 
    last_required_task_completed_time, last_all_task_completed_time, deleted, create_time, 
    create_user_id, update_time, update_user_id, db_archived
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_member_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from rv_activity_member_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    insert into rv_activity_member_statistics (id, org_id, user_id, actv_id, 
      actv_completed_status, actv_completed_rate, 
      actv_completed_time, period_completed_count, 
      earned_point, earned_credit, first_study_time, 
      last_study_time, all_task_count, all_task_completed_count, 
      all_task_completed_rate, required_task_count, 
      required_task_completed_count, required_task_completed_rate, 
      ojt_task_count, ojt_task_completed_count, ojt_required_task_count, 
      ojt_required_task_completed_count, ojt_required_task_completed_rate, 
      last_required_task_completed_time, last_all_task_completed_time, 
      deleted, create_time, create_user_id, 
      update_time, update_user_id, db_archived)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{userId,jdbcType=CHAR}, #{actvId,jdbcType=CHAR}, 
      #{actvCompletedStatus,jdbcType=TINYINT}, #{actvCompletedRate,jdbcType=DECIMAL}, 
      #{actvCompletedTime,jdbcType=TIMESTAMP}, #{periodCompletedCount,jdbcType=TINYINT}, 
      #{earnedPoint,jdbcType=INTEGER}, #{earnedCredit,jdbcType=DECIMAL}, #{firstStudyTime,jdbcType=TIMESTAMP}, 
      #{lastStudyTime,jdbcType=TIMESTAMP}, #{allTaskCount,jdbcType=INTEGER}, #{allTaskCompletedCount,jdbcType=INTEGER}, 
      #{allTaskCompletedRate,jdbcType=DECIMAL}, #{requiredTaskCount,jdbcType=INTEGER}, 
      #{requiredTaskCompletedCount,jdbcType=INTEGER}, #{requiredTaskCompletedRate,jdbcType=DECIMAL}, 
      #{ojtTaskCount,jdbcType=INTEGER}, #{ojtTaskCompletedCount,jdbcType=INTEGER}, #{ojtRequiredTaskCount,jdbcType=INTEGER}, 
      #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER}, #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL}, 
      #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP}, #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, #{dbArchived,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    insert into rv_activity_member_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="userId != null and userId != ''">
        user_id,
      </if>
      <if test="actvId != null and actvId != ''">
        actv_id,
      </if>
      <if test="actvCompletedStatus != null">
        actv_completed_status,
      </if>
      <if test="actvCompletedRate != null">
        actv_completed_rate,
      </if>
      <if test="actvCompletedTime != null">
        actv_completed_time,
      </if>
      <if test="periodCompletedCount != null">
        period_completed_count,
      </if>
      <if test="earnedPoint != null">
        earned_point,
      </if>
      <if test="earnedCredit != null">
        earned_credit,
      </if>
      <if test="firstStudyTime != null">
        first_study_time,
      </if>
      <if test="lastStudyTime != null">
        last_study_time,
      </if>
      <if test="allTaskCount != null">
        all_task_count,
      </if>
      <if test="allTaskCompletedCount != null">
        all_task_completed_count,
      </if>
      <if test="allTaskCompletedRate != null">
        all_task_completed_rate,
      </if>
      <if test="requiredTaskCount != null">
        required_task_count,
      </if>
      <if test="requiredTaskCompletedCount != null">
        required_task_completed_count,
      </if>
      <if test="requiredTaskCompletedRate != null">
        required_task_completed_rate,
      </if>
      <if test="ojtTaskCount != null">
        ojt_task_count,
      </if>
      <if test="ojtTaskCompletedCount != null">
        ojt_task_completed_count,
      </if>
      <if test="ojtRequiredTaskCount != null">
        ojt_required_task_count,
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        ojt_required_task_completed_count,
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        ojt_required_task_completed_rate,
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        last_required_task_completed_time,
      </if>
      <if test="lastAllTaskCompletedTime != null">
        last_all_task_completed_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id,
      </if>
      <if test="dbArchived != null">
        db_archived,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        #{userId,jdbcType=CHAR},
      </if>
      <if test="actvId != null and actvId != ''">
        #{actvId,jdbcType=CHAR},
      </if>
      <if test="actvCompletedStatus != null">
        #{actvCompletedStatus,jdbcType=TINYINT},
      </if>
      <if test="actvCompletedRate != null">
        #{actvCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="actvCompletedTime != null">
        #{actvCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="periodCompletedCount != null">
        #{periodCompletedCount,jdbcType=TINYINT},
      </if>
      <if test="earnedPoint != null">
        #{earnedPoint,jdbcType=INTEGER},
      </if>
      <if test="earnedCredit != null">
        #{earnedCredit,jdbcType=DECIMAL},
      </if>
      <if test="firstStudyTime != null">
        #{firstStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastStudyTime != null">
        #{lastStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allTaskCount != null">
        #{allTaskCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedCount != null">
        #{allTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedRate != null">
        #{allTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="requiredTaskCount != null">
        #{requiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedCount != null">
        #{requiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedRate != null">
        #{requiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="ojtTaskCount != null">
        #{ojtTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtTaskCompletedCount != null">
        #{ojtTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCount != null">
        #{ojtRequiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAllTaskCompletedTime != null">
        #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="dbArchived != null">
        #{dbArchived,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    update rv_activity_member_statistics
    <set>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        user_id = #{userId,jdbcType=CHAR},
      </if>
      <if test="actvId != null and actvId != ''">
        actv_id = #{actvId,jdbcType=CHAR},
      </if>
      <if test="actvCompletedStatus != null">
        actv_completed_status = #{actvCompletedStatus,jdbcType=TINYINT},
      </if>
      <if test="actvCompletedRate != null">
        actv_completed_rate = #{actvCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="actvCompletedTime != null">
        actv_completed_time = #{actvCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="periodCompletedCount != null">
        period_completed_count = #{periodCompletedCount,jdbcType=TINYINT},
      </if>
      <if test="earnedPoint != null">
        earned_point = #{earnedPoint,jdbcType=INTEGER},
      </if>
      <if test="earnedCredit != null">
        earned_credit = #{earnedCredit,jdbcType=DECIMAL},
      </if>
      <if test="firstStudyTime != null">
        first_study_time = #{firstStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastStudyTime != null">
        last_study_time = #{lastStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allTaskCount != null">
        all_task_count = #{allTaskCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedCount != null">
        all_task_completed_count = #{allTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedRate != null">
        all_task_completed_rate = #{allTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="requiredTaskCount != null">
        required_task_count = #{requiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedCount != null">
        required_task_completed_count = #{requiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedRate != null">
        required_task_completed_rate = #{requiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="ojtTaskCount != null">
        ojt_task_count = #{ojtTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtTaskCompletedCount != null">
        ojt_task_completed_count = #{ojtTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCount != null">
        ojt_required_task_count = #{ojtRequiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        ojt_required_task_completed_count = #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        ojt_required_task_completed_rate = #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        last_required_task_completed_time = #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAllTaskCompletedTime != null">
        last_all_task_completed_time = #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="dbArchived != null">
        db_archived = #{dbArchived,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    update rv_activity_member_statistics
    set org_id = #{orgId,jdbcType=CHAR},
      user_id = #{userId,jdbcType=CHAR},
      actv_id = #{actvId,jdbcType=CHAR},
      actv_completed_status = #{actvCompletedStatus,jdbcType=TINYINT},
      actv_completed_rate = #{actvCompletedRate,jdbcType=DECIMAL},
      actv_completed_time = #{actvCompletedTime,jdbcType=TIMESTAMP},
      period_completed_count = #{periodCompletedCount,jdbcType=TINYINT},
      earned_point = #{earnedPoint,jdbcType=INTEGER},
      earned_credit = #{earnedCredit,jdbcType=DECIMAL},
      first_study_time = #{firstStudyTime,jdbcType=TIMESTAMP},
      last_study_time = #{lastStudyTime,jdbcType=TIMESTAMP},
      all_task_count = #{allTaskCount,jdbcType=INTEGER},
      all_task_completed_count = #{allTaskCompletedCount,jdbcType=INTEGER},
      all_task_completed_rate = #{allTaskCompletedRate,jdbcType=DECIMAL},
      required_task_count = #{requiredTaskCount,jdbcType=INTEGER},
      required_task_completed_count = #{requiredTaskCompletedCount,jdbcType=INTEGER},
      required_task_completed_rate = #{requiredTaskCompletedRate,jdbcType=DECIMAL},
      ojt_task_count = #{ojtTaskCount,jdbcType=INTEGER},
      ojt_task_completed_count = #{ojtTaskCompletedCount,jdbcType=INTEGER},
      ojt_required_task_count = #{ojtRequiredTaskCount,jdbcType=INTEGER},
      ojt_required_task_completed_count = #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER},
      ojt_required_task_completed_rate = #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL},
      last_required_task_completed_time = #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP},
      last_all_task_completed_time = #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=CHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=CHAR},
      db_archived = #{dbArchived,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_activity_member_statistics
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="actv_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.actvId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="actv_completed_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.actvCompletedStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="actv_completed_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.actvCompletedRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="actv_completed_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.actvCompletedTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="period_completed_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.periodCompletedCount,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="earned_point = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.earnedPoint,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="earned_credit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.earnedCredit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="first_study_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.firstStudyTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="last_study_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lastStudyTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="all_task_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.allTaskCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="all_task_completed_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.allTaskCompletedCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="all_task_completed_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.allTaskCompletedRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="required_task_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.requiredTaskCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="required_task_completed_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.requiredTaskCompletedCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="required_task_completed_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.requiredTaskCompletedRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ojt_task_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ojtTaskCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ojt_task_completed_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ojtTaskCompletedCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ojt_required_task_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ojtRequiredTaskCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ojt_required_task_completed_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ojtRequiredTaskCompletedCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ojt_required_task_completed_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ojtRequiredTaskCompletedRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="last_required_task_completed_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="last_all_task_completed_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lastAllTaskCompletedTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="db_archived = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dbArchived,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_activity_member_statistics
    (id, org_id, user_id, actv_id, actv_completed_status, actv_completed_rate, actv_completed_time, 
      period_completed_count, earned_point, earned_credit, first_study_time, last_study_time, 
      all_task_count, all_task_completed_count, all_task_completed_rate, required_task_count, 
      required_task_completed_count, required_task_completed_rate, ojt_task_count, ojt_task_completed_count, 
      ojt_required_task_count, ojt_required_task_completed_count, ojt_required_task_completed_rate, 
      last_required_task_completed_time, last_all_task_completed_time, deleted, create_time, 
      create_user_id, update_time, update_user_id, db_archived
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.orgId,jdbcType=CHAR}, #{item.userId,jdbcType=CHAR}, 
        #{item.actvId,jdbcType=CHAR}, #{item.actvCompletedStatus,jdbcType=TINYINT}, #{item.actvCompletedRate,jdbcType=DECIMAL}, 
        #{item.actvCompletedTime,jdbcType=TIMESTAMP}, #{item.periodCompletedCount,jdbcType=TINYINT}, 
        #{item.earnedPoint,jdbcType=INTEGER}, #{item.earnedCredit,jdbcType=DECIMAL}, #{item.firstStudyTime,jdbcType=TIMESTAMP}, 
        #{item.lastStudyTime,jdbcType=TIMESTAMP}, #{item.allTaskCount,jdbcType=INTEGER}, 
        #{item.allTaskCompletedCount,jdbcType=INTEGER}, #{item.allTaskCompletedRate,jdbcType=DECIMAL}, 
        #{item.requiredTaskCount,jdbcType=INTEGER}, #{item.requiredTaskCompletedCount,jdbcType=INTEGER}, 
        #{item.requiredTaskCompletedRate,jdbcType=DECIMAL}, #{item.ojtTaskCount,jdbcType=INTEGER}, 
        #{item.ojtTaskCompletedCount,jdbcType=INTEGER}, #{item.ojtRequiredTaskCount,jdbcType=INTEGER}, 
        #{item.ojtRequiredTaskCompletedCount,jdbcType=INTEGER}, #{item.ojtRequiredTaskCompletedRate,jdbcType=DECIMAL}, 
        #{item.lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP}, #{item.lastAllTaskCompletedTime,jdbcType=TIMESTAMP}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserId,jdbcType=CHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}, #{item.dbArchived,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    insert into rv_activity_member_statistics
    (id, org_id, user_id, actv_id, actv_completed_status, actv_completed_rate, actv_completed_time, 
      period_completed_count, earned_point, earned_credit, first_study_time, last_study_time, 
      all_task_count, all_task_completed_count, all_task_completed_rate, required_task_count, 
      required_task_completed_count, required_task_completed_rate, ojt_task_count, ojt_task_completed_count, 
      ojt_required_task_count, ojt_required_task_completed_count, ojt_required_task_completed_rate, 
      last_required_task_completed_time, last_all_task_completed_time, deleted, create_time, 
      create_user_id, update_time, update_user_id, db_archived
      )
    values
    (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=CHAR}, #{userId,jdbcType=CHAR}, #{actvId,jdbcType=CHAR}, 
      #{actvCompletedStatus,jdbcType=TINYINT}, #{actvCompletedRate,jdbcType=DECIMAL}, 
      #{actvCompletedTime,jdbcType=TIMESTAMP}, #{periodCompletedCount,jdbcType=TINYINT}, 
      #{earnedPoint,jdbcType=INTEGER}, #{earnedCredit,jdbcType=DECIMAL}, #{firstStudyTime,jdbcType=TIMESTAMP}, 
      #{lastStudyTime,jdbcType=TIMESTAMP}, #{allTaskCount,jdbcType=INTEGER}, #{allTaskCompletedCount,jdbcType=INTEGER}, 
      #{allTaskCompletedRate,jdbcType=DECIMAL}, #{requiredTaskCount,jdbcType=INTEGER}, 
      #{requiredTaskCompletedCount,jdbcType=INTEGER}, #{requiredTaskCompletedRate,jdbcType=DECIMAL}, 
      #{ojtTaskCount,jdbcType=INTEGER}, #{ojtTaskCompletedCount,jdbcType=INTEGER}, #{ojtRequiredTaskCount,jdbcType=INTEGER}, 
      #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER}, #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL}, 
      #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP}, #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, #{dbArchived,jdbcType=TINYINT})
    on duplicate key update 
    id = #{id,jdbcType=BIGINT}, 
    org_id = #{orgId,jdbcType=CHAR}, 
    user_id = #{userId,jdbcType=CHAR}, 
    actv_id = #{actvId,jdbcType=CHAR}, 
    actv_completed_status = #{actvCompletedStatus,jdbcType=TINYINT}, 
    actv_completed_rate = #{actvCompletedRate,jdbcType=DECIMAL}, 
    actv_completed_time = #{actvCompletedTime,jdbcType=TIMESTAMP}, 
    period_completed_count = #{periodCompletedCount,jdbcType=TINYINT}, 
    earned_point = #{earnedPoint,jdbcType=INTEGER}, 
    earned_credit = #{earnedCredit,jdbcType=DECIMAL}, 
    first_study_time = #{firstStudyTime,jdbcType=TIMESTAMP}, 
    last_study_time = #{lastStudyTime,jdbcType=TIMESTAMP}, 
    all_task_count = #{allTaskCount,jdbcType=INTEGER}, 
    all_task_completed_count = #{allTaskCompletedCount,jdbcType=INTEGER}, 
    all_task_completed_rate = #{allTaskCompletedRate,jdbcType=DECIMAL}, 
    required_task_count = #{requiredTaskCount,jdbcType=INTEGER}, 
    required_task_completed_count = #{requiredTaskCompletedCount,jdbcType=INTEGER}, 
    required_task_completed_rate = #{requiredTaskCompletedRate,jdbcType=DECIMAL}, 
    ojt_task_count = #{ojtTaskCount,jdbcType=INTEGER}, 
    ojt_task_completed_count = #{ojtTaskCompletedCount,jdbcType=INTEGER}, 
    ojt_required_task_count = #{ojtRequiredTaskCount,jdbcType=INTEGER}, 
    ojt_required_task_completed_count = #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER}, 
    ojt_required_task_completed_rate = #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL}, 
    last_required_task_completed_time = #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP}, 
    last_all_task_completed_time = #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP}, 
    deleted = #{deleted,jdbcType=TINYINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    create_user_id = #{createUserId,jdbcType=CHAR}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    update_user_id = #{updateUserId,jdbcType=CHAR},
    db_archived = #{dbArchived,jdbcType=TINYINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics">
    <!--@mbg.generated-->
    insert into rv_activity_member_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="userId != null and userId != ''">
        user_id,
      </if>
      <if test="actvId != null and actvId != ''">
        actv_id,
      </if>
      <if test="actvCompletedStatus != null">
        actv_completed_status,
      </if>
      <if test="actvCompletedRate != null">
        actv_completed_rate,
      </if>
      <if test="actvCompletedTime != null">
        actv_completed_time,
      </if>
      <if test="periodCompletedCount != null">
        period_completed_count,
      </if>
      <if test="earnedPoint != null">
        earned_point,
      </if>
      <if test="earnedCredit != null">
        earned_credit,
      </if>
      <if test="firstStudyTime != null">
        first_study_time,
      </if>
      <if test="lastStudyTime != null">
        last_study_time,
      </if>
      <if test="allTaskCount != null">
        all_task_count,
      </if>
      <if test="allTaskCompletedCount != null">
        all_task_completed_count,
      </if>
      <if test="allTaskCompletedRate != null">
        all_task_completed_rate,
      </if>
      <if test="requiredTaskCount != null">
        required_task_count,
      </if>
      <if test="requiredTaskCompletedCount != null">
        required_task_completed_count,
      </if>
      <if test="requiredTaskCompletedRate != null">
        required_task_completed_rate,
      </if>
      <if test="ojtTaskCount != null">
        ojt_task_count,
      </if>
      <if test="ojtTaskCompletedCount != null">
        ojt_task_completed_count,
      </if>
      <if test="ojtRequiredTaskCount != null">
        ojt_required_task_count,
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        ojt_required_task_completed_count,
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        ojt_required_task_completed_rate,
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        last_required_task_completed_time,
      </if>
      <if test="lastAllTaskCompletedTime != null">
        last_all_task_completed_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id,
      </if>
      <if test="dbArchived != null">
        db_archived,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        #{userId,jdbcType=CHAR},
      </if>
      <if test="actvId != null and actvId != ''">
        #{actvId,jdbcType=CHAR},
      </if>
      <if test="actvCompletedStatus != null">
        #{actvCompletedStatus,jdbcType=TINYINT},
      </if>
      <if test="actvCompletedRate != null">
        #{actvCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="actvCompletedTime != null">
        #{actvCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="periodCompletedCount != null">
        #{periodCompletedCount,jdbcType=TINYINT},
      </if>
      <if test="earnedPoint != null">
        #{earnedPoint,jdbcType=INTEGER},
      </if>
      <if test="earnedCredit != null">
        #{earnedCredit,jdbcType=DECIMAL},
      </if>
      <if test="firstStudyTime != null">
        #{firstStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastStudyTime != null">
        #{lastStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allTaskCount != null">
        #{allTaskCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedCount != null">
        #{allTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedRate != null">
        #{allTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="requiredTaskCount != null">
        #{requiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedCount != null">
        #{requiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedRate != null">
        #{requiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="ojtTaskCount != null">
        #{ojtTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtTaskCompletedCount != null">
        #{ojtTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCount != null">
        #{ojtRequiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAllTaskCompletedTime != null">
        #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="dbArchived != null">
        #{dbArchived,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="userId != null and userId != ''">
        user_id = #{userId,jdbcType=CHAR},
      </if>
      <if test="actvId != null and actvId != ''">
        actv_id = #{actvId,jdbcType=CHAR},
      </if>
      <if test="actvCompletedStatus != null">
        actv_completed_status = #{actvCompletedStatus,jdbcType=TINYINT},
      </if>
      <if test="actvCompletedRate != null">
        actv_completed_rate = #{actvCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="actvCompletedTime != null">
        actv_completed_time = #{actvCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="periodCompletedCount != null">
        period_completed_count = #{periodCompletedCount,jdbcType=TINYINT},
      </if>
      <if test="earnedPoint != null">
        earned_point = #{earnedPoint,jdbcType=INTEGER},
      </if>
      <if test="earnedCredit != null">
        earned_credit = #{earnedCredit,jdbcType=DECIMAL},
      </if>
      <if test="firstStudyTime != null">
        first_study_time = #{firstStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastStudyTime != null">
        last_study_time = #{lastStudyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allTaskCount != null">
        all_task_count = #{allTaskCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedCount != null">
        all_task_completed_count = #{allTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="allTaskCompletedRate != null">
        all_task_completed_rate = #{allTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="requiredTaskCount != null">
        required_task_count = #{requiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedCount != null">
        required_task_completed_count = #{requiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="requiredTaskCompletedRate != null">
        required_task_completed_rate = #{requiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="ojtTaskCount != null">
        ojt_task_count = #{ojtTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtTaskCompletedCount != null">
        ojt_task_completed_count = #{ojtTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCount != null">
        ojt_required_task_count = #{ojtRequiredTaskCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedCount != null">
        ojt_required_task_completed_count = #{ojtRequiredTaskCompletedCount,jdbcType=INTEGER},
      </if>
      <if test="ojtRequiredTaskCompletedRate != null">
        ojt_required_task_completed_rate = #{ojtRequiredTaskCompletedRate,jdbcType=DECIMAL},
      </if>
      <if test="lastRequiredTaskCompletedTime != null">
        last_required_task_completed_time = #{lastRequiredTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAllTaskCompletedTime != null">
        last_all_task_completed_time = #{lastAllTaskCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="dbArchived != null">
        db_archived = #{dbArchived,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="selectByActvIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_member_statistics
    where
    org_id = #{orgId,jdbcType=VARCHAR}
    AND actv_id = #{actvId,jdbcType=VARCHAR}
    AND user_id = #{userId,jdbcType=VARCHAR}
    and deleted = 0
  </select>

  <select id="getInvolvedDeptIdFullPaths" resultType="java.lang.String">
    select distinct a.id_full_path
    from udp_dept a
    where a.id in (
      select c.dept_id
      from rv_activity_participation_member b
      join rv_xpd                           d on d.org_id = b.org_id and d.aom_prj_id = b.actv_id
      join udp_lite_user_sp                 c on c.id = b.user_id and c.org_id = b.org_id
      where d.id = #{xpdId}
        and b.org_id = #{orgId}
        and b.deleted = 0
      )
  </select>

  <select id="avgProgressByActvIds" resultType="com.yxt.talent.rv.controller.client.general.xpd.viewobj.XpdClientVO">
    select ms.actv_id                  as id,
           round(avg(ms.actv_completed_rate) * 100, 2) as progress
    from rv_activity_member_statistics ms
    where org_id = #{orgId}
      and deleted = 0
      and actv_id in
    <foreach collection="actvIds" item="actvId" open="(" close=")" separator=",">
      #{actvId}
    </foreach>
    group by actv_id
  </select>

    <select id="listCompRate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rv_activity_member_statistics ms
        where org_id = #{orgId}
          and deleted = 0
          and user_id = #{userId}
        <choose>
            <when test="actvIds != null and actvIds.size() != 0">
                and actv_id in
                <foreach collection="actvIds" item="actvId" open="(" close=")" separator=",">
                    #{actvId}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>
</mapper>