<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimCombMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_dim_comb-->
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="xpd_id" property="xpdId"/>
    <result column="comb_name" property="combName"/>
    <result column="comb_name_i18n" property="combNameI18n"/>
    <result column="x_sd_dim_id" property="xSdDimId"/>
    <result column="y_sd_dim_id" property="ySdDimId"/>
    <result column="comb_type" property="combType"/>
    <result column="comb_desc" property="combDesc"/>
    <result column="deleted" property="deleted"/>
    <result column="create_time" property="createTime"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="update_time" property="updateTime"/>
    <result column="update_user_id" property="updateUserId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id
         , org_id
         , xpd_id
         , comb_name
         , comb_name_i18n
         , x_sd_dim_id
         , y_sd_dim_id
         , comb_type
         , comb_desc
         , deleted
         , create_time
         , create_user_id
         , update_time
         , update_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_comb
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_comb
      (id,
       org_id,
       xpd_id,
       comb_name,
       comb_name_i18n,
       x_sd_dim_id,
       y_sd_dim_id,
       comb_type,
       comb_desc,
       deleted,
       create_time,
       create_user_id,
       update_time,
       update_user_id)
    values
      (#{id},
       #{orgId},
       #{xpdId},
       #{combName},
       #{combNameI18n},
       #{xSdDimId},
       #{ySdDimId},
       #{combType},
       #{combDesc},
       #{deleted},
       #{createTime},
       #{createUserId},
       #{updateTime},
       #{updateUserId})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_comb
      (id,
       org_id,
       xpd_id,
       comb_name,
       comb_name_i18n,
       x_sd_dim_id,
       y_sd_dim_id,
       comb_type,
       comb_desc,
       deleted,
       create_time,
       create_user_id,
       update_time,
       update_user_id)
    values
      (#{id},
       #{orgId},
       #{xpdId},
       #{combName},
       #{combNameI18n},
       #{xSdDimId},
       #{ySdDimId},
       #{combType},
       #{combDesc},
       #{deleted},
       #{createTime},
       #{createUserId},
       #{updateTime},
       #{updateUserId})
    on duplicate key update id             = #{id},
                            org_id         = #{orgId},
                            xpd_id         = #{xpdId},
                            comb_name      = #{combName},
                            comb_name_i18n = #{combNameI18n},
                            x_sd_dim_id    = #{xSdDimId},
                            y_sd_dim_id    = #{ySdDimId},
                            comb_type      = #{combType},
                            comb_desc      = #{combDesc},
                            deleted        = #{deleted},
                            create_time    = #{createTime},
                            create_user_id = #{createUserId},
                            update_time    = #{updateTime},
                            update_user_id = #{updateUserId}
  </insert>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_comb
      (id,
       org_id,
       xpd_id,
       comb_name,
       comb_name_i18n,
       x_sd_dim_id,
       y_sd_dim_id,
       comb_type,
       comb_desc,
       deleted,
       create_time,
       create_user_id,
       update_time,
       update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR},
       #{item.orgId,jdbcType=CHAR},
       #{item.xpdId,jdbcType=CHAR},
       #{item.combName,jdbcType=VARCHAR},
       #{item.combNameI18n,jdbcType=VARCHAR},
       #{item.xSdDimId,jdbcType=CHAR},
       #{item.ySdDimId,jdbcType=CHAR},
       #{item.combType,jdbcType=TINYINT},
       #{item.combDesc,jdbcType=LONGVARCHAR},
       #{item.deleted,jdbcType=TINYINT},
       #{item.createTime,jdbcType=TIMESTAMP},
       #{item.createUserId,jdbcType=CHAR},
       #{item.updateTime,jdbcType=TIMESTAMP},
       #{item.updateUserId,jdbcType=CHAR})
    </foreach>
  </insert>

  <select id="countByName" resultType="int">
    select count(1)
    from rv_xpd_dim_comb
    where org_id = #{orgId} and comb_name = #{combName} and xpd_id = #{xpdId} and deleted = 0
  </select>

  <select id="pagingByOrgId" resultType="com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombListVO">
    select c.id             as id
         , c.org_id         as orgId
         , c.xpd_id         as xpdId
         , c.comb_name      as combName
         , c.x_sd_dim_id    as xSdDimId
         , c.y_sd_dim_id    as ySdDimId
         , c.comb_type      as combType
         , c.comb_desc      as combDesc
         , u.fullname       as createUserName
         , c.create_time    as createtime
         , c.create_user_id as createUserId
    from rv_xpd_dim_comb       c
    left join udp_lite_user_sp u on c.create_user_id = u.id and u.org_id = #{orgId}
    where c.org_id = #{orgId}
      and c.deleted = 0
    <if test="xDimId != null and xDimId != ''">
      and c.x_sd_dim_id = #{xDimId}
    </if>
    <if test="yDimId != null and yDimId != ''">
      and c.y_sd_dim_id = #{yDimId}
    </if>
    <if test="combName != null and combName != ''">
      and c.comb_name like concat('%', #{combName}, '%')
    </if>
    <if test="scopeSdDimIds != null and scopeSdDimIds.size() != 0">
      and (c.x_sd_dim_id in
        <foreach collection="scopeSdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        and c.y_sd_dim_id in
        <foreach collection="scopeSdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      )
    </if>
    order by c.comb_type,c.create_time desc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_comb
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and deleted = 0
  </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_comb a
    where org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
      -- 校验 xpd_id: 如果是模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
  </select>

  <select id="listInner" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_comb
    where org_id = #{orgId} and comb_type = 0
    and deleted = 0
  </select>

  <select id="listByXpdId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    select a.id
         , a.org_id
         , a.xpd_id
         , a.comb_name
         , a.comb_name_i18n
         , a.x_sd_dim_id
         , a.y_sd_dim_id
         , a.comb_type
         , a.comb_desc
         , b.show_type
    from rv_xpd_dim_comb            a
    inner join rv_xpd_grid_dim_comb b
               on a.id = b.dim_comb_id and b.deleted = 0 and b.org_id = a.org_id and b.xpd_id = #{xpdId}
    inner join rv_xpd_grid          c on c.org_id = b.org_id and c.id = b.grid_id and c.deleted = 0 and c.xpd_id = #{xpdId}
    where a.org_id = #{orgId}
      and a.deleted = 0
    group by a.id, b.order_index, b.show_type
    order by b.order_index
  </select>

  <select id="listByXpdIdPage" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    select a.id
         , a.org_id
         , a.xpd_id
         , a.comb_name
         , a.comb_name_i18n
         , a.x_sd_dim_id
         , a.y_sd_dim_id
         , a.comb_type
         , a.comb_desc
         , b.show_type
    from rv_xpd_dim_comb            a
           inner join rv_xpd_grid_dim_comb b
                      on a.id = b.dim_comb_id and b.deleted = 0 and b.org_id = a.org_id and b.xpd_id = #{xpdId}
           inner join rv_xpd_grid          c on c.org_id = b.org_id and c.id = b.grid_id and c.deleted = 0 and c.xpd_id = #{xpdId}
    where a.org_id = #{orgId}
      and a.deleted = 0
      <if test="dimIds != null and dimIds.size() > 0">
         and (
        a.x_sd_dim_id in
        <foreach collection="dimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        or
        a.y_sd_dim_id in
        <foreach collection="dimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
           )
      </if>

    <if test="keyword != null and keyword != ''">
      and a.comb_name like concat('%',#{keyword},'%')
    </if>
    group by a.id, b.order_index, b.show_type
    order by b.order_index
  </select>

  <select id="selectByDimIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_comb
    where org_id = #{orgId}
      and deleted = 0
    <choose>
      <when test="sdDimIds != null and sdDimIds.size() != 0">
        and (x_sd_dim_id in
        <foreach collection="sdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        or y_sd_dim_id in
        <foreach collection="sdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        )
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <update id="deleteByXpdId">
    update rv_xpd_dim_comb
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="listByXpdIdAndGridId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO">
    select c.id
         , c.org_id
         , c.xpd_id
         , c.comb_name
         , c.comb_name_i18n
         , c.x_sd_dim_id
         , c.y_sd_dim_id
         , c.comb_type
         , c.comb_desc
         , b.show_type
    from rv_xpd_grid                a
    inner join rv_xpd_grid_dim_comb b on a.id = b.grid_id and b.deleted = 0 and b.org_id = a.org_id and a.xpd_id = b.xpd_id
    inner join rv_xpd_dim_comb      c on c.id = b.dim_comb_id and c.deleted = 0 and b.org_id = c.org_id
    where a.org_id = #{orgId}
      and a.id = #{gridId}
      and a.xpd_id = #{xpdId}
      and a.deleted = 0
    order by b.order_index
  </select>

  <delete id="deleteByOrgId">
    DELETE FROM rv_xpd_dim_comb WHERE org_id = #{orgId} AND deleted = 0
  </delete>
</mapper>