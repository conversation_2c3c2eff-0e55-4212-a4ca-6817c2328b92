<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleCalcMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_dim_rule_calc-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="dim_rule_id" property="dimRuleId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="ref_ids" property="refIds" />
    <result column="calc_method" property="calcMethod" />
    <result column="weight" property="weight" />
    <result column="order_index" property="orderIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, sd_dim_id, dim_rule_id, sd_indicator_id, ref_ids, calc_method,
    weight, order_index, deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_dim_rule_calc
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_rule_calc (id, org_id, xpd_id, sd_dim_id, dim_rule_id, sd_indicator_id,
      ref_ids, calc_method, weight, order_index, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{sdDimId}, #{dimRuleId}, #{sdIndicatorId},
      #{refIds}, #{calcMethod}, #{weight}, #{orderIndex}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_rule_calc
    (id, org_id, xpd_id, sd_dim_id, dim_rule_id, sd_indicator_id, ref_ids, calc_method,
      weight, order_index, deleted, create_user_id, create_time, update_user_id, update_time
      )
    values
    (#{id}, #{orgId}, #{xpdId}, #{sdDimId}, #{dimRuleId}, #{sdIndicatorId},
      #{refIds}, #{calcMethod}, #{weight}, #{orderIndex}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    sd_dim_id = #{sdDimId},
    dim_rule_id = #{dimRuleId}, 
    sd_indicator_id = #{sdIndicatorId}, 
    ref_ids = #{refIds}, 
    calc_method = #{calcMethod}, 
    weight = #{weight}, 
    order_index = #{orderIndex}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>

    <select id="listByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_dim_rule_calc
        where org_id = #{orgId}
        and deleted = 0 and xpd_id = #{xpdId}
    </select>

  <update id="deleteBySdDimIds">
    update rv_xpd_dim_rule_calc
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
      and sd_dim_id in
    <foreach collection="sdDimIds" item="sdDimId" separator="," open="(" close=")">
      #{sdDimId}
    </foreach>
  </update>

  <update id="deleteByDimRuleId">
    update rv_xpd_dim_rule_calc
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and dim_rule_id = #{dimRuleId}
      and deleted = 0
  </update>

    <update id="deleteByDimRuleIds">
        update rv_xpd_dim_rule_calc
        set deleted        = 1,
            update_user_id = #{userId},
            update_time    = sysdate(3)
        where org_id = #{orgId}
        <choose>
            <when test="dimRuleIds != null and dimRuleIds.size() != 0">
                and dim_rule_id in
                <foreach collection="dimRuleIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
        and deleted = 0
    </update>

    <select id="listBySdDimId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_xpd_dim_rule_calc
    where org_id = #{orgId}
      and sd_dim_id = #{sdDimId}
      and deleted = 0
  </select>

  <select id="listByDimRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_rule_calc
    where org_id = #{orgId}
      and dim_rule_id = #{dimRuleId}
      and deleted = 0
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_rule_calc
    (id, org_id, xpd_id, sd_dim_id, dim_rule_id, sd_indicator_id, ref_ids, calc_method,
     weight, order_index, deleted, create_user_id, create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR},
       #{item.sdDimId,jdbcType=CHAR}, #{item.dimRuleId,jdbcType=CHAR}, #{item.sdIndicatorId,jdbcType=CHAR}, #{item.refIds,jdbcType=VARCHAR},
       #{item.calcMethod,jdbcType=TINYINT}, #{item.weight,jdbcType=DECIMAL},
       #{item.orderIndex,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="deleteByXpdId">
    update rv_xpd_dim_rule_calc
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgIdAndXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_rule_calc a
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId}
      and a.deleted = 0
  </select>

  <update id="deleteByIds">
    update rv_xpd_dim_rule_calc
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where deleted = 0
    <choose>
      <when test="ids != null and ids.size() != 0">
        and id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <select id="listByXpdIdAndSdDimId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sprv.rv_xpd_dim_rule_calc a
    where a.org_id = #{orgId}
    and a.xpd_id = #{xpdId}
    and a.sd_dim_id = #{sdDimId}
  </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    <!-- 根据组织ID查询维度计算规则列表 -->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_rule_calc a
    where a.org_id = #{orgId,jdbcType=VARCHAR}
      and a.deleted = 0
      -- 校验 xpd_id: 关联的项目ID必须有效
      and exists(
      select 1 from rv_xpd b where b.org_id = a.org_id and b.id = a.xpd_id and b.deleted = 0
      )
      -- 校验 dim_rule_id: 关联的维度规则ID必须有效
      and exists(
      select 1 from rv_xpd_dim_rule dr where dr.id = a.dim_rule_id and dr.deleted = 0
      )
  </select>
</mapper>