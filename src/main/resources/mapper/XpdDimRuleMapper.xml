<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_dim_rule-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="parent_id" property="parentId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="calc_type" property="calcType" />
    <result column="weight" property="weight" />
    <result column="calc_rule" property="calcRule" />
    <result column="formula" property="formula" />
    <result column="formula_display" property="formulaDisplay" />
    <result column="formula_expression" property="formulaExpression" />
    <result column="formula_exp_code" property="formulaExpCode" />
    <result column="result_type" property="resultType" />
    <result column="aom_act_id" property="aomActId" />
    <result column="level_type" property="levelType" />
    <result column="level_priority" property="levelPriority" />
    <result column="level_rule" property="levelRule" />
    <result column="rule_desc" property="ruleDesc" />
    <result column="enabled" property="enabled" />
    <result column="rule_threshold" property="ruleThreshold"/>
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, parent_id, sd_dim_id, calc_type, weight, calc_rule, formula,
    formula_display, formula_expression, formula_exp_code, result_type, aom_act_id, level_type, level_priority, level_rule,
    rule_desc, enabled,rule_threshold, deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_dim_rule
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_rule (id, org_id, xpd_id, parent_id, sd_dim_id, calc_type,
      weight, calc_rule, formula, formula_display, formula_expression, formula_exp_code, result_type, aom_act_id,
      level_type, level_priority, level_rule, rule_desc, enabled, deleted, 
      create_user_id, create_time, update_user_id, update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{parentId}, #{sdDimId}, #{calcType},
      #{weight}, #{calcRule}, #{formula}, #{formulaDisplay}, #{formulaExpression}, #{formulaExpCode}, #{resultType}, #{aomActId},
      #{levelType}, #{levelPriority}, #{levelRule}, #{ruleDesc}, #{enabled}, #{deleted}, 
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim_rule
    (id, org_id, xpd_id, parent_id, sd_dim_id, calc_type, weight, calc_rule,
      formula, formula_display, formula_expression, formula_exp_code, result_type, aom_act_id, level_type, level_priority,
      level_rule, rule_desc, enabled, deleted, create_user_id, create_time, update_user_id, 
      update_time)
    values
    (#{id}, #{orgId}, #{xpdId}, #{parentId}, #{sdDimId}, #{calcType}, #{weight},
      #{calcRule}, #{formula}, #{formulaDisplay}, #{formulaExpression}, #{formulaExpCode}, #{resultType}, #{aomActId}, #{levelType},
      #{levelPriority}, #{levelRule}, #{ruleDesc}, #{enabled}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    parent_id = #{parentId},
    sd_dim_id = #{sdDimId}, 
    calc_type = #{calcType}, 
    weight = #{weight}, 
    calc_rule = #{calcRule}, 
    formula = #{formula}, 
    formula_display = #{formulaDisplay},
    formula_expression = #{formulaExpression},
    formula_exp_code = #{formulaExpCode},
    result_type = #{resultType}, 
    aom_act_id = #{aomActId}, 
    level_type = #{levelType}, 
    level_priority = #{levelPriority}, 
    level_rule = #{levelRule}, 
    rule_desc = #{ruleDesc}, 
    enabled = #{enabled}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>

    <select id="listByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_dim_rule
        where org_id = #{orgId}
        and deleted = 0 and xpd_id = #{xpdId}
    </select>

  <select id="listBySdDimIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_rule
    where org_id = #{orgId}
      and deleted = 0
      and xpd_id = #{xpdId}
    <choose>
      <when test="sdDimIds != null and sdDimIds.size() != 0">
        and sd_dim_id in
        <foreach collection="sdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <update id="deleteBySdDimIds">
    update rv_xpd_dim_rule
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    <choose>
      <when test="sdDimIds != null and sdDimIds.size() != 0">
        and sd_dim_id in
        <foreach collection="sdDimIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <select id="getByXpdIdAndSdDimId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_xpd_dim_rule
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and sd_dim_id = #{sdDimId}
      and deleted = 0
    order by create_time desc
    limit 1
  </select>

    <select id="selectById" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from rv_xpd_dim_rule
      where id = #{id}
        and deleted = 0
    </select>

  <update id="deleteByIds">
    update rv_xpd_dim_rule
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where deleted = 0
    <choose>
      <when test="ids != null and ids.size() != 0">
        and id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <update id="updateBatch">
    UPDATE rv_xpd_dim_rule
    <trim prefix="SET" suffixOverrides=",">
      <trim prefix="xpd_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="parent_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.parentId}
        </foreach>
      </trim>
      <trim prefix="sd_dim_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.sdDimId}
        </foreach>
      </trim>
      <trim prefix="calc_type = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.calcType}
        </foreach>
      </trim>
      <trim prefix="weight = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.weight}
        </foreach>
      </trim>
      <trim prefix="calc_rule = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.calcRule}
        </foreach>
      </trim>
      <trim prefix="weight = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.weight}
        </foreach>
      </trim>
      <trim prefix="formula = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.formula}
        </foreach>
      </trim>
      <trim prefix="formula_display = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.formulaDisplay}
        </foreach>
      </trim>
      <trim prefix="formula_expression = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.formulaExpression}
        </foreach>
      </trim>
      <trim prefix="formula_exp_code = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.formulaExpCode}
        </foreach>
      </trim>
      <trim prefix="result_type = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.resultType}
        </foreach>
      </trim>
      <trim prefix="aom_act_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.aomActId}
        </foreach>
      </trim>
      <trim prefix="level_type = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.levelType}
        </foreach>
      </trim>
      <trim prefix="level_priority = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.levelPriority}
        </foreach>
      </trim>
      <trim prefix="level_rule = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.levelRule}
        </foreach>
      </trim>
      <trim prefix="rule_desc = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.ruleDesc}
        </foreach>
      </trim>
      <trim prefix="enabled = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.enabled}
        </foreach>
      </trim>
      <trim prefix="deleted = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
          THEN #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="update_user_id = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = CASE" suffix="END,">
        <foreach collection="list" index="index" item="item">
          WHEN id = #{item.id}
            THEN #{item.updateTime}
        </foreach>
      </trim>
    </trim>
    WHERE id IN
    <foreach collection="list" item="item" open="(" separator=", " close=")">
      #{item.id}
    </foreach>
  </update>

  <update id="setRuleDisable">
    update rv_xpd_dim_rule
    set enabled        = 0,
    update_user_id = #{userId},
    update_time    = sysdate(3)
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and deleted = 0
    and sd_dim_id = #{sdDimId}
  </update>

  <update id="setRuleEnable">
    update rv_xpd_dim_rule
    set enabled        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
      and sd_dim_id = #{sdDimId}
  </update>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_xpd_dim_rule
        (id, org_id, xpd_id, parent_id, sd_dim_id, calc_type, weight, calc_rule,
         formula, formula_display, formula_expression, formula_exp_code, result_type, aom_act_id, level_type, level_priority,
         level_rule, rule_desc, enabled, deleted, create_user_id, create_time, update_user_id,
         update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR}, #{item.parentId,jdbcType=CHAR},
             #{item.sdDimId,jdbcType=CHAR}, #{item.calcType,jdbcType=TINYINT}, #{item.weight,jdbcType=DECIMAL}, #{item.calcRule,jdbcType=TINYINT},
             #{item.formula,jdbcType=VARCHAR}, #{item.formulaDisplay,jdbcType=VARCHAR}, #{item.formulaExpression,jdbcType=VARCHAR}, #{item.formulaExpCode,jdbcType=VARCHAR}, #{item.resultType,jdbcType=TINYINT}, #{item.aomActId,jdbcType=CHAR},
             #{item.levelType,jdbcType=TINYINT}, #{item.levelPriority,jdbcType=TINYINT}, #{item.levelRule,jdbcType=VARCHAR}, #{item.ruleDesc,jdbcType=VARCHAR},
             #{item.enabled,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

  <update id="deleteByXpdId">
    update rv_xpd_dim_rule
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    <!-- 根据组织ID查询维度规则列表 -->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim_rule a
    where org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
      -- 1. 校验 xpd_id (保持不变)
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      -- 2. 校验 parent_id
      -- 如果是顶级规则(parent_id='0')则通过, 否则其父级规则必须存在
      AND (a.parent_id = '0' OR EXISTS(
      SELECT 1 FROM rv_xpd_dim_rule p WHERE p.org_id = a.org_id AND p.id = a.parent_id AND p.deleted = 0
      ))
      -- 4. 校验 aom_act_id
      -- 如果关联了绩效活动, 则该活动ID必须有效
      AND (a.aom_act_id IS NULL OR EXISTS(
      SELECT 1 FROM rv_activity act WHERE act.org_id = a.org_id AND act.id = a.aom_act_id AND act.deleted = 0
      ))
  </select>
</mapper>