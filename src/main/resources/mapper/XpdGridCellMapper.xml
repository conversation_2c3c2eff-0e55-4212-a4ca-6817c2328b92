<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridCellMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridCellPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_grid_cell-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="grid_id" property="gridId" />
    <result column="dim_comb_id" property="dimCombId" />
    <result column="cell_name" property="cellName" />
    <result column="cell_name_i18n" property="cellNameI18n" />
    <result column="cell_color" property="cellColor" />
    <result column="cell_index" property="cellIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="x_index" property="xIndex" />
    <result column="y_index" property="yIndex" />
    <result column="cell_desc" property="cellDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, grid_id, dim_comb_id, cell_name, cell_name_i18n, cell_color, 
    cell_index, deleted, create_time, create_user_id, update_time, update_user_id, x_index, 
    y_index, cell_desc
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridCellPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_cell (id, org_id, xpd_id, grid_id, dim_comb_id, cell_name, cell_name_i18n, 
      cell_color, cell_index, deleted, create_time, create_user_id, update_time, 
      update_user_id, x_index, y_index, cell_desc)
    values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{cellName}, #{cellNameI18n}, 
      #{cellColor}, #{cellIndex}, #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, 
      #{updateUserId}, #{xIndex}, #{yIndex}, #{cellDesc})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridCellPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_cell (id, org_id, xpd_id, grid_id, dim_comb_id, cell_name, cell_name_i18n,
    cell_color, cell_index, deleted, create_time, create_user_id, update_time,
    update_user_id, x_index, y_index, cell_desc)
    values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{cellName}, #{cellNameI18n},
    #{cellColor}, #{cellIndex}, #{deleted}, #{createTime}, #{createUserId}, #{updateTime},
    #{updateUserId}, #{xIndex}, #{yIndex}, #{cellDesc})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    grid_id = #{gridId},
    dim_comb_id = #{dimCombId},
    cell_name = #{cellName},
    cell_name_i18n = #{cellNameI18n},
    cell_color = #{cellColor},
    cell_index = #{cellIndex},
    deleted = #{deleted},
    create_time = #{createTime},
    create_user_id = #{createUserId},
    update_time = #{updateTime},
    update_user_id = #{updateUserId},
    x_index = #{xIndex},
    y_index = #{yIndex},
    cell_desc = #{cellDesc}
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridCellPO">
    <!--@mbg.generated-->
    update rv_xpd_grid_cell
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      grid_id = #{gridId},
      dim_comb_id = #{dimCombId},
      cell_name = #{cellName},
      cell_name_i18n = #{cellNameI18n},
      cell_color = #{cellColor},
      cell_index = #{cellIndex},
      deleted = #{deleted},
      create_time = #{createTime},
      create_user_id = #{createUserId},
      update_time = #{updateTime},
      update_user_id = #{updateUserId},
      x_index = #{xIndex},
      y_index = #{yIndex},
      cell_desc = #{cellDesc}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_grid_cell
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="grid_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.gridId}
        </foreach>
      </trim>
      <trim prefix="dim_comb_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.dimCombId}
        </foreach>
      </trim>
      <trim prefix="cell_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellName}
        </foreach>
      </trim>
      <trim prefix="cell_name_i18n = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellNameI18n}
        </foreach>
      </trim>
      <trim prefix="cell_color = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellColor}
        </foreach>
      </trim>
      <trim prefix="cell_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellIndex}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="x_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xIndex}
        </foreach>
      </trim>
      <trim prefix="y_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.yIndex}
        </foreach>
      </trim>
      <trim prefix="cell_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cellDesc}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_cell
    (id, org_id, xpd_id, grid_id, dim_comb_id, cell_name, cell_name_i18n, cell_color, 
      cell_index, deleted, create_time, create_user_id, update_time, update_user_id, 
      x_index, y_index, cell_desc)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.gridId}, #{item.dimCombId}, #{item.cellName}, 
        #{item.cellNameI18n}, #{item.cellColor}, #{item.cellIndex}, #{item.deleted}, #{item.createTime}, 
        #{item.createUserId}, #{item.updateTime}, #{item.updateUserId}, #{item.xIndex}, 
        #{item.yIndex}, #{item.cellDesc})
    </foreach>
  </insert>

  <select id="listByGridIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
    <choose>
      <when test="gridIds != null and gridIds.size > 0">
        and grid_id in
        <foreach collection="gridIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1=2
      </otherwise>
    </choose>
    and deleted = 0
  </select>

  <select id="selectByXpdIdAndGridIdAndDimCombId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell a
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId}
      and a.grid_id = #{gridId}
      and a.dim_comb_id = #{dimCombId}
      and a.deleted = 0
    order by a.cell_index
  </select>

  <select id="listByGridId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
      and grid_id = #{gridId}
      and deleted = 0
  </select>

  <insert id="insertBatch">
    insert into rv_xpd_grid_cell(id,org_id,xpd_id,
    grid_id,dim_comb_id,cell_name,
    cell_name_i18n,cell_color,cell_index,
    deleted,create_time,create_user_id,
    update_time,update_user_id, x_index, y_index, cell_desc)
    values
    <foreach collection="coll" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.gridId,jdbcType=VARCHAR},#{item.dimCombId,jdbcType=VARCHAR},#{item.cellName,jdbcType=VARCHAR},
      #{item.cellNameI18n,jdbcType=VARCHAR},#{item.cellColor,jdbcType=VARCHAR},#{item.cellIndex,jdbcType=NUMERIC},
      #{item.deleted,jdbcType=NUMERIC},#{item.createTime},#{item.createUserId,jdbcType=VARCHAR},
      #{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR}, #{item.xIndex,jdbcType=NUMERIC},
      #{item.yIndex,jdbcType=NUMERIC}, #{item.cellDesc,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="deleteGrid">
    update rv_xpd_grid_cell set deleted = 1, update_time = now(), update_user_id =#{userId}
    where org_id = #{orgId} and grid_id = #{gridId}
    and deleted = 0
  </update>

  <select id="listByGridIdAndDimCombId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
    and grid_id = #{gridId}
    and dim_comb_id = #{dimCombId}
    and deleted = 0
    order by cell_index
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
    and deleted = 0
    and id in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </select>

  <update id="deleteByXpdId">
    update rv_xpd_grid_cell
    set deleted        = 1,
        update_time    = now(),
        update_user_id = #{userId}
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="listPageByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
    and deleted = 0
    and xpd_id = #{xpdId}
    and dim_comb_id = #{dimCombId}
    order by cell_index
  </select>

  <select id="selectByDimCombIdAndIndex" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_cell
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and dim_comb_id = #{dimCombId}
    and x_index = #{xIndex}
    and y_index = #{yIndex}
    and deleted = 0
  </select>

  <update id="deleteByGridIdAndDimCombIds">
    update rv_xpd_grid_cell
    set deleted        = 1,
        update_time    = now(),
        update_user_id =#{userId}
    where org_id = #{orgId}
      and grid_id = #{gridId}
      and deleted = 0
    <choose>
      <when test="dimCombIds != null and dimCombIds.size() != 0">
        and dim_comb_id in
        <foreach collection="dimCombIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <update id="deleteByGridId">
    update rv_xpd_grid_cell
    set deleted        = 1,
        update_time    = now(),
        update_user_id =#{userId}
    where org_id = #{orgId}
      and grid_id = #{gridId}
      and deleted = 0
  </update>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_cell a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 如果是模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
      -- 校验 grid_id: 关联的宫格ID必须有效
      AND EXISTS(
      SELECT 1
      FROM rv_xpd_grid g
      WHERE g.id = a.grid_id AND g.deleted = 0
      )
      -- 校验 dim_comb_id: 如果为NULL或空字符串(统一配置)则通过, 否则其关联的维度组合ID必须有效
      AND (a.dim_comb_id IS NULL OR a.dim_comb_id = '' OR EXISTS(
      SELECT 1 FROM rv_xpd_dim_comb d WHERE d.id = a.dim_comb_id AND d.deleted = 0
      ))
  </select>

  <delete id="deleteByOrgId">
    DELETE FROM rv_xpd_grid_cell WHERE org_id = #{orgId} AND deleted = 0
  </delete>
</mapper>