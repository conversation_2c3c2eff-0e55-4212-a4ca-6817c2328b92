<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridDimCombMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridDimCombPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_grid_dim_comb-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="grid_id" property="gridId" />
    <result column="dim_comb_id" property="dimCombId" />
    <result column="show_type" property="showType" />
    <result column="deleted" property="deleted" />
    <result column="order_index" property="orderIndex" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, grid_id, dim_comb_id, show_type, deleted, order_index, create_time, 
    create_user_id, update_time, update_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_grid_dim_comb
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridDimCombPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_dim_comb (id, org_id, xpd_id, grid_id, dim_comb_id, show_type, deleted, 
      order_index, create_time, create_user_id, update_time, update_user_id)
    values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{showType}, #{deleted}, 
      #{orderIndex}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridDimCombPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_dim_comb
    (id, org_id, xpd_id, grid_id, dim_comb_id, show_type, deleted, order_index, create_time, 
      create_user_id, update_time, update_user_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{showType}, #{deleted}, #{orderIndex}, 
      #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    grid_id = #{gridId}, 
    dim_comb_id = #{dimCombId}, 
    show_type = #{showType}, 
    deleted = #{deleted}, 
    order_index = #{orderIndex}, 
    create_time = #{createTime}, 
    create_user_id = #{createUserId}, 
    update_time = #{updateTime}, 
    update_user_id = #{updateUserId}
  </insert>

  <select id="listByGridIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_dim_comb
    where org_id = #{orgId}
    <choose>
      <when test="gridIds != null and gridIds.size > 0">
        and grid_id in
        <foreach collection="gridIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1=2
      </otherwise>
    </choose>
    and deleted = 0
  </select>

  <select id="listByXpdIdAndGridId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_dim_comb
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and grid_id = #{gridId}
      and deleted = 0
  </select>
  <insert id="insertBatch">
    insert into rv_xpd_grid_dim_comb(id,org_id,xpd_id,
    grid_id,dim_comb_id,show_type,
    deleted,order_index,create_time,
    create_user_id,update_time,update_user_id)
    values
    <foreach collection="xpdGridDimCombPOCollection" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.gridId,jdbcType=VARCHAR},#{item.dimCombId,jdbcType=VARCHAR},#{item.showType,jdbcType=NUMERIC},
      #{item.deleted,jdbcType=NUMERIC},#{item.orderIndex,jdbcType=NUMERIC},#{item.createTime},
      #{item.createUserId,jdbcType=VARCHAR},#{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="findTempByGridId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_grid_dim_comb
    where org_id = #{orgId}
    and grid_id = #{gridId}
    and deleted = 0
    order by order_index
  </select>

  <insert id="batchInsertOrUpdate">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_dim_comb
    (id,org_id,xpd_id,
    grid_id,dim_comb_id,show_type,
    deleted,order_index,create_time,
    create_user_id,update_time,update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.gridId,jdbcType=VARCHAR},#{item.dimCombId,jdbcType=VARCHAR},#{item.showType,jdbcType=NUMERIC},
      #{item.deleted,jdbcType=NUMERIC},#{item.orderIndex,jdbcType=NUMERIC},#{item.createTime},
      #{item.createUserId,jdbcType=VARCHAR},#{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update
    <trim prefix="" suffix="" suffixOverrides=",">
      org_id     = values(org_id),
      xpd_id            = values(xpd_id),
      grid_id           = values(grid_id),
      dim_comb_id       = values(dim_comb_id),
      show_type         = values(show_type),
      deleted           = values(deleted),
      order_index       = values(order_index),
      create_time       = values(create_time),
      create_user_id        = values(create_user_id),
      update_time           = values(update_time),
      update_user_id       = values(update_user_id)
    </trim>
  </insert>

  <update id="deleteByIds">
    update rv_xpd_grid_dim_comb set  deleted = 1, update_time = now(), update_user_id = #{userId}
    where  id in
    <foreach collection="ids" item="id" index="index" open="(" separator=","
             close=")">
      #{id}
    </foreach>
  </update>

  <select id="findGridCombList" resultType="com.yxt.talent.rv.controller.manage.xpd.grid.dto.XpdGridDimCombDTO">
    select a.dim_comb_id dimCombId, b.comb_name dimCombName,
           b.x_sd_dim_id xDimId,
           b.y_sd_dim_id yDimId,
           a.show_type showType
    from rv_xpd_grid_dim_comb a
    left join rv_xpd_dim_comb b
    on a.org_id = b.org_id
    and a.dim_comb_id = b.id
    where a.org_id = #{orgId}
      and a.grid_id = #{gridId}
      and a.deleted = 0
      and b.deleted = 0
    order by a.order_index
  </select>
  <update id="deleteByGridId">
    update rv_xpd_grid_dim_comb
    set deleted        = 1,
        update_time    = sysdate(3),
        update_user_id = #{userId}
    where org_id = #{orgId}
      and grid_id = #{gridId}
  </update>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_dim_comb
    (id, org_id, xpd_id, grid_id, dim_comb_id, show_type, deleted,
     order_index, create_time, create_user_id, update_time, update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR},
       #{item.gridId,jdbcType=VARCHAR}, #{item.dimCombId,jdbcType=VARCHAR}, #{item.showType,jdbcType=TINYINT},
       #{item.deleted,jdbcType=TINYINT}, #{item.orderIndex,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserId,jdbcType=CHAR},
       #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR})
    </foreach>
  </insert>

  <update id="deleteByXpdId">
    update rv_xpd_grid_dim_comb
    set deleted        = 1,
        update_time    = sysdate(3),
        update_user_id = #{userId}
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="countByDimCombId" resultType="int">
    select count(1)
    from rv_xpd_grid_dim_comb
    where org_id = #{orgId} and dim_comb_id = #{dimCombId} and deleted = 0
  </select>

    <update id="deleteByGridIdAndDimCombId">
      update rv_xpd_grid_dim_comb
      set deleted        = 1,
          update_time    = sysdate(3),
          update_user_id = #{userId}
      where org_id = #{orgId}
        and grid_id = #{gridId}
        and dim_comb_id in
      <foreach collection="dimCombIds" item="dimCombId" index="index" open="(" separator=","
               close=")">
        #{dimCombId}
      </foreach>
        and deleted = 0
    </update>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      UPDATE rv_xpd_grid_dim_comb
      SET
      org_id = #{item.orgId},
      xpd_id = #{item.xpdId},
      grid_id = #{item.gridId},
      dim_comb_id = #{item.dimCombId},
      show_type = #{item.showType},
      deleted = #{item.deleted},
      order_index = #{item.orderIndex},
      update_time = NOW(3),
      update_user_id = #{item.updateUserId}
      WHERE id = #{item.id}
    </foreach>
  </update>

  <select id="selectByOrgId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridDimCombPO">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_dim_comb a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 如果是模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
        SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
      -- 校验 grid_id: 关联的宫格ID必须有效
      AND EXISTS(
        SELECT 1 FROM rv_xpd_grid g WHERE g.id = a.grid_id AND g.deleted = 0
      )
      -- 校验 dim_comb_id: 关联的维度组合ID必须有效
      AND EXISTS(
        SELECT 1 FROM rv_xpd_dim_comb d WHERE d.id = a.dim_comb_id AND d.deleted = 0
      )
  </select>

  <delete id="deleteByOrgId">
    DELETE FROM rv_xpd_grid_dim_comb WHERE org_id = #{orgId} AND deleted = 0
  </delete>
</mapper>