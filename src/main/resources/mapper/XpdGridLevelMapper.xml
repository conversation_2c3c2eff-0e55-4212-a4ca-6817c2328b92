<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_grid_level-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="grid_id" property="gridId" />
    <result column="level_name" property="levelName" />
    <result column="level_name_i18n" property="levelNameI18n" />
    <result column="order_index" property="orderIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="third_dim_color" property="thirdDimColor" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted, create_time, 
    create_user_id, update_time, update_user_id, third_dim_color
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_grid_level
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_grid_level
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_level (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, 
      deleted, create_time, create_user_id, update_time, update_user_id, third_dim_color
      )
    values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{levelName}, #{levelNameI18n}, #{orderIndex}, 
      #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId}, #{thirdDimColor}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
    <!--@mbg.generated-->
    update rv_xpd_grid_level
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      grid_id = #{gridId},
      level_name = #{levelName},
      level_name_i18n = #{levelNameI18n},
      order_index = #{orderIndex},
      deleted = #{deleted},
      create_time = #{createTime},
      create_user_id = #{createUserId},
      update_time = #{updateTime},
      update_user_id = #{updateUserId},
      third_dim_color = #{thirdDimColor}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_grid_level
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="grid_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.gridId}
        </foreach>
      </trim>
      <trim prefix="level_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.levelName}
        </foreach>
      </trim>
      <trim prefix="level_name_i18n = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.levelNameI18n}
        </foreach>
      </trim>
      <trim prefix="order_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orderIndex}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="third_dim_color = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.thirdDimColor}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_level
    (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted, 
      create_time, create_user_id, update_time, update_user_id, third_dim_color)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.gridId}, #{item.levelName}, #{item.levelNameI18n},
        #{item.orderIndex}, #{item.deleted}, #{item.createTime}, #{item.createUserId},
        #{item.updateTime}, #{item.updateUserId}, #{item.thirdDimColor})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_level
    (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted, 
      create_time, create_user_id, update_time, update_user_id, third_dim_color)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.gridId}, #{item.levelName}, #{item.levelNameI18n},
        #{item.orderIndex}, #{item.deleted}, #{item.createTime}, #{item.createUserId},
        #{item.updateTime}, #{item.updateUserId}, #{item.thirdDimColor})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    grid_id=values(grid_id),
    level_name=values(level_name),
    level_name_i18n=values(level_name_i18n),
    order_index=values(order_index),
    deleted=values(deleted),
    create_time=values(create_time),
    create_user_id=values(create_user_id),
    update_time=values(update_time),
    update_user_id=values(update_user_id),
    third_dim_color=values(third_dim_color)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_level
    (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted, 
      create_time, create_user_id, update_time, update_user_id, third_dim_color)
    values
    (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{levelName}, #{levelNameI18n}, #{orderIndex}, 
      #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId}, #{thirdDimColor}
      )
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    grid_id = #{gridId}, 
    level_name = #{levelName}, 
    level_name_i18n = #{levelNameI18n}, 
    order_index = #{orderIndex}, 
    deleted = #{deleted}, 
    create_time = #{createTime}, 
    create_user_id = #{createUserId}, 
    update_time = #{updateTime}, 
    update_user_id = #{updateUserId}, 
    third_dim_color = #{thirdDimColor}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="gridId != null">
        grid_id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="levelNameI18n != null">
        level_name_i18n,
      </if>
      <if test="orderIndex != null">
        order_index,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="thirdDimColor != null">
        third_dim_color,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="gridId != null">
        #{gridId},
      </if>
      <if test="levelName != null">
        #{levelName},
      </if>
      <if test="levelNameI18n != null">
        #{levelNameI18n},
      </if>
      <if test="orderIndex != null">
        #{orderIndex},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="thirdDimColor != null">
        #{thirdDimColor},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="gridId != null">
        grid_id = #{gridId},
      </if>
      <if test="levelName != null">
        level_name = #{levelName},
      </if>
      <if test="levelNameI18n != null">
        level_name_i18n = #{levelNameI18n},
      </if>
      <if test="orderIndex != null">
        order_index = #{orderIndex},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="thirdDimColor != null">
        third_dim_color = #{thirdDimColor},
      </if>
    </trim>
  </insert>

    <select id="listByGridIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        <choose>
            <when test="gridIds != null and gridIds.size &gt; 0">
                and grid_id in
                <foreach close=")" collection="gridIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
              <!--@ignoreSql-->
                and 1=2
            </otherwise>
        </choose>
        and deleted = 0
    </select>

    <select id="listByGridId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index
    </select>

    <select id="listByGridIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index desc
    </select>

    <select id="listByGridIdReverse" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index desc
    </select>

    <select id="listByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
        order by order_index
    </select>

    <update id="deleteGridLevel">
        update rv_xpd_grid_level set deleted = 1, update_time = now(), update_user_id = #{userId}
        where org_id = #{orgId} and grid_id = #{gridId}
        and deleted = 0
    </update>
    <insert id="insertBatch">
        insert into rv_xpd_grid_level(id,org_id,xpd_id,
        grid_id,level_name,level_name_i18n,
        order_index,deleted,create_time,
        create_user_id,update_time,update_user_id)
        values
        <foreach collection="coll" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
            #{item.gridId,jdbcType=VARCHAR},#{item.levelName,jdbcType=VARCHAR},#{item.levelNameI18n,jdbcType=VARCHAR},
            #{item.orderIndex,jdbcType=NUMERIC},#{item.deleted,jdbcType=NUMERIC},#{item.createTime},
            #{item.createUserId,jdbcType=VARCHAR},#{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and deleted = 0
        order by order_index
    </select>

    <update id="deleteByXpdId">
        update rv_xpd_grid_level
        set deleted = 1,
        update_time = now(),
        update_user_id = #{userId}
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
    </update>

    <select id="listByXpdIdPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
        order by order_index
    </select>
    <select id="selectByIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_grid_level
        where id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_level a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 如果是模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
      -- 校验 grid_id: 关联的宫格ID必须有效
      AND EXISTS(
      SELECT 1
      FROM rv_xpd_grid g
      WHERE g.id = a.grid_id AND g.deleted = 0
      )
  </select>

  <delete id="deleteByOrgId">
    DELETE FROM rv_xpd_grid_level WHERE org_id = #{orgId} AND deleted = 0
  </delete>
</mapper>