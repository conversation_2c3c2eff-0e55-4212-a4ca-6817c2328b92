<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO">
        <!--@mbg.generated-->
        <!--@Table rv_xpd_grid-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="xpd_id" property="xpdId"/>
        <result column="template" property="template"/>
        <result column="source_grid_id" property="sourceGridId"/>
        <result column="grid_name" property="gridName"/>
        <result column="grid_name_i18n" property="gridNameI18n"/>
        <result column="grid_type" property="gridType"/>
        <result column="config_type" property="configType"/>
        <result column="source_type" property="sourceType"/>
        <result column="grid_state" property="gridState"/>
        <result column="grid_desc" property="gridDesc"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, xpd_id, `template`, source_grid_id, grid_name, grid_name_i18n, grid_type, config_type,
        source_type, grid_state, grid_desc, deleted, create_time, create_user_id, update_time,
        update_user_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where id = #{id} and deleted = 0
    </select>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO">
        <!--@mbg.generated-->
        insert into rv_xpd_grid (id, org_id, xpd_id, `template`, source_grid_id, grid_name, grid_name_i18n, grid_type,
        config_type, source_type, grid_state, grid_desc, deleted, create_time,
        create_user_id, update_time, update_user_id)
        values (#{id}, #{orgId}, #{xpdId}, #{template}, #{sourceGridId}, #{gridName}, #{gridNameI18n}, #{gridType},
        #{configType}, #{sourceType}, #{gridState}, #{gridDesc}, #{deleted}, #{createTime},
        #{createUserId}, #{updateTime}, #{updateUserId})
    </insert>
    <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO">
        <!--@mbg.generated-->
        insert into rv_xpd_grid
        (id, org_id, xpd_id, `template`, source_grid_id, grid_name, grid_name_i18n, grid_type, config_type,
        source_type, grid_state, grid_desc, deleted, create_time, create_user_id, update_time,
        update_user_id)
        values
        (#{id}, #{orgId}, #{xpdId}, #{template}, #{sourceGridId}, #{gridName}, #{gridNameI18n}, #{gridType},
        #{configType}, #{sourceType}, #{gridState}, #{gridDesc}, #{deleted}, #{createTime},
        #{createUserId}, #{updateTime}, #{updateUserId})
        on duplicate key update
        id = #{id},
        org_id = #{orgId},
        xpd_id = #{xpdId},
        `template` = #{template},
        source_grid_id = #{sourceGridId},
        grid_name = #{gridName},
        grid_name_i18n = #{gridNameI18n},
        grid_type = #{gridType},
        config_type = #{configType},
        source_type = #{sourceType},
        grid_state = #{gridState},
        grid_desc = #{gridDesc},
        deleted = #{deleted},
        create_time = #{createTime},
        create_user_id = #{createUserId},
        update_time = #{updateTime},
        update_user_id = #{updateUserId}
    </insert>

    <select id="listInner" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId} and xpd_id = #{xpdId}
        and deleted = 0 and template = 0 and source_type = 0
    </select>

    <select id="selectPage" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId}
        <if test="query.name != null and query.name != '' ">
            and grid_name like CONCAT('%', #{query.name}, '%') ESCAPE '\\'
        </if>
        <if test="query.gridState != null and query.gridState != -1 ">
            and grid_state = #{query.gridState}
        </if>
        <if test="query.sourceType != null and query.sourceType != -1 ">
            and source_type = #{query.sourceType}
        </if>
        <if test="query.gridType != null and query.gridType != -1 ">
            and grid_type = #{query.gridType}
        </if>
        and deleted = 0
        and template = 0
        order by create_time desc, id
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId}
        <if test="query.name != null and query.name != '' ">
            and grid_name like CONCAT('%', #{query.name}, '%') ESCAPE '\\'
        </if>
        <if test="query.gridState != null and query.gridState != -1 ">
            and grid_state = #{query.gridState}
        </if>
        and deleted = 0
        and template = 0
        order by create_time desc, id
    </select>

    <select id="getTemplateByGridId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId}
        and id = #{gridId}
        and grid_state = 1
        and deleted = 0
        limit 1
    </select>

    <update id="deleteById">
        update rv_xpd_grid
        set deleted = 1,
        update_user_id = #{userId},
        update_time = sysdate(3)
        where id = #{id}
    </update>

    <select id="selectByXpdIdAndSourceGridId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and source_grid_id = #{sourceGridId}
        and template = 1
        and deleted = 0
    </select>
    <select id="selectByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        AND xpd_id = #{xpdId,jdbcType=VARCHAR}
        and deleted = 0
    </select>

    <update id="deleteByXpdId">
        update rv_xpd_grid
        set deleted = 1,
        update_user_id = #{userId},
        update_time = sysdate(3)
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
    </update>

    <select id="findCountByName" resultType="java.lang.Integer">
        select
        count(*)
        from rv_xpd_grid
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        and grid_name = #{name}
        and template = 0
        and deleted = 0
    </select>

    <select id="selectByGridIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        and grid_type = #{gridType}
        and deleted = 0
    </select>

    <select id="selectByXpdIdAndSourceType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where org_id = #{orgId,jdbcType=VARCHAR}
        and xpd_id = #{xpdId}
        and source_type = #{sourceType}
        and grid_type = #{gridType}
        and deleted = 0
    </select>
    <select id="selectByIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="select4RefreshThirdDimColor" resultType="java.lang.String">
        select
        a.id
        from rv_xpd_grid a
        where a.deleted = 0
    </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid a
    where org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
      -- 1. 校验 xpd_id
      -- 如果是机构模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
  </select>

<!--auto generated on 2025-06-06-->
  <insert id="batchInsert">
    INSERT INTO rv_xpd_grid(id,
                            org_id,
                            xpd_id,
                            `template`,
                            source_grid_id,
                            grid_name,
                            grid_name_i18n,
                            grid_type,
                            config_type,
                            source_type,
                            grid_state,
                            grid_desc,
                            deleted,
                            create_time,
                            create_user_id,
                            update_time,
                            update_user_id)VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (#{element.id},
       #{element.orgId},
       #{element.xpdId},
       #{element.template},
       #{element.sourceGridId},
       #{element.gridName},
       #{element.gridNameI18n},
       #{element.gridType},
       #{element.configType},
       #{element.sourceType},
       #{element.gridState},
       #{element.gridDesc},
       #{element.deleted},
       #{element.createTime},
       #{element.createUserId},
       #{element.updateTime},
       #{element.updateUserId})
    </foreach>
  </insert>

  <delete id="deleteByOrgId">
      delete from rv_xpd_grid
      where org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
  </delete>
</mapper>