<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridRatioMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridRatioPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_grid_ratio-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="grid_id" property="gridId" />
    <result column="dim_comb_id" property="dimCombId" />
    <result column="grid_cell_ids" property="gridCellIds" />
    <result column="grid_cell_index" property="gridCellIndex" />
    <result column="ratio" property="ratio" />
    <result column="order_index" property="orderIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, grid_id, dim_comb_id, grid_cell_ids, grid_cell_index, ratio, 
    order_index, deleted, create_time, create_user_id, update_time, update_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_grid_ratio
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridRatioPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_ratio (id, org_id, xpd_id, grid_id, dim_comb_id, grid_cell_ids, grid_cell_index, 
      ratio, order_index, deleted, create_time, create_user_id, update_time, 
      update_user_id)
    values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{gridCellIds}, #{gridCellIndex}, 
      #{ratio}, #{orderIndex}, #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, 
      #{updateUserId})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridRatioPO">
    <!--@mbg.generated-->
    insert into rv_xpd_grid_ratio
    (id, org_id, xpd_id, grid_id, dim_comb_id, grid_cell_ids, grid_cell_index, ratio,
      order_index, deleted, create_time, create_user_id, update_time, update_user_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{dimCombId}, #{gridCellIds}, #{gridCellIndex},
      #{ratio}, #{orderIndex}, #{deleted}, #{createTime}, #{createUserId}, #{updateTime},
      #{updateUserId})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    grid_id = #{gridId},
    dim_comb_id = #{dimCombId},
    grid_cell_ids = #{gridCellIds},
    grid_cell_index = #{gridCellIndex},
    ratio = #{ratio},
    order_index = #{orderIndex},
    deleted = #{deleted},
    create_time = #{createTime},
    create_user_id = #{createUserId},
    update_time = #{updateTime},
    update_user_id = #{updateUserId}
  </insert>

  <select id="listByGridIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_ratio
    where org_id = #{orgId}
    <choose>
      <when test="gridIds != null and gridIds.size > 0">
        and grid_id in
        <foreach collection="gridIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        and 1=2
      </otherwise>
    </choose>
    and deleted = 0
  </select>

  <select id="selectByXpdIdAndGridIdAndDimCombId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_ratio a
    where a.xpd_id = #{xpdId}
      and a.grid_id = #{gridId}
      and a.dim_comb_id = #{dimCombId}
    and a.deleted = 0
    order by a.order_index
  </select>

  <select id="listByGridId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_ratio
    where org_id = #{orgId}
      and grid_id = #{gridId}
      and deleted = 0
  </select>

  <update id="deleteGridRatio">
    update rv_xpd_grid_ratio set deleted = 1, update_time = now(), update_user_id = #{userId}
    where org_id = #{orgId} and grid_id = #{gridId}
    and deleted = 0
  </update>
  <insert id="batchInsert">
    insert into rv_xpd_grid_ratio(id,org_id,xpd_id,
    grid_id,dim_comb_id,grid_cell_ids,
    grid_cell_index,ratio,order_index,
    deleted,create_time,create_user_id,
    update_time,update_user_id)
    values
    <foreach collection="xpdGridRatioPOCollection" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.gridId,jdbcType=VARCHAR},#{item.dimCombId,jdbcType=VARCHAR},#{item.gridCellIds,jdbcType=VARCHAR},
      #{item.gridCellIndex,jdbcType=VARCHAR},#{item.ratio,jdbcType=DECIMAL},#{item.orderIndex,jdbcType=NUMERIC},
      #{item.deleted,jdbcType=NUMERIC},#{item.createTime},#{item.createUserId,jdbcType=VARCHAR},
      #{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="deleteByXpdId">
    update rv_xpd_grid_ratio
    set deleted        = 1,
        update_time    = now(),
        update_user_id = #{userId}
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_grid_ratio a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 如果是模板(xpd_id为全零)则通过, 否则其关联的项目ID必须有效
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
      -- 校验 grid_id: 关联的宫格ID必须有效
      AND EXISTS(
      SELECT 1
      FROM rv_xpd_grid g
      WHERE g.id = a.grid_id AND g.deleted = 0
      )
      -- 校验 dim_comb_id: 如果是统一配置(空字符串)则通过, 否则其关联的维度组合ID必须有效
      AND (a.dim_comb_id = '' OR EXISTS(
      SELECT 1 FROM rv_xpd_dim_comb d WHERE d.id = a.dim_comb_id AND d.deleted = 0
      ))
  </select>

  <delete id="deleteByOrgId">
    DELETE FROM rv_xpd_grid_ratio WHERE org_id = #{orgId} AND deleted = 0
  </delete>
</mapper>