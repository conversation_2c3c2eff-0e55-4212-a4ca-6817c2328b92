<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportDimUserMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportDimUserPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_import_dim_user-->
    <id column="id" property="id" />
    <result column="import_id" property="importId" />
    <result column="xpd_id" property="xpdId" />
    <result column="org_id" property="orgId" />
    <result column="user_id" property="userId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="grid_level_id" property="gridLevelId" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, import_id, xpd_id, org_id, user_id, sd_dim_id, grid_level_id, create_user_id, update_user_id,
    create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_import_dim_user
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportDimUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_dim_user (id, import_id, xpd_id, org_id, user_id, sd_dim_id, grid_level_id,
    create_user_id, update_user_id, create_time, update_time, deleted)
    values (#{id}, #{importId}, #{xpdId}, #{orgId}, #{userId}, #{sdDimId}, #{gridLevelId},
    #{createUserId}, #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportDimUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_dim_user
    (id, import_id, xpd_id, org_id, user_id, sd_dim_id, grid_level_id, create_user_id, update_user_id,
    create_time, update_time, deleted)
    values
    (#{id}, #{importId}, #{xpdId}, #{orgId}, #{userId}, #{sdDimId}, #{gridLevelId}, #{createUserId},
    #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
    on duplicate key update
    id = #{id},
    import_id = #{importId},
    xpd_id = #{xpdId},
    org_id = #{orgId},
    user_id = #{userId},
    sd_dim_id = #{sdDimId},
    grid_level_id = #{gridLevelId},
    create_user_id = #{createUserId},
    update_user_id = #{updateUserId},
    create_time = #{createTime},
    update_time = #{updateTime},
    deleted = #{deleted}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportDimUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_dim_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="importId != null">
        import_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="sdDimId != null">
        sd_dim_id,
      </if>
      <if test="gridLevelId != null">
        grid_level_id,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="importId != null">
        #{importId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="sdDimId != null">
        #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        #{gridLevelId},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="importId != null">
        import_id = #{importId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="sdDimId != null">
        sd_dim_id = #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        grid_level_id = #{gridLevelId},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
    </trim>
  </insert>
    <select id="selectByXpdIdAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_import_dim_user
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        AND xpd_id = #{xpdId,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        and deleted = 0
    </select>

  <select id="getBySdDimIdAndUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_import_dim_user
    where org_id = #{orgId} and xpd_id = #{xpdId} and sd_dim_id = #{sdDimId} and deleted = 0 and user_id in
    <foreach collection="userIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectUserCount" parameterType="java.lang.String" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.XpdImportUserDTO">
    select
      import_id importId, count(user_id) userNum
    from rv_xpd_import_dim_user
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and import_id in
    <foreach collection="importIds" item="importId" open="(" separator="," close=")">
      #{importId}
    </foreach>
    and deleted = 0
    group by import_id
  </select>

  <select id="getByUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_import_dim_user
    where org_id = #{orgId} and xpd_id = #{xpdId}
      and import_id = #{importId}
      and deleted = 0 and user_id in
    <foreach collection="userIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <insert id="insertBatch">
    insert into rv_xpd_import_dim_user(id,import_id,xpd_id,
    org_id,user_id,sd_dim_id,
    grid_level_id,create_user_id,update_user_id,
    create_time,update_time,deleted)
    values
    <foreach collection="xpdImportDimUserPOCollection" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.importId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.orgId,jdbcType=VARCHAR},#{item.userId,jdbcType=VARCHAR},#{item.sdDimId,jdbcType=VARCHAR},
      #{item.gridLevelId,jdbcType=VARCHAR},#{item.createUserId,jdbcType=VARCHAR},#{item.updateUserId,jdbcType=VARCHAR},
      #{item.createTime},#{item.updateTime},#{item.deleted,jdbcType=NUMERIC})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="list">
    <foreach collection="list" item="record" separator=";">
      UPDATE rv_xpd_import_dim_user
      SET
      import_id = #{record.importId},
      xpd_id = #{record.xpdId},
      org_id = #{record.orgId},
      user_id = #{record.userId},
      sd_dim_id = #{record.sdDimId},
      grid_level_id = #{record.gridLevelId},
      create_user_id = #{record.createUserId},
      update_user_id = #{record.updateUserId},
      update_time = NOW(),
      deleted = #{record.deleted}
      WHERE id = #{record.id}
    </foreach>
  </update>

  <select id="findUserGridLevel" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportGridLevelDTO">
    select a.user_id userId, a.grid_level_id gridLevelId,
           b.level_name gridLevelName
    from rv_xpd_import_dim_user a
           inner join rv_xpd_grid_level b
           on a.org_id = b.org_id
          and a.grid_level_id = b.id
          and b.deleted = 0
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId}
      and a.import_id = #{importId}
      and a.deleted = 0
      and a.user_id in
      <foreach collection="userIds" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>

  </select>
  <update id="deleteByImportIdAndUserId">
    update rv_xpd_import_dim_user set deleted = 1,update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and import_id = #{importId} and sd_dim_id = #{dimId} and  user_id =#{userId}
  </update>

  <update id="deleteByXpdId">
    update rv_xpd_import_dim_user set deleted = 1,update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
  </update>

  <update id="deleteByXpdIdAndDimIds">
    update rv_xpd_import_dim_user set deleted = 1,update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and sd_dim_id in
    <foreach collection="dimIds" item="dimId" open="(" separator="," close=")">
      #{dimId}
    </foreach>
  </update>

  <select id="findDimNum" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportDimNumDTO">
    select sd_dim_id dimId, count(*) dimNum
    from rv_xpd_result_user_dim where org_id = #{orgId}
                                  and xpd_id = #{xpdId}
                                  and user_id = #{userId}
                                  and deleted = 0
                                  and sd_dim_id in
    <foreach collection="dimIds" item="dimId" open="(" separator="," close=")">
      #{dimId}
    </foreach>
    group by sd_dim_id

  </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import_dim_user a
    where org_id = #{orgId}
      and deleted = 0
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      AND EXISTS(
      SELECT 1 FROM sprv.rv_xpd_import c WHERE c.org_id = a.org_id AND c.id = a.import_id AND c.deleted = 0
      )
      AND EXISTS(
      SELECT 1 FROM rv_xpd_grid_level d WHERE d.org_id = a.org_id AND d.id = a.grid_level_id AND d.deleted = 0
      )
  </select>
</mapper>