<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportLogMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportLogPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_import_log-->
    <id column="id" property="id" />
    <result column="xpd_id" property="xpdId" />
    <result column="org_id" property="orgId" />
    <result column="import_id" property="importId" />
    <result column="import_time" property="importTime" />
    <result column="import_file" property="importFile" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, xpd_id, org_id, import_id, import_time, import_file, create_user_id, update_user_id, 
    create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_import_log
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportLogPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_log (id, xpd_id, org_id, import_id, import_time, import_file, create_user_id, 
      update_user_id, create_time, update_time, deleted)
    values (#{id}, #{xpdId}, #{orgId}, #{importId}, #{importTime}, #{importFile}, #{createUserId}, 
      #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportLogPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_log
    (id, xpd_id, org_id, import_id, import_time, import_file, create_user_id, update_user_id, 
      create_time, update_time, deleted)
    values
    (#{id}, #{xpdId}, #{orgId}, #{importId}, #{importTime}, #{importFile}, #{createUserId}, 
      #{updateUserId}, #{createTime}, #{updateTime}, #{deleted})
    on duplicate key update 
    id = #{id}, 
    xpd_id = #{xpdId}, 
    org_id = #{orgId}, 
    import_id = #{importId}, 
    import_time = #{importTime}, 
    import_file = #{importFile}, 
    create_user_id = #{createUserId}, 
    update_user_id = #{updateUserId}, 
    create_time = #{createTime}, 
    update_time = #{updateTime}, 
    deleted = #{deleted}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportLogPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="importId != null">
        import_id,
      </if>
      <if test="importTime != null">
        import_time,
      </if>
      <if test="importFile != null">
        import_file,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="importId != null">
        #{importId},
      </if>
      <if test="importTime != null">
        #{importTime},
      </if>
      <if test="importFile != null">
        #{importFile},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="importId != null">
        import_id = #{importId},
      </if>
      <if test="importTime != null">
        import_time = #{importTime},
      </if>
      <if test="importFile != null">
        import_file = #{importFile},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
    </trim>
  </insert>
    <select id="selectByImportId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_import_log
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        AND import_id in
      <foreach close=")" collection="importIds" index="index" item="importId" open="(" separator=",">
        #{importId}
      </foreach>
        and deleted = 0
    </select>

  <update id="deleteByXpdId">
    update rv_xpd_import_log
    set deleted = 1,update_user_id = #{operator}, update_time = now()
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted =0
  </update>

  <update id="deleteByXpdIdAndImportIds">
    update rv_xpd_import_log
    set deleted = 1,update_user_id = #{operator}, update_time = now()
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted =0
    and import_id in
    <foreach close=")" collection="importIds" index="index" item="importId" open="(" separator=",">
      #{importId}
    </foreach>
  </update>

<!--auto generated on 2025-06-06-->
  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import_log a
    where org_id = #{orgId}
      and deleted = 0
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      AND EXISTS(
      SELECT 1 FROM sprv.rv_xpd_import c WHERE c.org_id = a.org_id AND c.id = a.import_id AND c.deleted = 0
      )
  </select>
</mapper>