<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdLevelMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_level-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="xpd_rule_id" property="xpdRuleId" />
    <result column="grid_id" property="gridId" />
    <result column="level_name" property="levelName" />
    <result column="level_name_i18n" property="levelNameI18n" />
    <result column="level_value" property="levelValue" />
    <result column="competent" property="competent" />
    <result column="icon" property="icon" />
    <result column="formula" property="formula" />
    <result column="formula_display" property="formulaDisplay" />
    <result column="order_index" property="orderIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, xpd_rule_id, grid_id, level_name, level_name_i18n, level_value,
    competent, icon, formula, formula_display, order_index, deleted, create_time, create_user_id,
    update_time, update_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_level
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO">
    <!--@mbg.generated-->
    insert into rv_xpd_level (id, org_id, xpd_id, xpd_rule_id, grid_id, level_name, level_name_i18n,
      level_value, competent, icon, formula, formula_display, order_index,
      deleted, create_time, create_user_id, update_time, update_user_id)
    values (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{gridId}, #{levelName}, #{levelNameI18n},
      #{levelValue}, #{competent}, #{icon}, #{formula}, #{formulaDisplay}, #{orderIndex},
      #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO">
    <!--@mbg.generated-->
    insert into rv_xpd_level
    (id, org_id, xpd_id, xpd_rule_id, grid_id, level_name, level_name_i18n, level_value,
      competent, icon, formula, formula_display, order_index, deleted, create_time, create_user_id,
      update_time, update_user_id)
    values
    (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{gridId}, #{levelName}, #{levelNameI18n},
      #{levelValue}, #{competent}, #{icon}, #{formula}, #{formulaDisplay}, #{orderIndex},
      #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    xpd_rule_id = #{xpdRuleId},
    grid_id = #{gridId},
    level_name = #{levelName},
    level_name_i18n = #{levelNameI18n},
    level_value = #{levelValue},
    competent = #{competent},
    icon = #{icon},
    formula = #{formula},
    formula_display = #{formulaDisplay},
    order_index = #{orderIndex},
    deleted = #{deleted},
    create_time = #{createTime},
    create_user_id = #{createUserId},
    update_time = #{updateTime},
    update_user_id = #{updateUserId}
  </insert>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_level
    where id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_level
    (id, org_id, xpd_id, xpd_rule_id, grid_id, level_name, level_name_i18n, level_value,
      competent, icon, formula, formula_display, order_index, deleted, create_time, create_user_id,
      update_time, update_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR},
        #{item.xpdRuleId,jdbcType=CHAR}, #{item.gridId,jdbcType=CHAR}, #{item.levelName,jdbcType=VARCHAR},
        #{item.levelNameI18n,jdbcType=VARCHAR}, #{item.levelValue,jdbcType=DECIMAL}, #{item.competent,jdbcType=TINYINT},
        #{item.icon,jdbcType=VARCHAR}, #{item.formula,jdbcType=LONGVARCHAR}, #{item.formulaDisplay,jdbcType=LONGVARCHAR},
        #{item.orderIndex,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}
        )
    </foreach>
  </insert>

  <select id="listByGridIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
    <choose>
      <when test="gridIds != null and gridIds.size > 0">
        and grid_id in
        <foreach collection="gridIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        and 1=2
      </otherwise>
    </choose>
    and deleted = 0
  </select>

  <select id="queryByRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and xpd_rule_id = #{xpdRuleId}
    and deleted = 0
    order by order_index
  </select>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level a
    where org_id = #{orgId}
      and deleted = 0
      -- 检查 xpd_id: 如果是 ZERO_UUID 则通过，否则必须在 rv_xpd 表中存在
      AND (a.xpd_id = '00000000-0000-0000-0000-000000000000' OR EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      ))
      -- 检查 xpd_rule_id: 如果是 ZERO_UUID 则通过，否则必须在 rv_xpd_rule 表中存在
      AND (a.xpd_rule_id = '00000000-0000-0000-0000-000000000000' OR a.xpd_rule_id = '' OR EXISTS(
      SELECT 1 FROM rv_xpd_rule c WHERE c.org_id = a.org_id AND c.id = a.xpd_rule_id AND c.deleted = 0
      ))
      AND EXISTS(
      SELECT 1 FROM rv_xpd_grid d WHERE d.org_id = a.org_id AND d.id = a.grid_id AND d.deleted = 0
      )
    order by order_index
  </select>

  <select id="countByName" resultType="int">
        select count(1)
        from rv_xpd_level
        where org_id = #{orgId}
          and level_name = #{levelName}
          and xpd_id = #{xpdId}
          and grid_id = #{gridId}
          and deleted = 0
        <if test="id != null and id != ''">
            and id not in (#{id})
        </if>
    </select>

    <select id="queryMaxOrderIndexByOrgId" resultType="int">
        select ifnull(max(order_index),0) from rv_xpd_level where org_id = #{orgId} and xpd_id = #{xpdId} and grid_id = #{gridId}
    </select>

    <select id="queryAllByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_level
        where org_id = #{orgId}
          and xpd_id = #{xpdId}
          and grid_id = #{gridId}
          and deleted = 0
        order by order_index
    </select>

  <select id="listByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    order by order_index
  </select>

  <select id="findByActId"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and deleted = 0
      and xpd_id = #{xpdId}
  </select>

  <select id="listByXpdRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
    order by order_index
  </select>

  <select id="listByXpdRuleIdReverse" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
    order by order_index desc
  </select>

  <update id="deleteByXpdRuleId">
    update rv_xpd_level
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
  </update>

    <update id="deleteByXpdId">
        update rv_xpd_level
        set deleted        = 1,
            update_user_id = #{userId},
            update_time    = sysdate(3)
        where org_id = #{orgId}
          and xpd_id = #{xpdId}
          and deleted = 0
    </update>

    <select id="listByGridId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and grid_id = #{gridId}
      and deleted = 0
    order by order_index
  </select>

  <select id="selectByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    order by order_index
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_level
    where deleted = 0
    <choose>
      <when test="ids != null and ids.size() != 0">
        and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>
  <update id="batchUpdateFormulaDisplay">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_level set formula_display = #{item.formulaDisplay} where id = #{item.id}
    </foreach>
  </update>

  <delete id="deleteByOrgId">
    delete from rv_xpd_level where org_id = #{orgId} and deleted = 0
  </delete>
</mapper>
