<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultIndicatorMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultIndicatorPO">
        <!--@mbg.generated-->
        <!--@Table rv_xpd_result_indicator-->
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="xpd_id" property="xpdId"/>
        <result column="sd_indicator_id" property="sdIndicatorId"/>
        <result column="score_total" property="scoreTotal"/>
        <result column="score_standard" property="scoreStandard"/>
        <result column="score_avg" property="scoreAvg"/>
        <result column="qualified_ptg" property="qualifiedPtg"/>
        <result column="deleted" property="deleted"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, xpd_id, sd_indicator_id, score_total, score_standard, score_avg, qualified_ptg,
        deleted, create_user_id, create_time, update_user_id, update_time
    </sql>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_xpd_result_indicator (id, org_id, xpd_id, sd_indicator_id, score_total, score_standard,
        score_avg, qualified_ptg, deleted, create_user_id, create_time, update_user_id,
        update_time)
        values (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{scoreTotal}, #{scoreStandard},
        #{scoreAvg}, #{qualifiedPtg}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId},
        #{updateTime})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_xpd_result_indicator
        (id, org_id, xpd_id, sd_indicator_id, score_total, score_standard, score_avg, qualified_ptg,
        deleted, create_user_id, create_time, update_user_id, update_time)
        values
        (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{scoreTotal}, #{scoreStandard}, #{scoreAvg},
        #{qualifiedPtg}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}
        )
        on duplicate key update
        id = #{id},
        org_id = #{orgId},
        xpd_id = #{xpdId},
        sd_indicator_id = #{sdIndicatorId},
        score_total = #{scoreTotal},
        score_standard = #{scoreStandard},
        score_avg = #{scoreAvg},
        qualified_ptg = #{qualifiedPtg},
        deleted = #{deleted},
        create_user_id = #{createUserId},
        create_time = #{createTime},
        update_user_id = #{updateUserId},
        update_time = #{updateTime}
    </insert>

  <delete id="deleteByXpdSdIndicatorIds">
    delete from rv_xpd_result_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
    and sd_indicator_id in
    <foreach item="indicatorId" collection="indicatorIds" open="(" separator="," close=")">
      #{indicatorId}
    </foreach>
  </delete>
    <select id="findAllByXpdId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_indicator
        where xpd_id = #{xpdId}
        and org_id = #{orgId}
        and deleted = 0

    </select>
    <select id="findByIndicatorIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_indicator
        where xpd_id = #{xpdId}
        and org_id = #{orgId}
        and deleted = 0
        and sd_indicator_id in
        <foreach item="indicatorId" collection="indicatorIds" open="(" separator="," close=")">
            #{indicatorId}
        </foreach>
    </select>

    <select id="selectByOrgId"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultIndicatorPO">
        select
        <include refid="Base_Column_List"/>
      from rv_xpd_result_indicator a
      where org_id = #{orgId}
        and deleted = 0
        AND EXISTS(
        SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
        )
  </select>
</mapper>