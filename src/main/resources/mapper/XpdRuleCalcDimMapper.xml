<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleCalcDimMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_rule_calc_dim-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="xpd_rule_id" property="xpdRuleId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="weight" property="weight" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="order_index" property="orderIndex" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, xpd_rule_id, sd_dim_id, weight, deleted, create_user_id, create_time, 
    update_user_id, update_time, order_index
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_rule_calc_dim
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_calc_dim (id, org_id, xpd_id, xpd_rule_id, sd_dim_id, weight, deleted, 
      create_user_id, create_time, update_user_id, update_time, order_index)
    values (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{sdDimId}, #{weight}, #{deleted}, 
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{orderIndex})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_calc_dim (id, org_id, xpd_id, xpd_rule_id, sd_dim_id, weight, deleted,
    create_user_id, create_time, update_user_id, update_time, order_index)
    values (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{sdDimId}, #{weight}, #{deleted},
    #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{orderIndex})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    xpd_rule_id = #{xpdRuleId},
    sd_dim_id = #{sdDimId},
    weight = #{weight},
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime},
    order_index = #{orderIndex}
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO">
    <!--@mbg.generated-->
    update rv_xpd_rule_calc_dim
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      xpd_rule_id = #{xpdRuleId},
      sd_dim_id = #{sdDimId},
      weight = #{weight},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      order_index = #{orderIndex}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_rule_calc_dim
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="xpd_rule_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdRuleId}
        </foreach>
      </trim>
      <trim prefix="sd_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdDimId}
        </foreach>
      </trim>
      <trim prefix="weight = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.weight}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="order_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orderIndex}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_calc_dim
    (id, org_id, xpd_id, xpd_rule_id, sd_dim_id, weight, deleted, create_user_id, create_time, 
      update_user_id, update_time, order_index)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.xpdRuleId}, #{item.sdDimId}, #{item.weight}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime}, #{item.orderIndex})
    </foreach>
  </insert>

  <select id="getByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule_calc_dim a
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and exists (
      select 1 from rv_xpd_rule b where b.deleted = 0 and b.id = a.xpd_rule_id and b.xpd_id = a.xpd_id
    )
  </select>

  <update id="deleteByXpdIdAndSdDimIds">
    update rv_xpd_rule_calc_dim
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and sd_dim_id in
    <foreach close=")" collection="delSdDimIds" item="sdDimId" open="(" separator=",">
      #{sdDimId}
    </foreach>
  </update>

  <select id="listByXpdRuleId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from rv_xpd_rule_calc_dim
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
    order by order_index
  </select>

  <update id="deleteByXpdRuleId">
    update rv_xpd_rule_calc_dim
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
    </update>

  <update id="deleteByXpdId">
    update rv_xpd_rule_calc_dim
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgId"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_calc_dim a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 关联的项目ID必须有效
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      -- 校验 xpd_rule_id: 关联的项目规则ID必须有效
      AND EXISTS(
      SELECT 1 FROM rv_xpd_rule xr WHERE xr.id = a.xpd_rule_id AND xr.deleted = 0
      )
  </select>
</mapper>