<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleCalcIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_rule_calc_indicator-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="xpd_rule_id" property="xpdRuleId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="ref_ids" property="refIds" />
    <result column="calc_method" property="calcMethod" />
    <result column="weight" property="weight" />
    <result column="order_index" property="orderIndex" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, xpd_rule_id, sd_indicator_id, ref_ids, calc_method, weight, order_index, 
    deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_rule_calc_indicator
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_calc_indicator (id, org_id, xpd_id, xpd_rule_id, sd_indicator_id, ref_ids, calc_method, 
      weight, order_index, deleted, create_user_id, create_time, update_user_id, 
      update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{sdIndicatorId}, #{refIds}, #{calcMethod}, 
      #{weight}, #{orderIndex}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, 
      #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_calc_indicator
    (id, org_id, xpd_id, xpd_rule_id, sd_indicator_id, ref_ids, calc_method, weight, 
      order_index, deleted, create_user_id, create_time, update_user_id, update_time)
    values
    (#{id}, #{orgId}, #{xpdId}, #{xpdRuleId}, #{sdIndicatorId}, #{refIds}, #{calcMethod}, 
      #{weight}, #{orderIndex}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, 
      #{updateTime})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    xpd_rule_id = #{xpdRuleId}, 
    sd_indicator_id = #{sdIndicatorId}, 
    ref_ids = #{refIds}, 
    calc_method = #{calcMethod}, 
    weight = #{weight}, 
    order_index = #{orderIndex}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}
  </insert>

  <insert id="batchInsert">
    insert into rv_xpd_rule_calc_indicator
    (id, org_id, xpd_id, xpd_rule_id, sd_indicator_id, ref_ids, calc_method,
     weight, order_index, deleted, create_user_id, create_time, update_user_id,
     update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.xpdRuleId}, #{item.sdIndicatorId}, #{item.refIds}, #{item.calcMethod},
       #{item.weight}, #{item.orderIndex}, #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId},
       #{item.updateTime})
    </foreach>
  </insert>

  <select id="getByXpdId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule_calc_indicator
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
  </select>

  <select id="listByXpdRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_calc_indicator
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
    order by order_index
  </select>

  <update id="deleteByXpdRuleId">
    update rv_xpd_rule_calc_indicator
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_rule_id = #{xpdRuleId}
      and deleted = 0
  </update>

  <update id="deleteByXpdId">
    update rv_xpd_rule_calc_indicator
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    </update>

  <select id="selectByOrgId"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_calc_indicator a
    where org_id = #{orgId}
      and deleted = 0
      -- 校验 xpd_id: 关联的项目ID必须有效
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      -- 校验 xpd_rule_id: 关联的项目规则ID必须有效
      AND EXISTS(
      SELECT 1 FROM rv_xpd_rule xr WHERE xr.id = a.xpd_rule_id AND xr.deleted = 0
      )
  </select>
</mapper>