<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleConfMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_rule_conf-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="template_grid_id" property="templateGridId" />
    <result column="grid_id" property="gridId" />
    <result column="result_type" property="resultType" />
    <result column="score_system" property="scoreSystem" />
    <result column="snap" property="snap" />
    <result column="version" property="version" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, template_grid_id, grid_id, result_type, score_system, snap, version, deleted, create_user_id, create_time,
    update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule_conf
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_conf (id, org_id, xpd_id, template_grid_id, grid_id, result_type, score_system, snap, version, deleted,
      create_user_id, create_time, update_user_id, update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{templateGridId}, #{gridId}, #{resultType}, #{scoreSystem}, #{snap}, #{version}, #{deleted},
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule_conf
    (id, org_id, xpd_id, template_grid_id, grid_id, result_type, score_system, snap, version, deleted, create_user_id, create_time,
      update_user_id, update_time)
    values
    (#{id}, #{orgId}, #{xpdId}, #{templateGridId}, #{gridId}, #{resultType}, #{scoreSystem}, #{snap}, #{version}, #{deleted}, #{createUserId},
      #{createTime}, #{updateUserId}, #{updateTime})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    template_grid_id = #{templateGridId},
    grid_id = #{gridId},
    result_type = #{resultType},
    score_system = #{scoreSystem},
    snap = #{snap},
    version = version + 1,
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime}
  </insert>

  <select id="selectByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_conf
    where xpd_id = #{xpdId}
      and org_id = #{orgId}
      and deleted = 0
    order by create_time desc
    limit 1
  </select>

  <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_conf
    where id = #{id}
      and deleted = 0
  </select>

  <update id="deleteByXpdId">
    update rv_xpd_rule_conf
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgId"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule_conf a
    where org_id = #{orgId}
      and deleted = 0
      -- 1. 校验 xpd_id (保持不变)
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
      -- 2. 校验 grid_id (已修正关联表)
      AND EXISTS(
      SELECT 1 FROM rv_xpd_grid c WHERE c.org_id = a.org_id AND c.id = a.grid_id AND c.deleted = 0
      )
      -- 3. 校验 template_grid_id (已补全)
      AND EXISTS(
      SELECT 1 FROM rv_xpd_grid d WHERE d.org_id = a.org_id AND d.id = a.template_grid_id AND d.deleted = 0
      )
  </select>
</mapper>