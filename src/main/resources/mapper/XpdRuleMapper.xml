<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_rule-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="calc_type" property="calcType" />
    <result column="result_type" property="resultType" />
    <result column="calc_rule" property="calcRule" />
    <result column="formula" property="formula" />
    <result column="formula_display" property="formulaDisplay" />
    <result column="level_type" property="levelType" />
    <result column="rule_desc" property="ruleDesc" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="level_priority" property="levelPriority" />
    <result column="formula_expression" property="formulaExpression" />
    <result column="formula_exp_code" property="formulaExpCode" />
    <result column="rule_threshold" property="ruleThreshold" />
    <result column="threshold_invalid" property="thresholdInvalid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, 
    level_type, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time, 
    level_priority, formula_expression, formula_exp_code, rule_threshold, threshold_invalid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_rule
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_rule
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, 
      formula_display, level_type, rule_desc, deleted, create_user_id, create_time, 
      update_user_id, update_time, level_priority, formula_expression, formula_exp_code, 
      rule_threshold, threshold_invalid)
    values (#{id}, #{orgId}, #{xpdId}, #{calcType}, #{resultType}, #{calcRule}, #{formula}, 
      #{formulaDisplay}, #{levelType}, #{ruleDesc}, #{deleted}, #{createUserId}, #{createTime}, 
      #{updateUserId}, #{updateTime}, #{levelPriority}, #{formulaExpression}, #{formulaExpCode}, 
      #{ruleThreshold}, #{thresholdInvalid})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    update rv_xpd_rule
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      calc_type = #{calcType},
      result_type = #{resultType},
      calc_rule = #{calcRule},
      formula = #{formula},
      formula_display = #{formulaDisplay},
      level_type = #{levelType},
      rule_desc = #{ruleDesc},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      level_priority = #{levelPriority},
      formula_expression = #{formulaExpression},
      formula_exp_code = #{formulaExpCode},
      rule_threshold = #{ruleThreshold},
      threshold_invalid = #{thresholdInvalid}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_rule
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="calc_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcType}
        </foreach>
      </trim>
      <trim prefix="result_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultType}
        </foreach>
      </trim>
      <trim prefix="calc_rule = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcRule}
        </foreach>
      </trim>
      <trim prefix="formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.formula}
        </foreach>
      </trim>
      <trim prefix="formula_display = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.formulaDisplay}
        </foreach>
      </trim>
      <trim prefix="level_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.levelType}
        </foreach>
      </trim>
      <trim prefix="rule_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.ruleDesc}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="level_priority = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.levelPriority}
        </foreach>
      </trim>
      <trim prefix="formula_expression = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.formulaExpression}
        </foreach>
      </trim>
      <trim prefix="formula_exp_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.formulaExpCode}
        </foreach>
      </trim>
      <trim prefix="rule_threshold = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.ruleThreshold}
        </foreach>
      </trim>
      <trim prefix="threshold_invalid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.thresholdInvalid}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_rule
    (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, 
      level_type, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time, 
      level_priority, formula_expression, formula_exp_code, rule_threshold, threshold_invalid
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.calcType}, #{item.resultType}, 
        #{item.calcRule}, #{item.formula}, #{item.formulaDisplay}, #{item.levelType}, #{item.ruleDesc}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime}, #{item.levelPriority}, #{item.formulaExpression}, #{item.formulaExpCode}, 
        #{item.ruleThreshold}, #{item.thresholdInvalid})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_rule
    (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, 
      level_type, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time, 
      level_priority, formula_expression, formula_exp_code, rule_threshold, threshold_invalid
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.calcType}, #{item.resultType}, 
        #{item.calcRule}, #{item.formula}, #{item.formulaDisplay}, #{item.levelType}, #{item.ruleDesc}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime}, #{item.levelPriority}, #{item.formulaExpression}, #{item.formulaExpCode}, 
        #{item.ruleThreshold}, #{item.thresholdInvalid})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    calc_type=values(calc_type),
    result_type=values(result_type),
    calc_rule=values(calc_rule),
    formula=values(formula),
    formula_display=values(formula_display),
    level_type=values(level_type),
    rule_desc=values(rule_desc),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    level_priority=values(level_priority),
    formula_expression=values(formula_expression),
    formula_exp_code=values(formula_exp_code),
    rule_threshold=values(rule_threshold),
    threshold_invalid=values(threshold_invalid)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule
    (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, 
      level_type, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time, 
      level_priority, formula_expression, formula_exp_code, rule_threshold, threshold_invalid
      )
    values
    (#{id}, #{orgId}, #{xpdId}, #{calcType}, #{resultType}, #{calcRule}, #{formula}, 
      #{formulaDisplay}, #{levelType}, #{ruleDesc}, #{deleted}, #{createUserId}, #{createTime}, 
      #{updateUserId}, #{updateTime}, #{levelPriority}, #{formulaExpression}, #{formulaExpCode}, 
      #{ruleThreshold}, #{thresholdInvalid})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    calc_type = #{calcType}, 
    result_type = #{resultType}, 
    calc_rule = #{calcRule}, 
    formula = #{formula}, 
    formula_display = #{formulaDisplay}, 
    level_type = #{levelType}, 
    rule_desc = #{ruleDesc}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}, 
    level_priority = #{levelPriority}, 
    formula_expression = #{formulaExpression}, 
    formula_exp_code = #{formulaExpCode}, 
    rule_threshold = #{ruleThreshold}, 
    threshold_invalid = #{thresholdInvalid}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="calcType != null">
        calc_type,
      </if>
      <if test="resultType != null">
        result_type,
      </if>
      <if test="calcRule != null">
        calc_rule,
      </if>
      <if test="formula != null">
        formula,
      </if>
      <if test="formulaDisplay != null">
        formula_display,
      </if>
      <if test="levelType != null">
        level_type,
      </if>
      <if test="ruleDesc != null">
        rule_desc,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="levelPriority != null">
        level_priority,
      </if>
      <if test="formulaExpression != null">
        formula_expression,
      </if>
      <if test="formulaExpCode != null">
        formula_exp_code,
      </if>
      <if test="ruleThreshold != null">
        rule_threshold,
      </if>
      <if test="thresholdInvalid != null">
        threshold_invalid,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="calcType != null">
        #{calcType},
      </if>
      <if test="resultType != null">
        #{resultType},
      </if>
      <if test="calcRule != null">
        #{calcRule},
      </if>
      <if test="formula != null">
        #{formula},
      </if>
      <if test="formulaDisplay != null">
        #{formulaDisplay},
      </if>
      <if test="levelType != null">
        #{levelType},
      </if>
      <if test="ruleDesc != null">
        #{ruleDesc},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="levelPriority != null">
        #{levelPriority},
      </if>
      <if test="formulaExpression != null">
        #{formulaExpression},
      </if>
      <if test="formulaExpCode != null">
        #{formulaExpCode},
      </if>
      <if test="ruleThreshold != null">
        #{ruleThreshold},
      </if>
      <if test="thresholdInvalid != null">
        #{thresholdInvalid},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="calcType != null">
        calc_type = #{calcType},
      </if>
      <if test="resultType != null">
        result_type = #{resultType},
      </if>
      <if test="calcRule != null">
        calc_rule = #{calcRule},
      </if>
      <if test="formula != null">
        formula = #{formula},
      </if>
      <if test="formulaDisplay != null">
        formula_display = #{formulaDisplay},
      </if>
      <if test="levelType != null">
        level_type = #{levelType},
      </if>
      <if test="ruleDesc != null">
        rule_desc = #{ruleDesc},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="levelPriority != null">
        level_priority = #{levelPriority},
      </if>
      <if test="formulaExpression != null">
        formula_expression = #{formulaExpression},
      </if>
      <if test="formulaExpCode != null">
        formula_exp_code = #{formulaExpCode},
      </if>
      <if test="ruleThreshold != null">
        rule_threshold = #{ruleThreshold},
      </if>
      <if test="thresholdInvalid != null">
        threshold_invalid = #{thresholdInvalid},
      </if>
    </trim>
  </insert>

  <select id="getByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 limit 1
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule
    where id = #{id}
      and deleted = 0
  </select>

  <update id="deleteByXpdId">
    update rv_xpd_rule
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>

  <select id="selectByOrgId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    select
    <include refid="Base_Column_List" />
    FROM rv_xpd_rule a
    WHERE a.org_id = #{orgId}
      AND a.deleted = 0
      -- 校验 xpd_id: 关联的项目ID必须有效
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
  </select>
</mapper>