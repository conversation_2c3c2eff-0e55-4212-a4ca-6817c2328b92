<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdSceneMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_scene-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="scene_name" jdbcType="VARCHAR" property="sceneName" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, scene_name, order_num, create_user_id, update_user_id, create_time, update_time, 
    deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_scene
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_scene
    where id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    insert into rv_xpd_scene (id, org_id, scene_name, 
      order_num, create_user_id, update_user_id, 
      create_time, update_time, deleted
      )
    values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{sceneName,jdbcType=VARCHAR}, 
      #{orderNum,jdbcType=INTEGER}, #{createUserId,jdbcType=CHAR}, #{updateUserId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    insert into rv_xpd_scene
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="sceneName != null and sceneName != ''">
        scene_name,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id,
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="sceneName != null and sceneName != ''">
        #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null and createUserId != ''">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    update rv_xpd_scene
    <set>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="sceneName != null and sceneName != ''">
        scene_name = #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    update rv_xpd_scene
    set org_id = #{orgId,jdbcType=CHAR},
      scene_name = #{sceneName,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=INTEGER},
      create_user_id = #{createUserId,jdbcType=CHAR},
      update_user_id = #{updateUserId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_scene
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="scene_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.sceneName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.orderNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=CHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_scene
    (id, org_id, scene_name, order_num, create_user_id, update_user_id, create_time, 
      update_time, deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.sceneName,jdbcType=VARCHAR}, 
        #{item.orderNum,jdbcType=INTEGER}, #{item.createUserId,jdbcType=CHAR}, #{item.updateUserId,jdbcType=CHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleted,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    insert into rv_xpd_scene
    (id, org_id, scene_name, order_num, create_user_id, update_user_id, create_time, 
      update_time, deleted)
    values
    (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{sceneName,jdbcType=VARCHAR}, #{orderNum,jdbcType=INTEGER}, 
      #{createUserId,jdbcType=CHAR}, #{updateUserId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT})
    on duplicate key update 
    id = #{id,jdbcType=CHAR}, 
    org_id = #{orgId,jdbcType=CHAR}, 
    scene_name = #{sceneName,jdbcType=VARCHAR}, 
    order_num = #{orderNum,jdbcType=INTEGER}, 
    create_user_id = #{createUserId,jdbcType=CHAR}, 
    update_user_id = #{updateUserId,jdbcType=CHAR}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    deleted = #{deleted,jdbcType=TINYINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene">
    <!--@mbg.generated-->
    insert into rv_xpd_scene
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="sceneName != null and sceneName != ''">
        scene_name,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id,
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="sceneName != null and sceneName != ''">
        #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null and createUserId != ''">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null and id != ''">
        id = #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="sceneName != null and sceneName != ''">
        scene_name = #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null and createUserId != ''">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null and updateUserId != ''">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <select id="queryPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from rv_xpd_scene where deleted = 0 and org_id = #{orgId} order by order_num
  </select>

  <select id="queryById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from rv_xpd_scene where deleted = 0 and org_id = #{orgId} and id = #{id}
  </select>

    <select id="queryBySceneName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_scene
        where org_id = #{orgId}
          and scene_name = #{sceneName}
          and deleted = 0
    </select>
</mapper>