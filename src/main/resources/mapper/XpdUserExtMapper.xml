<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdUserExtMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO">
    <id property="id" column="id" jdbcType="CHAR"/>
    <result property="orgId" column="org_id" jdbcType="CHAR"/>
    <result property="xpdId" column="xpd_id" jdbcType="CHAR"/>
    <result property="userId" column="user_id" jdbcType="CHAR"/>
    <result property="suggestion" column="suggestion" jdbcType="VARCHAR"/>
    <result property="createUserId" column="create_user_id" jdbcType="CHAR"/>
    <result property="updateUserId" column="update_user_id" jdbcType="CHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
         , org_id
         , xpd_id
         , user_id
         , suggestion
         , create_user_id
         , update_user_id
         , create_time
         , update_time
         , deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext
    WHERE id = #{id,jdbcType=CHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    DELETE FROM rv_xpd_user_ext WHERE id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
          parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO"
          useGeneratedKeys="true">
    INSERT INTO rv_xpd_user_ext
      (id, org_id, xpd_id, user_id, suggestion, create_user_id, update_user_id, create_time, update_time, deleted)
    VALUES
      (#{id,jdbcType=CHAR},
       #{orgId,jdbcType=CHAR},
       #{xpdId,jdbcType=CHAR},
       #{userId,jdbcType=CHAR},
       #{suggestion,jdbcType=VARCHAR},
       #{createUserId,jdbcType=CHAR},
       #{updateUserId,jdbcType=CHAR},
       #{createTime,jdbcType=TIMESTAMP},
       #{updateTime,jdbcType=TIMESTAMP},
       #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
          parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO"
          useGeneratedKeys="true">
    INSERT INTO rv_xpd_user_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="suggestion != null">
        suggestion,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=CHAR},
      </if>
      <if test="xpdId != null">
        #{xpdId,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=CHAR},
      </if>
      <if test="suggestion != null">
        #{suggestion,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO">
    UPDATE rv_xpd_user_ext
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=CHAR},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=CHAR},
      </if>
      <if test="suggestion != null">
        suggestion = #{suggestion,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey"
          parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdUserExtPO">
    UPDATE rv_xpd_user_ext
    SET org_id         = #{orgId,jdbcType=CHAR},
        xpd_id         = #{xpdId,jdbcType=CHAR},
        user_id        = #{userId,jdbcType=CHAR},
        suggestion     = #{suggestion,jdbcType=VARCHAR},
        create_user_id = #{createUserId,jdbcType=CHAR},
        update_user_id = #{updateUserId,jdbcType=CHAR},
        create_time    = #{createTime,jdbcType=TIMESTAMP},
        update_time    = #{updateTime,jdbcType=TIMESTAMP},
        deleted        = #{deleted,jdbcType=TINYINT}
    WHERE id = #{id,jdbcType=CHAR}
  </update>

  <select id="countXpdUserNum" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM rv_xpd_user_ext WHERE org_id = #{orgId} AND xpd_id = #{xpdId} AND deleted = 0
  </select>

  <select id="selectUserBaseInfo" resultType="com.yxt.talent.rv.application.xpd.user.dto.XpdUserStaticsDTO">
    SELECT a.user_id, a.join_method AS joinType, a.join_time AS joinTime, b.actv_completed_rate AS progress
    FROM rv_activity_participation_member a
    JOIN rv_activity_member_statistics    b
         ON b.user_id = a.user_id AND b.deleted = 0 AND b.actv_id = a.actv_id AND b.org_id = a.org_id
    JOIN rv_xpd                           c ON c.aom_prj_id = a.actv_id AND c.deleted = 0 AND c.org_id = a.org_id
    WHERE a.deleted = 0
      AND a.org_id = #{orgId}
      AND c.id = #{xpdId}
    GROUP BY a.user_id, a.join_method, a.join_time, b.actv_completed_rate
  </select>

  <select id="findByOrgIdAndXpdId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext
    WHERE org_id = #{orgId,jdbcType=VARCHAR}
      AND xpd_id = #{xpdId,jdbcType=VARCHAR}
      AND deleted = 0
  </select>

  <select id="selectPage" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportActUserVO">
    SELECT a.user_id   AS userId
         , b.fullname  AS fullName
         , b.username  AS userName
         , b.status
         , b.dept_name AS deptName
         , b.position  AS posName
    FROM rv_xpd_user_ext       a
    LEFT JOIN udp_lite_user_sp b ON a.org_id = b.org_id AND a.user_id = b.id
    WHERE a.org_id = #{orgId}
      AND a.xpd_id = #{xpdId}
    <if test="query.keywords != null and query.keywords != ''">
      AND ((b.fullname LIKE CONCAT('%', #{query.keywords}, '%') ESCAPE '\\') OR
           (b.username LIKE CONCAT('%', #{query.keywords}, '%') ESCAPE '\\'))
    </if>
    <if test="query.deptIds != null and query.deptIds.size() != 0">
      AND b.dept_id IN
      <foreach collection="query.deptIds" item="deptId" open="(" separator="," close=")">
        #{deptId}
      </foreach>
    </if>
    <if test="query.positionIds != null and query.positionIds.size() != 0">
      AND b.position_id IN
      <foreach collection="query.positionIds" item="positionId" open="(" separator="," close=")">
        #{positionId}
      </foreach>
    </if>
    AND a.deleted = 0
    ORDER BY a.create_time DESC
  </select>
  <select id="selectByXpdIdAndUserId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext
    WHERE org_id = #{orgId,jdbcType=VARCHAR}
      AND xpd_id = #{xpdId,jdbcType=VARCHAR}
      AND user_id = #{userId,jdbcType=VARCHAR}
      AND deleted = 0
    ORDER BY update_time DESC
    LIMIT 1
  </select>

  <select id="selectByXpdIdsAndUserId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext
    WHERE org_id = #{orgId,jdbcType=VARCHAR}
    <choose>
      <when test="xpdIds != null and xpdIds.size() != 0">
        AND xpd_id IN
        <foreach collection="xpdIds" item="xpdId" separator="," open="(" close=")">
          #{xpdId}
        </foreach>
      </when>
      <otherwise>
        AND FALSE
      </otherwise>
    </choose>
    AND user_id = #{userId,jdbcType=VARCHAR}
    AND deleted = 0
  </select>

  <insert id="insertOrUpdate">
    INSERT INTO rv_xpd_user_ext
      (id,
       org_id,
       xpd_id,
       user_id,
       suggestion,
       deleted,
       create_user_id,
       create_time,
       update_user_id,
       update_time)
    VALUES
      (#{id},
       #{orgId},
       #{xpdId},
       #{userId},
       #{suggestion},
       #{deleted},
       #{createUserId},
       #{createTime},
       #{updateUserId},
       #{updateTime})
    ON DUPLICATE KEY UPDATE org_id         = #{orgId},
                            xpd_id         = #{xpdId},
                            user_id        = #{userId},
                            suggestion     = #{suggestion},
                            deleted        = #{deleted},
                            create_user_id = #{createUserId},
                            create_time    = #{createTime},
                            update_user_id = #{updateUserId},
                            update_time    = #{updateTime}
  </insert>

  <select id="selectByXpdIdAndUserIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext
    WHERE org_id = #{orgId,jdbcType=VARCHAR}
      AND xpd_id = #{xpdId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        AND user_id IN
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
          #{userId}
        </foreach>
      </when>
      <otherwise>
        AND FALSE
      </otherwise>
    </choose>
    AND deleted = 0
  </select>


  <update id="batchUpdateRvXpdUserExt" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      UPDATE rv_xpd_user_ext
      <set>
        <if test="item.orgId != null">
          org_id = #{item.orgId},
        </if>
        <if test="item.xpdId != null">
          xpd_id = #{item.xpdId},
        </if>
        <if test="item.userId != null">
          user_id = #{item.userId},
        </if>
        <if test="item.suggestion != null">
          suggestion = #{item.suggestion},
        </if>
        <if test="item.createUserId != null">
          create_user_id = #{item.createUserId},
        </if>
        <if test="item.updateUserId != null">
          update_user_id = #{item.updateUserId},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime},
        </if>
        update_time = #{item.updateTime}
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>
  <insert id="insertBatch">
    INSERT INTO rv_xpd_user_ext(id,
                                org_id,
                                xpd_id,
                                user_id,
                                suggestion,
                                create_user_id,
                                update_user_id,
                                create_time,
                                update_time,
                                deleted)
    VALUES
    <foreach collection="xpdUserExtPOCollection" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},
       #{item.orgId,jdbcType=VARCHAR},
       #{item.xpdId,jdbcType=VARCHAR},
       #{item.userId,jdbcType=VARCHAR},
       #{item.suggestion,jdbcType=VARCHAR},
       #{item.createUserId,jdbcType=VARCHAR},
       #{item.updateUserId,jdbcType=VARCHAR},
       #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateTime,jdbcType=TIMESTAMP},
       #{item.deleted,jdbcType=NUMERIC})
    </foreach>
  </insert>

<!--auto generated on 2025-06-06-->
  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM rv_xpd_user_ext a
    WHERE a.org_id = #{orgId,jdbcType=CHAR}
      AND a.deleted = 0
      AND EXISTS(
      SELECT 1 FROM rv_xpd b WHERE b.org_id = a.org_id AND b.id = a.xpd_id AND b.deleted = 0
      )
  </select>
</mapper>
