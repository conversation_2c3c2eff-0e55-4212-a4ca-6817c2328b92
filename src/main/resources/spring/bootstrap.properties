spring.application.name=sptalentrvapi
server.port=19727

spring.cloud.nacos.config.enabled=${nacos.discover.enable:true}
spring.cloud.nacos.config.server-addr=${nacos.url:}
spring.cloud.nacos.config.namespace=${nacos.config.namespace:}
spring.cloud.nacos.config.shared-configs[0].data-id=application.properties
spring.cloud.nacos.config.shared-configs[0].refresh=true
spring.cloud.nacos.config.shared-configs[1].data-id=application-shared-endpoint.properties
spring.cloud.nacos.config.shared-configs[1].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[1].refresh=true
spring.cloud.nacos.config.shared-configs[2].data-id=application-shared-security.properties
spring.cloud.nacos.config.shared-configs[2].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[2].refresh=true
spring.cloud.nacos.config.shared-configs[3].data-id=sptalentrvapi-biz.properties
spring.cloud.nacos.config.shared-configs[3].group=DEFAULT_GROUP
spring.cloud.nacos.config.shared-configs[3].refresh=true

spring.cloud.nacos.discovery.enabled=${nacos.discover.enable:true}
spring.cloud.nacos.discovery.server-addr=${nacos.url:}
spring.cloud.nacos.discovery.namespace=${nacos.namespace:}

nacos.core.auth.caching.enabled=true
spring.cloud.nacos.config.username=${nacos.rw.username:}
spring.cloud.nacos.config.password=${nacos.rw.password:}
spring.cloud.nacos.discovery.username=${nacos.rw.username:}
spring.cloud.nacos.discovery.password=${nacos.rw.password:}

spring.messages.basename=sptalentrv-messages,talent-messages,aom-messages,aom-message,uacd-messages,common-messages
ikit.i18n.bundle.fallback-basename=message,i18n
