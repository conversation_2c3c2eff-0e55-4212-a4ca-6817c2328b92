package com.yxt.talent.rv;

import com.yxt.common.util.DateUtil;
import com.yxt.downfacade.bean.down.DownCreateFinish;
import com.yxt.downfacade.bean.down.DownCreateResult;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.downfacade.service.DownFacade;
import com.yxt.export.DlcComponent;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.calimeet.dto.CaliTempExportBean;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserImportDTO;
import com.yxt.talent.rv.application.calimeet.impt.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 以启动spring容器的方式进行测试
 */
@Transactional
@SpringBootTest
@AutoConfigureMockMvc
@Rollback(value = true)
@ActiveProfiles("native")
public class BaseSpringBootTest extends BaseTest { // NOSONAR

    @Autowired
    private PerfActivityService perfActivityService;

    @Autowired
    private DlcComponent dlcComponent;

    @Autowired
    private CaliDimLevelTemExportStrategy caliDimLevelTemExportStrategy;

    @Autowired
    private CaliDimTemExportStrategy caliDimTemExportStrategy;

    @Autowired
    private CaliIndicatorTemExportStrategy caliIndicatorTemExportStrategy;

    @Autowired
    private CaliDimLevelImporter caliDimLevelImporter;

    @Autowired
    private CaliIndicatorImporter caliIndicatorImporter;

    @Value("downloadcenter.uperr.domain")
    private String downloadCenterDomain;


    @Autowired
    private DownFacade downFacade;

    @Test
    public void testInfo(){
//        String s = "99999999999999969b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73（复制）";
//        System.out.println(convertString(s, 200));
//
//        String formData = "{\"name\": \"绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长\", \"modelId\": \"f136595a-d0af-4ece-b1d5-fc82d2c915c2\", \"aomActId\": \"\", \"confList\": [{\"weight\": 100, \"periodId\": \"478c6d4d-854b-44df-8b6f-12dd0ab8b876\"}], \"evalType\": 2, \"indicator\": \"大局观念\", \"periodIds\": \"478c6d4d-854b-44df-8b6f-12dd0ab8b876\", \"description\": \"\", \"indicatorId\": \"83d8370c-e14b-409d-8c72-62b3c85a927a\", \"evalTimeType\": 1, \"scoreQualified\": 1}";
//        PerfActivityDTO perfActivityDTO = BeanHelper.json2Bean(formData, PerfActivityDTO.class);
//        if (perfActivityDTO == null){
//            throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
//        }
//        perfActivityDTO.setName(convertString(perfActivityDTO.getName(), 200));
//        //                perfActivityDTO.setAomActId(activity.getActvId());
//        String orgId = "cc21f03c-003d-467f-9c06-91c562008fa9";
//        perfActivityService.createPerfActivity(orgId, "c532b4d3-f206-4804-86d8-2ee8e490be68", perfActivityDTO);

//        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
//        caliTempExportBean.setDms(Lists.newArrayList("能力","潜力","绩效"));
//        String path = dlcComponent.upload2TemporaryDisk("导出模板.xlsx", caliTempExportBean, caliDimLevelTemExportStrategy);
//        System.out.println(path);

//        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
//        caliTempExportBean.setDms(Lists.newArrayList("能力","潜力","绩效2"));
//
//        List<CaliUserImportDTO> userDatas = new ArrayList<>();
//        CaliUserImportDTO user1 = new CaliUserImportDTO();
//        user1.setUserId("111");
//        user1.setUserName("sunlh");
//        user1.setFullName("孙凌咺");
//
//        Map<String,String> dmResults = new LinkedHashMap<>();
//        dmResults.put("能力","9.33");
//        dmResults.put("潜力","8.33");
//        dmResults.put("绩效2","9.56");
//        user1.setDmResults(dmResults);
//        userDatas.add(user1);
//        caliTempExportBean.setUserDatas(userDatas);

//        String path = dlcComponent.upload2TemporaryDisk("导出校准结果分层模板.xlsx", caliTempExportBean, caliDimLevelTemExportStrategy);
////        System.out.println(path);


        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
        LinkedHashMap<String, List<String>> dmIndicators = new LinkedHashMap<>();
        dmIndicators.put("能力",Lists.newArrayList("指标1","指标2"));
        dmIndicators.put("潜力",Lists.newArrayList("指标3","指标4"));
        dmIndicators.put("绩效",Lists.newArrayList("指标5","指标6"));
//        caliTempExportBean.setDmIndicators(dmIndicators);

        List<CaliUserImportDTO> userDatas = new ArrayList<>();
        CaliUserImportDTO user1 = new CaliUserImportDTO();
        user1.setUserId("111");
        user1.setUserName("sunlh");
        user1.setFullName("孙凌咺");


        Map<String,String> dmResults = new LinkedHashMap<>();
        dmResults.put("能力指标1","9.33");
        dmResults.put("能力指标2","9.34");
        dmResults.put("潜力指标3","9.35");
        dmResults.put("潜力指标4","9.36");
        dmResults.put("绩效指标5","9.37");
        dmResults.put("绩效指标6","9.38");
        user1.setDmResults(dmResults);
        userDatas.add(user1);
        caliTempExportBean.setUserDatas(userDatas);

        String fileName = "导出指标结果模板";
        String fullName = "管理员";
        String userId = "c532b4d3-f206-4804-86d8-2ee8e490be68";
        String orgId = "cc21f03c-003d-467f-9c06-91c562008fa9";

        DownInfo4Add downInfo4Add = new DownInfo4Add();
        downInfo4Add.setFileName(fileName + System.nanoTime());
        downInfo4Add.setName(fileName);
        downInfo4Add.setFullname(fullName);
        downInfo4Add.setFileType(ExportFileTypeEnum.EXCEL.getType());
        downInfo4Add.setUserId(userId);
        downInfo4Add.setAppCode(AppConstants.DOWNLOAD_APP_CODE);
        downInfo4Add.setModuleCode(AppConstants.DOWNLOAD_MODULE_CODE);
        downInfo4Add.setFunctionName(AppConstants.FUNCTION_NAME);
        downInfo4Add.setOrgId(orgId);
        DownCreateResult downloadId = downFacade.createInfo(downInfo4Add);

        String path = dlcComponent.upload2TemporaryDisk("导出指标结果模板.xlsx", caliTempExportBean, caliIndicatorTemExportStrategy);
        System.out.println(path);

        DownCreateFinish downCreateFinish = new DownCreateFinish();
        downCreateFinish.setId(downloadId.getId());
        downCreateFinish.setStatus(0);
        downCreateFinish.setLocalPath(path.replace(downloadCenterDomain,""));
        downCreateFinish.setGenerateFinishTime(DateUtil.formatDate(new Date()));
        downFacade.finish(downCreateFinish);

//        FileImportCmd fileImportCmd = new FileImportCmd();
//        fileImportCmd.setFileId("c8f38619-496a-4a91-a73a-c20ffbfd3d4b");
//        UserCacheDetail userCacheDetail = new UserCacheDetail();
//        userCacheDetail.setOrgId("cc21f03c-003d-467f-9c06-91c562008fa9");
//        userCacheDetail.setUserId("c532b4d3-f206-4804-86d8-2ee8e490be68");
//        caliDimLevelImporter.toImport(fileImportCmd, null, userCacheDetail);

//        FileImportCmd fileImportCmd = new FileImportCmd();
//        fileImportCmd.setFileId("8f104dca-cdbd-4c99-b4cc-159b2d80e321");
//        UserCacheDetail userCacheDetail = new UserCacheDetail();
//        userCacheDetail.setOrgId("cc21f03c-003d-467f-9c06-91c562008fa9");
//        userCacheDetail.setUserId("c532b4d3-f206-4804-86d8-2ee8e490be68");
//        caliIndicatorImporter.toImport(fileImportCmd, null, userCacheDetail);
    }


}
