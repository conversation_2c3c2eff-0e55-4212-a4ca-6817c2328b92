package com.yxt.talent.rv.infrastructure.service.rule;

import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.application.xpd.rule.RvRuleComponent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 循环依赖修复测试
 * 验证修复后的代码能正确处理初始化顺序问题
 */
@ExtendWith(MockitoExtension.class)
class CircularDependencyFixTest {

    @Mock
    private ApplicationContext applicationContext;
    
    @Mock
    private SpRuleService spRuleService;
    
    @Mock
    private RvRuleComponent rvRuleComponent;

    private RuleTypeHandlerFactory factory;
    private RuleTypeHandlerRegistry registry;

    @BeforeEach
    void setUp() {
        factory = new RuleTypeHandlerFactory();
        
        // 模拟ApplicationContext返回RvRuleComponent
        when(applicationContext.getBean(RvRuleComponent.class)).thenReturn(rvRuleComponent);
        
        // 创建注册器，传入空的处理器列表以简化测试
        registry = new RuleTypeHandlerRegistry(
            factory, applicationContext,
            null, null, null, null, null, null, null, null, null, null, null
        );
    }

    @Test
    void testFactoryStaticInstanceIsSetDuringRegistration() {
        // 在注册之前，静态实例应该为null
        RuleTypeHandlerFactory.setStaticInstance(null);
        assertNull(RuleTypeHandlerFactory.getInstance());
        
        // 执行注册
        registry.registerHandlers();
        
        // 注册后，静态实例应该被设置
        assertSame(factory, RuleTypeHandlerFactory.getInstance());
        
        // 验证RvRuleComponent的initRuleColumns方法被调用
        verify(rvRuleComponent, times(1)).initRuleColumns();
    }

    @Test
    void testRegistryHandlesRvRuleComponentException() {
        // 模拟获取RvRuleComponent时抛出异常
        when(applicationContext.getBean(RvRuleComponent.class))
            .thenThrow(new RuntimeException("Bean not found"));
        
        // 执行注册不应该抛出异常
        assertDoesNotThrow(() -> registry.registerHandlers());
        
        // 静态实例仍然应该被设置
        assertSame(factory, RuleTypeHandlerFactory.getInstance());
    }

    @Test
    void testRvRuleComponentInitRuleColumns() {
        // 创建真实的RvRuleComponent实例进行测试
        RvRuleComponent realComponent = new RvRuleComponent(
            null, null, null, null, null, spRuleService
        );
        
        // 调用initRuleColumns方法
        realComponent.initRuleColumns();
        
        // 验证spRuleService.registerRuleColumns被调用
        verify(spRuleService, times(1)).registerRuleColumns(any(List.class));
    }

    @Test
    void testRuleTypeEnumAcceptOperatorWithNullFactory() {
        // 清除静态实例
        RuleTypeHandlerFactory.setStaticInstance(null);
        
        // 调用acceptOperator应该不抛出异常
        assertDoesNotThrow(() -> {
            RvRuleTypeEnum.XPD_DIM_COUNT.acceptOperator();
        });
    }

    @Test
    void testRuleTypeEnumAcceptOperatorWithFactory() {
        // 设置静态实例
        RuleTypeHandlerFactory.setStaticInstance(factory);
        
        // 创建模拟处理器
        RuleTypeHandler mockHandler = mock(RuleTypeHandler.class);
        when(mockHandler.getRuleType()).thenReturn(RvRuleTypeEnum.XPD_DIM_COUNT.columnType());
        when(mockHandler.getSupportedOperators()).thenReturn(new com.yxt.spsdk.common.enums.SpCompareOperator[0]);
        
        // 注册处理器
        factory.registerHandler(mockHandler);
        
        // 调用acceptOperator应该正常工作
        assertDoesNotThrow(() -> {
            RvRuleTypeEnum.XPD_DIM_COUNT.acceptOperator();
        });
        
        // 验证处理器的getSupportedOperators方法被调用
        verify(mockHandler, times(1)).getSupportedOperators();
    }
}
