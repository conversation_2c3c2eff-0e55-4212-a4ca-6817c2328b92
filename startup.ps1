param(
    [string]$jarFileName = "sptalentrvapi.jar",
    [string]$profile = "native"
)

# 设置控制台输出编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 获取脚本所在目录的绝对路径
$scriptPath = $PSScriptRoot
if (!$scriptPath) {
    $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
}

Write-Host "Script path: $scriptPath"

# 构建 jar 文件的完整路径（在target目录下）
$targetPath = Join-Path -Path $scriptPath -ChildPath "target"
$jarFile = Join-Path -Path $targetPath -ChildPath $jarFileName

Write-Host "Looking for jar file at: $jarFile"

# 检查 jar 文件是否存在
if (!(Test-Path -Path $jarFile -PathType Leaf)) {
    Write-Error "Jar file not found: $jarFile"
    exit 1
} else {
    Write-Host "Found jar file: $jarFile"
}

$javaArgs = @(
    "-Dspring.profiles.active=$profile"
    "-Dnacos.url=***********:8848"
    "-Dnacos.config.url=***********:8848"
    "-Dspring.cloud.nacos.discovery.enabled=false"
    "-Dspring.cloud.nacos.config.enabled=false"
    "-Dnacos.rw.username=readwrite"
    "-Dnacos.rw.password=readwrite"
    "-Dnacos.ro.username=read"
    "-Dnacos.ro.password=read"
    "-Dcom.sun.management.jmxremote"
    "-Dcom.sun.management.jmxremote.port=25000"
    "-Dcom.sun.management.jmxremote.ssl=false"
    "-Dcom.sun.management.jmxremote.authenticate=false"
    "-Xmx1000m"
    "-Xms500m"
    "-Xss512k"
    "-XX:MetaspaceSize=500m"
    "-XX:MaxMetaspaceSize=500m"
    "-XX:ReservedCodeCacheSize=500m"
    "-Dfile.encoding=UTF-8"
    "--add-opens=java.base/java.lang=ALL-UNNAMED"
    "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED"
    "--add-opens=java.base/java.io=ALL-UNNAMED"
    "--add-opens=java.base/java.math=ALL-UNNAMED"
    "--add-opens=java.base/java.net=ALL-UNNAMED"
    "--add-opens=java.base/java.nio=ALL-UNNAMED"
    "--add-opens=java.base/java.security=ALL-UNNAMED"
    "--add-opens=java.base/java.text=ALL-UNNAMED"
    "--add-opens=java.base/java.time=ALL-UNNAMED"
    "--add-opens=java.base/java.util=ALL-UNNAMED"
    "--add-opens=java.base/jdk.internal.access=ALL-UNNAMED"
    "--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED"
    "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED"
    "--add-opens=java.base/sun.reflect.annotation=ALL-UNNAMED"
    "-jar"
    "`"$jarFile`""
)

Write-Host "Starting Java application..."
Write-Host "Command: java $javaArgs"

try {
    # 执行 Java 命令
    & java $javaArgs
}
catch {
    Write-Error "Error running Java application: $_"
    exit 1
}
