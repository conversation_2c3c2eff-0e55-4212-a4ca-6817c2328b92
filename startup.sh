#!/bin/bash

# 默认参数
jarFileName="sptalentrvapi.jar"
profile="native"

# 处理命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --jar=*)
      jarFileName="${1#*=}"
      shift
      ;;
    --profile=*)
      profile="${1#*=}"
      shift
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# 设置UTF-8编码
export LANG=en_US.UTF-8

# 获取脚本所在目录的绝对路径
scriptPath="$(cd "$(dirname "$0")" && pwd)"
echo "Script path: $scriptPath"

# 构建jar文件的完整路径（在target目录下）
targetPath="$scriptPath/target"
jarFile="$targetPath/$jarFileName"

echo "Looking for jar file at: $jarFile"

# 检查jar文件是否存在
if [ ! -f "$jarFile" ]; then
  echo "错误: Jar文件未找到: $jarFile" >&2
  exit 1
else
  echo "Found jar file: $jarFile"
fi

# 构建Java参数
javaArgs=(
  "-Dspring.profiles.active=$profile"
  "-Dnacos.url=***********:8848"
  "-Dnacos.config.url=***********:8848"
  "-Dspring.cloud.nacos.discovery.enabled=false"
  "-Dspring.cloud.nacos.config.enabled=false"
  "-Dnacos.rw.username=readwrite"
  "-Dnacos.rw.password=readwrite"
  "-Dnacos.ro.username=read"
  "-Dnacos.ro.password=read"
  "-Dcom.sun.management.jmxremote"
  "-Dcom.sun.management.jmxremote.port=25000"
  "-Dcom.sun.management.jmxremote.ssl=false"
  "-Dcom.sun.management.jmxremote.authenticate=false"
  "-Xmx1000m"
  "-Xms500m"
  "-Xss512k"
  "-XX:MetaspaceSize=500m"
  "-XX:MaxMetaspaceSize=500m"
  "-XX:ReservedCodeCacheSize=500m"
  "-Dfile.encoding=UTF-8"
  "--add-opens=java.base/java.lang=ALL-UNNAMED"
  "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED"
  "--add-opens=java.base/java.io=ALL-UNNAMED"
  "--add-opens=java.base/java.math=ALL-UNNAMED"
  "--add-opens=java.base/java.net=ALL-UNNAMED"
  "--add-opens=java.base/java.nio=ALL-UNNAMED"
  "--add-opens=java.base/java.security=ALL-UNNAMED"
  "--add-opens=java.base/java.text=ALL-UNNAMED"
  "--add-opens=java.base/java.time=ALL-UNNAMED"
  "--add-opens=java.base/java.util=ALL-UNNAMED"
  "--add-opens=java.base/jdk.internal.access=ALL-UNNAMED"
  "--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED"
  "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED"
  "--add-opens=java.base/sun.reflect.annotation=ALL-UNNAMED"
  "-jar"
  "$jarFile"
)

echo "Starting Java application..."
echo "Command: java ${javaArgs[*]}"

# 执行Java命令
java "${javaArgs[@]}" || {
  echo "错误: 运行Java应用程序时出错" >&2
  exit 1
}
