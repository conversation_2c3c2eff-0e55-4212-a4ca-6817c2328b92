{"java.format.settings.url": ".vscode/java-formatter.xml", "java.completion.importOrder": ["java", "javax", "com.yxt", "org", "com"], "maven.terminal.customEnv": [{"environmentVariable": "MAVEN_OPTS", "value": "-Xms512m -Xmx1024m"}], "editor.tabSize": 4, "editor.fontSize": 12, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": false, "files.encoding": "utf8", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 3000, "debug.console.fontSize": 12, "terminal.integrated.fontSize": 12, "commentTranslate.targetLanguage": "zh-CN", "java.autobuild.enabled": false, "java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "disabled", "chat.editor.fontSize": 12, "markdown.preview.fontSize": 12, "files.eol": "\n", "git.followTagsWhenSync": true, "git.rebaseWhenSync": true, "git.autofetch": true, "git.pullBeforeCheckout": true, "git.enableSmartCommit": true}